/**
 * Styles for configurable categories
 */

/* Fix for categories dropdown in desktop mode */
@media (min-width: 769px) {
    .categories-content {
        display: block !important;
        transform: none !important;
    }

    .categories-content:not(.show) {
        visibility: hidden;
        opacity: 0;
        pointer-events: none;
    }
}

/* Categories list styles */
.categories-list {
    padding-bottom: 20px; /* Add padding to the bottom of the list */
}

.categories-list > li {
    margin-bottom: 0.5rem; /* Add spacing between list items */
}

/* Category item with subcategories */
.category-item {
    position: relative;
}

.category-item > a {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.category-item > a .category-expand {
    margin-left: 0.5rem;
    transition: transform 0.2s;
    font-size: 0.7rem;
    opacity: 0.7;
}

.category-item.expanded > a .category-expand {
    transform: rotate(180deg);
}

/* Subcategories list */
.subcategories {
    display: none;
    list-style: none;
    padding: 0;
    margin: 0.5rem 0 0.5rem 1rem;
    border-left: 1px solid var(--border, #374151);
}

.category-item.expanded .subcategories {
    display: block;
    animation: fadeIn 0.3s ease;
}

.subcategories li {
    margin-bottom: 0.25rem;
}

.subcategories a {
    padding: 0.5rem 1rem;
    font-size: 0.95rem;
    color: var(--text-secondary, #e5e7eb);
    text-decoration: none;
    border-radius: 0.5rem;
    transition: all 0.2s;
    font-weight: 500;
    display: block;
}

.subcategories a:hover {
    background-color: var(--bg-card-hover, #2d3748);
    color: var(--accent, #f97316);
    padding-left: 1.25rem;
}

/* Mobile styles */
@media (max-width: 768px) {
    /* Subcategory styles */
    .subcategories {
        margin-left: 1.5rem;
    }

    .subcategories a {
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }

    /* Fix for categories dropdown to prevent bottom nav from blocking content */
    .categories-content {
        padding-bottom: 70px !important; /* Add padding to account for bottom nav */
    }

    /* Ensure the categories body can scroll and has proper spacing */
    .categories-body {
        max-height: calc(100vh - 130px) !important; /* Account for header and bottom nav */
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
        padding-bottom: 20px !important; /* Extra padding at the bottom */
    }

    /* Add extra padding to the last item to ensure it's not covered by the bottom nav */
    .categories-list > li:last-child {
        margin-bottom: 60px !important;
    }
}

/* Small mobile devices */
@media (max-width: 480px) {
    .categories-content {
        padding-bottom: 66px !important; /* Adjust padding for smaller devices */
    }

    .categories-body {
        max-height: calc(100vh - 116px) !important; /* Adjust for smaller devices */
    }
}

/* Spacer element for bottom navigation */
.categories-spacer {
    height: 60px;
    min-height: 60px;
    background: transparent;
    border: none;
    margin: 0;
    padding: 0;
    visibility: visible !important;
    display: block !important;
}

@media (max-width: 480px) {
    .categories-spacer {
        height: 56px;
        min-height: 56px;
    }
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Subcategories styling */
.subcategories-list {
    list-style: none;
    padding: 0;
    margin: 0 0 0 1rem;
    border-left: 1px solid var(--border, #374151);
}

.subcategories-list li {
    margin-bottom: 0.25rem;
}

.subcategories-list a {
    display: block;
    padding: 0.5rem 0.75rem;
    color: var(--text-secondary, #e5e7eb);
    text-decoration: none;
    border-radius: 0.5rem;
    transition: all 0.2s;
    font-weight: 400;
    font-size: 0.9em;
}

.subcategories-list a:hover {
    background-color: var(--bg-card-hover, #2d3748);
    color: var(--accent, #f97316);
    padding-left: 1rem;
}

/* Mobile styling for subcategories */
@media (max-width: 768px) {
    .subcategories-list {
        margin-left: 1.5rem;
    }
    
    .subcategories-list a {
        padding: 0.75rem;
        font-size: 1rem;
    }
}
