/* Mobile Header Styles - Using bottom navigation */

/* Desktop navigation buttons */
.desktop-nav-buttons {
  display: flex;
  gap: var(--spacing-4);
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  /* Hide desktop navigation on mobile */
  .desktop-nav-buttons {
    display: none !important;
  }

  /* Adjust main navigation container on mobile */
  .main-nav {
    padding: 0 !important;
    margin: 0 auto !important;
    position: relative !important;
    right: 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: auto !important; /* Changed from 100% to auto */
    max-width: calc(100% - 20px) !important; /* Ensure there's some margin */
  }

  /* Floating action buttons and mobile header buttons have been removed - using bottom navigation instead */

  /* Mobile header button styles removed - using bottom navigation instead */
}

/* Small mobile devices adjustments removed - using bottom navigation instead */

/* Reduced motion preference for mobile header buttons removed - using bottom navigation instead */
