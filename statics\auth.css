/**
 * Authentication Styles
 */

/* Auth links in header */
.auth-links {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-links {
    display: none; /* Hidden by default, will be shown via JS when user is logged in */
    align-items: center;
    gap: 0.5rem;
}

.user-display-name {
    font-weight: 500;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

@media (max-width: 768px) {
    /* Show username on mobile with adjusted styling */
    .user-display-name {
        font-size: 0.85rem;
        max-width: 100px; /* Limit width on mobile */
        margin-right: 0.5rem;
        padding: 0.25rem 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Ensure user links are properly styled */
    .user-links {
        justify-content: flex-end;
        align-items: center;
    }

    /* Style the logout button */
    .user-links .logout-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.9rem;
        background-color: var(--primary);
        color: white !important;
        border-radius: 4px;
        font-weight: 600;
    }
}

/* Small mobile devices */
@media (max-width: 480px) {
    .user-display-name {
        font-size: 0.8rem;
        max-width: 80px; /* Further limit width on small mobile */
        margin-right: 0.3rem;
    }

    .user-links .logout-btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.8rem;
    }
}

/* Auth forms */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--text-secondary);
}

.form-group input {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.auth-button {
    padding: 0.75rem;
    background-color: var(--primary);
    color: white;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
}

.auth-button:hover {
    background-color: var(--primary-dark);
}

.auth-links {
    margin-top: 1rem;
    text-align: center;
    font-size: 0.9rem;
}

.auth-links a {
    color: var(--primary);
    text-decoration: none;
}

.auth-links a:hover {
    text-decoration: underline;
}

.auth-error {
    background-color: rgba(220, 38, 38, 0.1);
    color: rgb(220, 38, 38);
    padding: 0.75rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    display: none;
}

.auth-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: rgb(16, 185, 129);
    padding: 0.75rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    display: none;
}

/* Auth Modal Styles */
.auth-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.auth-modal-backdrop {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7); /* Darker background for better contrast */
    z-index: 999;
    backdrop-filter: blur(3px); /* Add blur effect for modern browsers */
    -webkit-backdrop-filter: blur(3px);
}

.auth-modal-content {
    background-color: var(--bg-secondary);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    width: 100%;
    max-width: 400px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalFadeIn 0.3s ease;
    /* Ensure solid background with no transparency */
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.auth-modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
    color: var(--text-primary);
}

.auth-modal-close {
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    line-height: 1;
}

.auth-modal-close:hover {
    color: var(--text-primary);
}

.auth-modal-body {
    padding: 1.5rem;
}

/* Forgot password specific styles */
.forgot-password-info {
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
    line-height: 1.5;
    font-size: 0.95rem;
}

#forgot-password-form .auth-button {
    margin-top: 0.5rem;
}

#forgot-password-form .auth-button:disabled {
    background-color: var(--border-color);
    cursor: not-allowed;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    .auth-modal-content {
        max-width: 90%;
        margin: 0 auto;
    }

    /* Improve login/register button alignment on mobile */
    .main-nav .header-auth-links {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.25rem;
        margin: 0 auto; /* Center horizontally */
        position: relative; /* Changed from absolute to relative */
        left: auto; /* Remove absolute positioning */
        transform: none; /* Remove transform */
        background-color: var(--bg-primary);
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        z-index: 10;
        width: auto; /* Allow natural width */
        max-width: 100%; /* Ensure it doesn't overflow */
    }

    /* Add specific styling for login/register links */
    .login-link, .register-link {
        color: var(--primary) !important;
        font-weight: 600 !important;
        padding: 0.25rem 0.35rem !important; /* Slightly reduced padding */
        font-size: 0.9rem !important;
        white-space: nowrap !important;
    }

    /* Style the separator between login and register */
    .auth-separator {
        color: var(--text-secondary);
        font-size: 0.9rem;
        padding: 0 0.1rem;
    }

    .main-nav .auth-links a {
        padding: 0.25rem 0.5rem;
        font-size: 0.9rem;
        white-space: nowrap;
        font-weight: 500;
    }

    /* Ensure buttons are properly spaced */
    .auth-button {
        margin-top: 0.5rem;
        padding: 0.85rem;
        font-size: 1rem;
    }

    /* Fixed header buttons have been removed - using bottom navigation instead */
}
