/**
 * Enhanced Player Loading Animation
 * Provides a better loading experience for video players
 */

// Utility for logging - only logs in development environments
const logUtils = (function() {
  // Check if we're in a development environment
  const isDev = window.location.hostname === 'localhost' || 
                window.location.hostname === '127.0.0.1' ||
                window.location.hostname.includes('.local');
  
  // Return logging utility functions
  return {
    log: isDev ? console.log.bind(console) : function() {},
    error: isDev ? console.error.bind(console) : function() {}
  };
})();

// Run the initialization function as soon as possible
(function() {
  // Initialize player loading animation on script load
  initPlayerLoading();
  
  // Also run after DOM content loaded to ensure it works in both scenarios
  document.addEventListener('DOMContentLoaded', function() {
    initPlayerLoading();
  });

  // Add a listener for the ArtPlayer ready event to ensure we remove the loading indicator
  window.addEventListener('artplayerReady', function() {
    removeLoadingAnimation();
  });
})();

/**
 * Initialize player loading animation
 */
function initPlayerLoading() {
  // Find player wrapper and placeholder
  const playerWrapper = document.getElementById('player-wrapper');
  const playerPlaceholder = document.querySelector('.player-placeholder');

  if (!playerWrapper) {
    logUtils.error('Player wrapper not found');
    return;
  }

  // Check if player is already loaded
  if (isPlayerLoaded()) {
    // Player already loaded, no need for animation
    logUtils.log('Player already loaded, skipping loading animation');
    return;
  }

  // Create placeholder if it doesn't exist
  let placeholder = playerPlaceholder;
  if (!placeholder) {
    logUtils.log('Creating player placeholder as it was not found');
    placeholder = document.createElement('div');
    placeholder.className = 'player-placeholder';
    playerWrapper.appendChild(placeholder);
  }
  
  // Clear any existing static loading animation before creating the enhanced one
  placeholder.innerHTML = '';

  // Get video information
  const vodId = playerWrapper.getAttribute('data-vod-id') || '';
  const posterUrl = document.querySelector('meta[name="video:poster"]')?.getAttribute('content') || '';
  const videoTitle = document.querySelector('meta[name="video:title"]')?.getAttribute('content') ||
                    document.querySelector('title')?.textContent || '视频加载中';

  // Create enhanced loading UI
  createEnhancedLoadingUI(placeholder, {
    poster: posterUrl,
    title: videoTitle,
    vodId: vodId
  });

  // Start loading progress simulation
  simulateLoadingProgress();

  // Check for player load timeout
  startLoadingTimeout();
  
  logUtils.log('Player loading animation initialized successfully');
  
  // Set up a mutation observer to detect when ArtPlayer is added to the DOM
  setupPlayerDetectionObserver(playerWrapper);
}

/**
 * Setup a mutation observer to detect when the player is added to the DOM
 * @param {HTMLElement} playerWrapper - The player wrapper element
 */
function setupPlayerDetectionObserver(playerWrapper) {
  // Create a new mutation observer
  const observer = new MutationObserver(function(mutations) {
    for (const mutation of mutations) {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        for (const node of mutation.addedNodes) {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if the added element is an ArtPlayer
            if (node.classList.contains('art-video-player') || 
                node.classList.contains('artplayer') ||
                node.querySelector('.art-video-player') ||
                node.querySelector('.artplayer')) {
              logUtils.log('ArtPlayer detected in DOM, removing loading animation');
              removeLoadingAnimation();
              observer.disconnect();
              return;
            }
          }
        }
      }
    }
  });

  // Start observing the player wrapper for changes
  observer.observe(playerWrapper, { childList: true, subtree: true });
}

/**
 * Check if the player is already loaded
 * @returns {boolean} True if the player is loaded
 */
function isPlayerLoaded() {
  return !!(document.querySelector('.dplayer') ||
    document.querySelector('media-player') ||
    document.querySelector('.art-video-player') ||
    document.querySelector('.artplayer') ||
    document.querySelector('#player-wrapper iframe'));
}

/**
 * Remove the loading animation
 */
function removeLoadingAnimation() {
  const placeholder = document.querySelector('.player-placeholder');
  const loadingContainer = document.querySelector('.player-loading-container');
  
  if (placeholder) {
    placeholder.style.display = 'none';
    placeholder.style.opacity = '0';
    placeholder.style.visibility = 'hidden';
  }
  
  if (loadingContainer) {
    loadingContainer.style.display = 'none';
    loadingContainer.style.opacity = '0';
    loadingContainer.style.visibility = 'hidden';
  }
  
  logUtils.log('Loading animation removed successfully');
}

/**
 * Create enhanced loading UI
 * @param {HTMLElement} container - The container element
 * @param {Object} options - Options for the loading UI
 */
function createEnhancedLoadingUI(container, options = {}) {
  // Clear existing content
  container.innerHTML = '';

  // Create a new loading container element
  const loadingContainer = document.createElement('div');
  loadingContainer.className = 'player-loading-container';
  
  // Define critical inline styles for spinner animation
  const spinnerInlineStyles = `
    width: 48px !important;
    height: 48px !important; 
    border-radius: 50% !important;
    position: relative !important;
    margin-bottom: 16px !important;
    border: 4px solid rgba(255, 255, 255, 0.1) !important;
    border-top-color: #f97316 !important;
    animation: spin 1s linear infinite !important;
  `;
  
  // Define critical inline styles for text
  const textInlineStyles = `
    font-size: 16px !important;
    font-weight: 500 !important;
    color: #f9fafb !important;
    margin-bottom: 8px !important;
    text-align: center !important;
    font-family: system-ui, -apple-system, sans-serif !important;
  `;
  
  // Create loading HTML with inline styles
  loadingContainer.innerHTML = `
    ${options.poster ? `<img src="${options.poster}" alt="" class="player-loading-poster" aria-hidden="true" style="position: absolute !important; top: 0 !important; left: 0 !important; width: 100% !important; height: 100% !important; object-fit: cover !important; opacity: 0.7 !important;">` : ''}
    <div class="player-loading-overlay" style="position: absolute !important; top: 0 !important; left: 0 !important; width: 100% !important; height: 100% !important; display: flex !important; flex-direction: column !important; align-items: center !important; justify-content: center !important; background-color: rgba(17, 24, 39, 0.7) !important; backdrop-filter: blur(4px) !important; -webkit-backdrop-filter: blur(4px) !important; z-index: 10 !important;">
      <div class="player-loading-spinner" role="progressbar" aria-label="加载中" style="${spinnerInlineStyles}"></div>
      <div class="player-loading-text" style="${textInlineStyles}">${options.title || '视频加载中'}</div>
      <div class="player-loading-progress" style="width: 80% !important; max-width: 240px !important; height: 4px !important; background-color: rgba(255, 255, 255, 0.1) !important; border-radius: 2px !important; overflow: hidden !important; margin-bottom: 16px !important;">
        <div class="player-loading-progress-bar" style="height: 100% !important; width: 30% !important; background-color: #f97316 !important; border-radius: 2px !important; position: relative !important; animation: player-progress-indeterminate 1.5s infinite ease-in-out !important;"></div>
      </div>
      <div class="player-loading-message" style="font-size: 14px !important; color: #e5e7eb !important; text-align: center !important; max-width: 80% !important; line-height: 1.4 !important;">正在加载视频资源，请稍候...</div>
      <div class="player-loading-error" style="display: none; color: #ef4444 !important; font-size: 14px !important; margin-top: 8px !important; text-align: center !important; max-width: 80% !important;">加载超时，请检查网络连接</div>
      <button class="player-loading-retry" style="display: none; margin-top: 16px !important; padding: 8px 16px !important; background-color: #f97316 !important; color: white !important; border: none !important; border-radius: 4px !important; font-size: 14px !important; font-weight: 500 !important; cursor: pointer !important;" aria-label="重试">重新加载</button>
    </div>
    <div class="player-loading-controls" style="position: absolute !important; bottom: 0 !important; left: 0 !important; width: 100% !important; height: 48px !important; background-color: rgba(0, 0, 0, 0.5) !important; display: flex !important; align-items: center !important; padding: 0 16px !important;">
      <div class="player-loading-control" style="width: 32px !important; height: 32px !important; border-radius: 50% !important; background-color: rgba(255, 255, 255, 0.2) !important; margin-right: 12px !important; position: relative !important; overflow: hidden !important;">
        <div class="player-loading-shimmer" style="position: absolute !important; top: 0 !important; left: 0 !important; width: 100% !important; height: 100% !important; background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.08), transparent) !important;"></div>
      </div>
      <div class="player-loading-control" style="width: 32px !important; height: 32px !important; border-radius: 50% !important; background-color: rgba(255, 255, 255, 0.2) !important; margin-right: 12px !important; position: relative !important; overflow: hidden !important;">
        <div class="player-loading-shimmer" style="position: absolute !important; top: 0 !important; left: 0 !important; width: 100% !important; height: 100% !important; background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.08), transparent) !important;"></div>
      </div>
      <div class="player-loading-progress-bar-skeleton" style="flex: 1 !important; height: 4px !important; background-color: rgba(255, 255, 255, 0.2) !important; border-radius: 2px !important; margin: 0 12px !important; position: relative !important; overflow: hidden !important;">
        <div class="player-loading-shimmer" style="position: absolute !important; top: 0 !important; left: 0 !important; width: 100% !important; height: 100% !important; background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.08), transparent) !important;"></div>
      </div>
      <div class="player-loading-control" style="width: 32px !important; height: 32px !important; border-radius: 50% !important; background-color: rgba(255, 255, 255, 0.2) !important; margin-right: 12px !important; position: relative !important; overflow: hidden !important;">
        <div class="player-loading-shimmer" style="position: absolute !important; top: 0 !important; left: 0 !important; width: 100% !important; height: 100% !important; background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.08), transparent) !important;"></div>
      </div>
    </div>
    <span class="sr-only" id="loading-status" style="position: absolute !important; width: 1px !important; height: 1px !important; padding: 0 !important; margin: -1px !important; overflow: hidden !important; clip: rect(0, 0, 0, 0) !important; white-space: nowrap !important; border-width: 0 !important;">视频正在加载中，请稍候</span>
  `;

  // Append the loading container to the container
  container.appendChild(loadingContainer);

  // Force layout recalculation to ensure styles are applied
  void container.offsetWidth;

  // Add inline styles to ensure visibility
  container.style.display = 'block';
  container.style.opacity = '1';
  container.style.visibility = 'visible';
  loadingContainer.style.display = 'block';
  loadingContainer.style.opacity = '1';
  loadingContainer.style.visibility = 'visible';

  // Ensure spinner animation works by adding a style element if it doesn't exist already
  if (!document.getElementById('spinner-animation-style')) {
    const style = document.createElement('style');
    style.id = 'spinner-animation-style';
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      @keyframes player-progress-indeterminate {
        0% { left: -30%; }
        100% { left: 100%; }
      }
    `;
    document.head.appendChild(style);
  }

  // Add event listener to retry button
  const retryButton = loadingContainer.querySelector('.player-loading-retry');
  if (retryButton) {
    retryButton.addEventListener('click', function() {
      // Reload the page
      window.location.reload();
    });
  }
  
  logUtils.log('Enhanced loading UI created successfully');
}

/**
 * Simulate loading progress
 */
function simulateLoadingProgress() {
  // Get progress elements
  const progressBar = document.querySelector('.player-loading-progress-bar');
  const loadingMessage = document.querySelector('.player-loading-message');
  const loadingStatus = document.getElementById('loading-status');

  if (!progressBar || !loadingMessage || !loadingStatus) {
    logUtils.error('Progress elements not found');
    return;
  }

  // Define loading stages
  const loadingStages = [
    { message: '正在连接视频服务器...', time: 1000 },
    { message: '正在获取视频信息...', time: 2000 },
    { message: '正在加载视频资源...', time: 3000 },
    { message: '正在准备播放器...', time: 4000 },
    { message: '即将开始播放...', time: 5000 }
  ];

  // Update loading message and status
  let currentStage = 0;

  const updateLoadingStage = () => {
    if (currentStage >= loadingStages.length) return;

    // Check if player is loaded before updating message
    if (isPlayerLoaded()) {
      removeLoadingAnimation();
      return;
    }

    // Update message
    const stage = loadingStages[currentStage];
    loadingMessage.textContent = stage.message;
    loadingStatus.textContent = stage.message;

    // Move to next stage
    currentStage++;

    // Schedule next update if player hasn't loaded yet
    if (!isPlayerLoaded() && currentStage < loadingStages.length) {
      setTimeout(updateLoadingStage, stage.time);
    }
  };

  // Start updating loading stages
  updateLoadingStage();
  
  logUtils.log('Loading progress simulation started');
}

/**
 * Start loading timeout
 */
function startLoadingTimeout() {
  // Set timeout for player loading
  setTimeout(() => {
    // Check if player has loaded
    if (!isPlayerLoaded()) {
      // Player has not loaded within timeout, show error
      logUtils.log('Player loading timeout, showing error message');
      
      const loadingMessage = document.querySelector('.player-loading-message');
      const loadingError = document.querySelector('.player-loading-error');
      const retryButton = document.querySelector('.player-loading-retry');
      const loadingStatus = document.getElementById('loading-status');
      
      if (loadingMessage) loadingMessage.style.display = 'none';
      if (loadingError) loadingError.style.display = 'block';
      if (retryButton) retryButton.style.display = 'block';
      if (loadingStatus) loadingStatus.textContent = '加载超时，请检查网络连接或重试';
      
      // Set timeout for another check
      setTimeout(() => {
        if (isPlayerLoaded()) {
          removeLoadingAnimation();
        }
      }, 5000);
    }
  }, 25000); // 25 seconds timeout
}

/**
 * Show player loading error
 * @param {string} message - Error message to display
 */
window.showPlayerLoadingError = function(message) {
  const loadingMessage = document.querySelector('.player-loading-message');
  const loadingError = document.querySelector('.player-loading-error');
  const retryButton = document.querySelector('.player-loading-retry');
  const loadingStatus = document.getElementById('loading-status');
  
  if (loadingMessage) loadingMessage.style.display = 'none';
  if (loadingError) {
    loadingError.textContent = message;
    loadingError.style.display = 'block';
  }
  if (retryButton) retryButton.style.display = 'block';
  if (loadingStatus) loadingStatus.textContent = message;
};

/**
 * Check device capabilities to optimize loading animation
 */
function checkDeviceCapabilities() {
  // Check if we're on a low-end device
  const isLowEndDevice = 
    navigator.deviceMemory < 4 || // Less than 4GB RAM
    navigator.hardwareConcurrency < 4; // Less than 4 cores
  
  // Add a class to the document body if this is a low-end device
  if (isLowEndDevice) {
    document.documentElement.classList.add('low-end-device');
    logUtils.log('Low-end device detected, using simplified animations');
  }
}

// Check device capabilities when script loads
checkDeviceCapabilities();
