/* Bottom Navigation Bar for Mobile */

/* Hide on desktop - with !important to ensure it's not overridden */
.bottom-nav {
  display: none !important;
}

@media (max-width: 768px) {
  /* Fixed header buttons have been removed - using bottom navigation instead */

  /* Bottom navigation bar */
  .bottom-nav {
    display: flex !important; /* Override the desktop display:none with !important */
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background-color: var(--bg-dark);
    border-top: 1px solid var(--border);
    z-index: 9999; /* Extremely high z-index to ensure it's above everything */
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
    padding-bottom: env(safe-area-inset-bottom, 0); /* For iOS devices with home indicator */
  }

  /* Button container */
  .bottom-nav-buttons {
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: space-around;
    align-items: center;
  }

  /* Individual button */
  .bottom-nav-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    height: 100%;
    color: var(--text-tertiary);
    background: transparent;
    border: none;
    padding: 8px 0;
    transition: color 0.2s ease;
    cursor: pointer;
    position: relative;
    z-index: 10000; /* Ensure buttons are above any overlapping elements */
    pointer-events: auto !important; /* Force pointer events to be enabled */
  }

  /* Button icon */
  .bottom-nav-btn-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 4px;
  }

  /* Button label */
  .bottom-nav-btn-label {
    font-size: 12px;
    font-weight: 500;
    text-align: center;
  }

  /* Active button */
  .bottom-nav-btn.active {
    color: var(--primary);
  }

  /* Active indicator */
  .bottom-nav-btn.active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 3px;
    background-color: var(--primary);
    border-radius: 0 0 3px 3px;
  }

  /* Add padding to main content to account for bottom nav */
  main.main-content {
    padding-bottom: 70px !important;
  }
  
  /* Specific padding for search results page to ensure proper scrolling */
  .search-results-page main.main-content {
    padding-bottom: 70px !important;
    min-height: calc(100vh - 70px) !important; /* Ensure the page takes up full height */
  }

  /* Add padding to footer to account for bottom nav */
  footer.container {
    padding-bottom: 70px !important;
  }

  /* Adjust fixed elements that might be covered by the bottom nav */
  .history-content,
  .favorites-content,
  .categories-content {
    max-height: calc(100vh - 130px) !important; /* Account for header and bottom nav */
    bottom: 60px !important; /* Position above bottom nav */
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .bottom-nav {
    height: 56px;
  }

  .bottom-nav-btn-icon svg {
    width: 20px;
    height: 20px;
  }

  .bottom-nav-btn-label {
    font-size: 10px;
  }

  main.main-content {
    padding-bottom: 66px !important;
  }
  
  /* Specific padding adjustment for search results page on small devices */
  .search-results-page main.main-content {
    padding-bottom: 66px !important;
    min-height: calc(100vh - 66px) !important;
  }

  footer.container {
    padding-bottom: 66px !important;
  }

  .history-content,
  .favorites-content,
  .categories-content {
    max-height: calc(100vh - 116px) !important;
    bottom: 56px !important;
  }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .bottom-nav-btn {
    transition: none;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .bottom-nav {
    background-color: var(--bg-dark, #111827);
    border-top-color: var(--border, #374151);
  }
}
