/**
 * CSS Design System Variables
 * Centralized design tokens for consistent theming
 * Version: 2.0 - Refactored and optimized
 */

:root {
  /* === COLOR PALETTE === */

  /* Primary Colors */
  --primary: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --primary-hover: #1e40af;

  /* Secondary Colors */
  --secondary: #10b981;
  --secondary-dark: #059669;
  --secondary-light: #34d399;
  --secondary-hover: #047857;

  /* Accent Colors */
  --accent: #f97316;
  --accent-dark: #ea580c;
  --accent-light: #fb923c;
  --accent-hover: #c2410c;

  /* Status Colors */
  --success: #22c55e;
  --warning: #eab308;
  --error: #ef4444;
  --info: #3b82f6;

  /* === NEUTRAL COLORS === */

  /* Background Colors */
  --bg-dark: #111827;
  --bg-darker: #0f172a;
  --bg-card: #1f2937;
  --bg-card-hover: #2d3748;
  --bg-overlay: rgba(0, 0, 0, 0.8);

  /* Text Colors */
  --text-primary: #f9fafb;
  --text-secondary: #e5e7eb;
  --text-tertiary: #9ca3af;
  --text-muted: #6b7280;
  --text-inverse: #111827;

  /* Border Colors */
  --border: #374151;
  --border-light: #4b5563;
  --border-lighter: #6b7280;
  --border-focus: var(--primary);

  /* === SHADOWS === */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* === TYPOGRAPHY === */
  --font-family-primary: 'Inter', 'Roboto', 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Arial', sans-serif;
  --font-family-mono: 'Fira Code', 'Monaco', 'Consolas', 'Courier New', monospace;

  /* Font Sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */

  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* === SPACING === */
  --spacing-0: 0;
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */
  --spacing-20: 5rem;     /* 80px */
  --spacing-24: 6rem;     /* 96px */
  --spacing-32: 8rem;     /* 128px */

  /* === BORDER RADIUS === */
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius: 0.25rem;       /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;

  /* === TRANSITIONS === */
  --transition-fast: 150ms;
  --transition: 200ms;
  --transition-slow: 300ms;
  --transition-slower: 500ms;

  /* Easing Functions */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);

  /* === Z-INDEX SCALE === */
  --z-base: 0;
  --z-dropdown: 10;
  --z-sticky: 20;
  --z-fixed: 30;
  --z-modal-backdrop: 40;
  --z-modal: 50;
  --z-popover: 60;
  --z-tooltip: 70;
  --z-toast: 80;
  --z-max: 9999;

  /* === LAYOUT === */

  /* Breakpoints */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* Grid System */
  --grid-columns: 12;
  --grid-gap: var(--spacing-4);
  --grid-gap-sm: var(--spacing-2);
  --grid-gap-lg: var(--spacing-6);

  /* Container */
  --container-max-width: 1200px;
  --container-padding: var(--spacing-4);
  --container-padding-sm: var(--spacing-2);
  --container-padding-lg: var(--spacing-6);

  /* === COMPONENT DIMENSIONS === */

  /* Header & Navigation */
  --header-height: 4rem;
  --header-height-collapsed: 3rem;
  --nav-item-height: 2.5rem;
  --bottom-nav-height: 4rem;

  /* Sidebar */
  --sidebar-width: 16rem;
  --sidebar-width-collapsed: 4rem;

  /* Footer */
  --footer-height: 3rem;

  /* Cards & Media */
  --card-min-width: 140px;
  --card-aspect-ratio: 3/4;
  --media-card-border-radius: var(--radius-lg);

  /* === ANIMATION DURATIONS === */
  --duration-instant: 0ms;
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --duration-slower: 750ms;
  --duration-slowest: 1000ms;

  /* === ACCESSIBILITY === */

  /* Focus indicators */
  --focus-ring-width: 2px;
  --focus-ring-offset: 2px;
  --focus-ring-color: var(--primary);

  /* Touch targets */
  --touch-target-min: 44px;

  /* === RESPONSIVE UTILITIES === */

  /* Mobile-first approach */
  --mobile-padding: var(--spacing-4);
  --tablet-padding: var(--spacing-6);
  --desktop-padding: var(--spacing-8);
}

/* === MEDIA QUERY MIXINS === */

/* Dark mode support (already dark by default) */
@media (prefers-color-scheme: light) {
  :root {
    /* Light mode overrides if needed */
    --bg-dark: #ffffff;
    --bg-card: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #4b5563;
    --text-tertiary: #6b7280;
    --border: #e5e7eb;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  :root {
    --border: currentColor;
    --shadow: none;
    --shadow-sm: none;
    --shadow-md: none;
    --shadow-lg: none;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: 0ms;
    --transition: 0ms;
    --transition-slow: 0ms;
    --transition-slower: 0ms;
    --duration-fast: 0ms;
    --duration-normal: 0ms;
    --duration-slow: 0ms;
    --duration-slower: 0ms;
    --duration-slowest: 0ms;
  }
}