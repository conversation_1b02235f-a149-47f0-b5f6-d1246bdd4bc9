{| extend("../base.html") |}

{| block head_title |}{| include("incl_detail_head.html") |}{| end block head_title |}
{| block head_preload |}{| end block head_preload |}

{| block head |}
<!-- Add meta tags for video information -->
<meta name="video:title" content="{{ ctx.vod.title }} {{ ctx.vod.sources[ctx.ch].urls[ctx.ep].title }}">
<meta name="video:poster" content="{= ctx.vod.poster =}">
<!-- Add optimized player loading styles -->
<link rel="stylesheet" href="/statics/player-loading.css">
<!-- Add VOD player alignment CSS -->
<link rel="stylesheet" href="/statics/vod-player-alignment.css">
<!-- Add mobile navigation styles -->
<link rel="stylesheet" href="/statics/vod-nav-mobile.css">
<!-- Critical inline styles for player loading -->
<style>
  /* Critical loading styles to prevent FOUC */
  .player-placeholder {
    width: 100% !important;
    height: auto !important;
    aspect-ratio: 16/9 !important;
    background-color: #1f2937 !important;
    position: relative !important;
    display: block !important;
    border-radius: 8px !important;
    overflow: hidden !important;
  }
  
  .player-loading-container {
    width: 100% !important;
    height: 0 !important;
    padding-bottom: 56.25% !important;
    position: relative !important;
    background-color: #1f2937 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    display: block !important;
  }
  
  .player-loading-overlay {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: rgba(17, 24, 39, 0.7) !important;
  }
  
  .player-loading-spinner {
    width: 48px !important;
    height: 48px !important;
    border-radius: 50% !important;
    position: relative !important;
    margin-bottom: 16px !important;
  }

  /* Critical styles for loading text */
  .player-loading-text,
  .player-loading-overlay div:nth-child(2),
  div.player-loading-text {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: #f9fafb !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    margin-bottom: 8px !important;
    text-align: center !important;
    font-family: system-ui, -apple-system, sans-serif !important;
    line-height: 1.4 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
    pointer-events: none !important;
    z-index: 100 !important;
    background: transparent !important;
    max-width: 90% !important;
  }
  
  @keyframes player-spinner-rotate {
    to { transform: rotate(360deg); }
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>

<!-- Early player loader script -->
<script>
  // Ensure basic loading animation is shown immediately
  window.addEventListener('DOMContentLoaded', function() {
    const playerPlaceholder = document.querySelector('.player-placeholder');
    if (!playerPlaceholder) return;
    
    // Check if enhanced loading is already created
    if (playerPlaceholder.querySelector('.player-loading-container')) return;
    
    // Get video information
    const videoTitle = document.querySelector('meta[name="video:title"]')?.getAttribute('content') || 
                      document.querySelector('title')?.textContent || '视频加载中';
    const posterUrl = document.querySelector('meta[name="video:poster"]')?.getAttribute('content') || '';
    
    // Add spinner animation style if needed
    if (!document.getElementById('spinner-animation-style')) {
      const style = document.createElement('style');
      style.id = 'spinner-animation-style';
      style.textContent = `
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `;
      document.head.appendChild(style);
    }
    
    // Create enhanced loading UI
    const loadingContainer = document.createElement('div');
    loadingContainer.className = 'player-loading-container';
    loadingContainer.style.width = '100%';
    loadingContainer.style.height = '0';
    loadingContainer.style.paddingBottom = '56.25%';
    loadingContainer.style.position = 'relative';
    loadingContainer.style.backgroundColor = '#1f2937';
    loadingContainer.style.borderRadius = '8px';
    loadingContainer.style.overflow = 'hidden';
    loadingContainer.style.display = 'block';
    
    // Set loading container content with inline styles
    loadingContainer.innerHTML = `
      ${posterUrl ? `<img src="${posterUrl}" alt="" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover; opacity: 0.7;" aria-hidden="true">` : ''}
      <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; background-color: rgba(17, 24, 39, 0.7); z-index: 10;">
        <div role="progressbar" aria-label="加载中" style="width: 48px; height: 48px; border-radius: 50%; border: 4px solid rgba(255, 255, 255, 0.1); border-top-color: #f97316; animation: spin 1s linear infinite; margin-bottom: 16px;"></div>
        <div style="font-size: 16px; font-weight: 500; color: #f9fafb; margin-bottom: 8px; text-align: center; font-family: system-ui, -apple-system, sans-serif; line-height: 1.4; display: block; width: auto; max-width: 80%; opacity: 1; visibility: visible;">${videoTitle}</div>
        <div style="font-size: 14px; color: #e5e7eb; text-align: center; max-width: 80%; line-height: 1.4;">正在加载播放器，请稍候...</div>
      </div>
    `;
    
    // Add loading container to the placeholder
    playerPlaceholder.innerHTML = '';
    playerPlaceholder.appendChild(loadingContainer);
  });
</script>
{| end block head |}

{| block content |}
<script>
    const loadPlayer = ({ code, src, loader }) => {
        if (src.endsWith('.m3u8') || src.endsWith('.mp4')) {
            return loader({ src })
        }
        // JSON 接口反代地址，比如：https://my-vod-site.com:8080/?json_url=
        const proxy = ''
        const solutions = [
            // ['播放编码', '解析地址', '键名或 null'],
            // `播放编码`就是后台`线路设置`中的`播放编码`，只填写需要解析的线路。
            // 第三个元素如果有值，则通过反代地址向解析地址发送 JSON 请求，取出返回值相应的键值，
            // 如果是 null，则以 iframe 的形式载入解析地址提供的播放器。下面是例子：
            // ['iqiyi', 'https://jiexi.com/?json=', 'url'],
            // ['youku', 'https://jiexi.com/?player=', null],
        ]
        for (const solution of solutions) {
            if (solution[0] == code) {
                if (typeof solution[2] == 'string' && solution[2].length > 0) {
                    const suf = solution[1] + encodeURIComponent(src)
                    const url = proxy + (proxy.includes('?') ? encodeURIComponent(suf) : suf)
                    fetch(url, { headers: { 'Content-Type': 'application/json' }, mode: 'cors' })
                        .then((res) => loader({ src: res[solution[2]] }))
                        .catch((e) => console.log(e))
                } else {
                    return document.getElementById('player-wrapper').innerHTML =
                        `\x3ciframe width="100%" height="100%" border="0" scrolling="no"
src="${solution[1]}${src}" frameborder="0" marginwidth="0" marginheight="0"
allowfullscreen="allowfullscreen" mozallowfullscreen="mozallowfullscreen"
msallowfullscreen="msallowfullscreen" oallowfullscreen="oallowfullscreen"
webkitallowfullscreen="webkitallowfullscreen" security="restricted"
sandbox="allow-same-origin allow-forms allow-scripts"\x3e\x3c/iframe\x3e`
                }
            }
        }
        return loader({ src })
    }
</script>
{[
local path_query = string.format('%s?%s', func.uri.path(ctx.com.urls.current), func.uri.query(ctx.com.urls.current))
]}
<script>
    const rateKey = 'vodg-playback-rate'
    if (!localStorage.getItem(rateKey)) {
        localStorage.setItem(rateKey, 1)
    }
    
    // Remove playerKey and setPlayer function since we only use ArtPlayer
    
    // Set default player to artplayer
    localStorage.setItem('vodg-player-name', 'artplayer')
    
    const cacheKey = 'vodg-play'
    const vodId = '{{ ctx.vod.id }}'
    const vodEp = '{{ ctx.ep }}'
    const vodCh = '{{ ctx.ch }}'
    const getPlayCache = () => {
        let cache = localStorage.getItem(cacheKey)
        if (cache && typeof cache == 'string') {
            try {
                cache = JSON.parse(cache)
            } catch {
                cache = {}
            }
        } else {
            cache = {}
        }
        if (!cache[vodId] || typeof cache[vodId] != 'object') {
            cache[vodId] = {}
        }
        if (!cache[vodId][vodEp] || typeof cache[vodId][vodEp] != 'object') {
            cache[vodId][vodEp] = {}
        }
        return cache
    }
    const getPlaybackCache = () => {
        const cache = getPlayCache()[vodId][vodEp]
        if (!cache.playProgressRate) {
            cache.playProgressRate = 0
        }
        return cache
    }
    let playbackTimeUpdated = Date.now()
    const updatePlayProgressRate = (currTime, duration) => {
        const now = Date.now()
        if (playbackTimeUpdated + 1000 < now && 2 < currTime) {
            playbackTimeUpdated = now
            const playProgressRate = currTime / duration
            if (0 < playProgressRate && playProgressRate <= 1) {
                const cache = getPlayCache()
                cache[vodId][vodEp].playProgressRate = playProgressRate
                cache[vodId][vodEp].updated = now
                cache[vodId][vodEp].ch = vodCh // Store channel information
                localStorage.setItem(cacheKey, JSON.stringify(cache))

                // Update lastWatched timestamp in favorites and history
                try {
                    // Update favorites
                    const favorites = JSON.parse(localStorage.getItem('vodg-favorites')) || [];
                    const favoriteIndex = favorites.findIndex(fav => fav && fav.id === vodId);

                    if (favoriteIndex !== -1) {
                        // Update the lastWatched timestamp for sorting
                        favorites[favoriteIndex].lastWatched = now;
                        // Also update timestamp for Firebase sync
                        favorites[favoriteIndex].timestamp = now;

                        // Sort favorites by lastWatched (most recent first)
                        favorites.sort((a, b) => {
                            return ((b.lastWatched || 0) - (a.lastWatched || 0));
                        });

                        // Save the updated favorites
                        localStorage.setItem('vodg-favorites', JSON.stringify(favorites));
                    }

                    // Update history items with the same lastWatched timestamp
                    const history = JSON.parse(localStorage.getItem('vodg-vod-history')) || [];
                    const historyIndex = history.findIndex(h => h && h.id === vodId);

                    if (historyIndex !== -1) {
                        // Update both timestamps for consistency
                        history[historyIndex].lastWatched = now;
                        history[historyIndex].timestamp = now;

                        // Sort history by lastWatched (most recent first)
                        history.sort((a, b) => {
                            return ((b.lastWatched || 0) - (a.lastWatched || 0));
                        });

                        // Save the updated history
                        localStorage.setItem('vodg-vod-history', JSON.stringify(history));
                    }
                } catch (e) {
                    console.error('Error updating lastWatched timestamps:', e);
                }

                // If user is logged in, sync to Firebase only when significant progress is made
                // This reduces Firebase writes while still ensuring progress is saved
                if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
                    const userId = window.getCurrentUserId();
                    if (userId && window.syncManager) {
                        // Sync every 10 seconds while the video is playing
                        // Use the updated timestamp to determine when to sync
                        const lastSyncTime = cache[vodId][vodEp].lastSyncTime || 0;
                        if (now - lastSyncTime >= 10000) { // 10 seconds interval
                            console.log(`Syncing playback progress at ${Math.round(playProgressRate * 100)}%`);
                            // Update the last sync time
                            cache[vodId][vodEp].lastSyncTime = now;
                            localStorage.setItem(cacheKey, JSON.stringify(cache));
                            
                            if (typeof window.syncManager.debouncedSync === 'function') {
                                window.syncManager.debouncedSync('watchProgress');
                            } else {
                                window.syncManager.syncToFirestore(userId, 'watchProgress');
                            }
                        }
                    }
                } else {
                    // If not logged in, just dispatch the event
                    window.dispatchEvent(new CustomEvent('watchProgressUpdated'));
                }

                // Real-time updates for both history and favorites
                // Update history dropdown if it exists and is open
                if (typeof window.loadHistoryItems === 'function') {
                    const historyDropdown = document.getElementById('history-dropdown');
                    if (historyDropdown && historyDropdown.classList.contains('show')) {
                        window.loadHistoryItems();
                    }
                }

                // Update favorites dropdown if it exists and is open
                if (typeof window.loadFavoritesItems === 'function') {
                    const favoritesDropdown = document.getElementById('favorites-dropdown');
                    if (favoritesDropdown && favoritesDropdown.classList.contains('show')) {
                        window.loadFavoritesItems();
                    }
                }
            }
        }
    }
    const removePlayProgressRate = () => {
        const cache = getPlayCache()
        delete cache[vodId][vodEp]
        if (Object.keys(cache[vodId]).length == 0) {
            delete cache[vodId]
        }
        localStorage.setItem(cacheKey, JSON.stringify(cache))

        // If user is logged in, sync directly to Firebase
        // Progress removal is a significant change that should be synced
        if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
            const userId = window.getCurrentUserId();
            if (userId && window.syncManager) {
                console.log('Syncing progress removal');
                if (typeof window.syncManager.debouncedSync === 'function') {
                    window.syncManager.debouncedSync('watchProgress');
                } else {
                    window.syncManager.syncToFirestore(userId, 'watchProgress');
                }
            }
        } else {
            // If not logged in, just dispatch the event
            window.dispatchEvent(new CustomEvent('watchProgressUpdated'));
        }
    }
    const getSeekTime = (duration) =>
        Math.max(Math.min(Math.max(getPlaybackCache().playProgressRate, 0), 1) * duration - 2, 0)
    const tryFx = (f) => (x) => { try { f(x) } catch (e) { console.log(e) } }
    tryFx(() => {
        const now = Date.now()
        if (now / 1000 % 10 < 1) {
            const cache = getPlayCache()
            for (const vid in cache) {
                for (const vep in cache[vid]) {
                    if (cache[vid][vep].updated < now - 86400000 * 92) {
                        delete cache[vid][vep]
                    }
                }
                if (Object.keys(cache[vid]).length == 0) {
                    delete cache[vid]
                }
            }
            localStorage.setItem(cacheKey, JSON.stringify(cache))
        }
    })()
    const m3u8 = async (url) => {
        if (!url.endsWith('.m3u8')) {
            return url
        }
        const indexUrl = url
        let retry = 2
        while (retry > 0) {
            try {
                const txt = await (await fetch(url)).text()
                let res = await fetch('/vod/api/m3u8/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `url=${encodeURIComponent(url)}&content=${encodeURIComponent(txt)}&index_url=${encodeURIComponent(indexUrl)}`
                })
                res = await res.json()
                if (res.code == 200) {
                    if (res.data.master.length == 0) {
                        return `/vod/api/m3u8/?url=${encodeURIComponent(indexUrl)}`
                    } else {
                        url = res.data.master
                        continue
                    }
                }
                break
            } catch (e) {
                console.log(e)
                retry--
            }
        }
        // Return the original URL if all retries failed
        return url
    }
    const gotoNextEp = () => document.getElementById('next-ep') && document.getElementById('next-ep').click()

    // Add event listener to sync watch progress before page unloads
    window.addEventListener('beforeunload', () => {
        // If user is logged in, force sync watch progress to Firebase
        if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
            const userId = window.getCurrentUserId();
            if (userId && window.syncManager && typeof window.syncManager.forceWatchProgressSync === 'function') {
                // Use a synchronous approach since we're in beforeunload
                const cache = getPlayCache();
                if (cache[vodId] && cache[vodId][vodEp]) {
                    // Update the lastSyncTime to prevent duplicate syncs
                    cache[vodId][vodEp].lastSyncTime = Date.now();
                    localStorage.setItem(cacheKey, JSON.stringify(cache));
                    
                    console.log('Forcing final watch progress sync before page unload');
                    window.syncManager.forceWatchProgressSync(userId);
                }
            }
        }
    });
</script>
<!-- Player wrapper -->
<div id="player-wrapper" data-vod-id="{= ctx.vod.id =}">
    <!-- Enhanced player placeholder with optimized loading animation -->
    <div class="player-placeholder" style="width: 100%; height: auto; aspect-ratio: 16/9; background-color: #1f2937; position: relative; display: block;">
        <!-- Initial loading state before JavaScript loads -->
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: white;">
            <div style="width: 48px; height: 48px; border: 4px solid rgba(255, 255, 255, 0.1); border-radius: 50%; border-top-color: #f97316; margin: 0 auto 16px; animation: spin 1s linear infinite;"></div>
            <div style="font-size: 16px; font-weight: 500;">{{ ctx.vod.title }} {{ ctx.vod.sources[ctx.ch].urls[ctx.ep].title }}</div>
        </div>
    </div>
</div>
<div class="vod-title-bar">
    <h2 style="margin-bottom: 0;">
        <a href="/vod/detail/?id={= ctx.vod.id =}">{{ ctx.vod.title }}</a>
        {{ ctx.vod.sources[ctx.ch].urls[ctx.ep].title }}
    </h2>
    <div class="vod-prev-next">
        {[ if ctx.ep > 1 then ]}
        <a href="/vod/play/?id={= ctx.vod.id =}&ch={= ctx.ch =}&ep={= ctx.ep - 1 =}{[ if ctx.player.name ~= '' then ]}&ply={= ctx.player.name =}{[ else ]}&ply=artplayer{[ end ]}"
            rel="nofollow">上一集</a>
        {[ end ]}
        {[ if ctx.vod.sources[ctx.ch].urls[ctx.ep + 1] then ]}
        <a href="/vod/play/?id={= ctx.vod.id =}&ch={= ctx.ch =}&ep={= ctx.ep + 1 =}{[ if ctx.player.name ~= '' then ]}&ply={= ctx.player.name =}{[ else ]}&ply=artplayer{[ end ]}"
            rel="nofollow">下一集</a>
        {[ end ]}
    </div>
</div>
<div class="tabs">
    {| include("incl_detail_episodes.html", { include_detail_box = true, include_more_vods = true }) |}
</div>
<style>
    .tabs .vod-chan-curr {
        background-color: #23272e;
        color: #fff;
    }

    .tabs .vod-chan-curr sup {
        color: #fc9;
    }

    /* Fix for channel tabs in desktop view */
    @media screen and (min-width: 769px) {
        .tab-buttons-wrapper {
            overflow-x: auto;
            padding-bottom: 5px;
        }

        .tab-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            white-space: normal;
        }

        .tab-button {
            margin-bottom: 0.5rem;
        }
    }
</style>
{| end block content |}

{| block script |}
<!-- Load player loading script -->
<script src="/statics/player-loading.js"></script>
<script>
    // Always use ArtPlayer
    localStorage.setItem('vodg-player-name', 'artplayer');
</script>

<!-- ArtPlayer -->
<script src="https://cdn.jsdelivr.net/npm/artplayer/dist/artplayer.js"></script>
<script src="https://cdn.jsdelivr.net/npm/hls.js/dist/hls.min.js"></script>
<script>
    const loader = ({ src }) => document.write(
        `\x3cscript type="module"\x3e
    await (async () => {
        let src;
        try {
            src = await m3u8('${src}')
            if (!src) {
                // m3u8 function returned empty URL
                src = '${src}'; // Fallback to original URL
            }
        } catch (error) {
            // Error in m3u8 function
            src = '${src}'; // Fallback to original URL
        }
        document.getElementById('player-wrapper').innerHTML = ''
        // Determine if we need to use the m3u8 custom type
        const type = src.endsWith('.m3u8') ? 'customHls' : 'auto';

        const art = new Artplayer({
            container: '#player-wrapper',
            url: src,
            type: type,
            title: '{{ ctx.vod.title }} {{ ctx.vod.sources[ctx.ch].urls[ctx.ep].title }}',
            poster: '{= ctx.vod.poster =}',
            posterScaling: 'contain',
            volume: 0.8,
            isLive: false,
            muted: false,
            autoplay: false,
            pip: true,
            autoSize: true,
            autoMini: true,
            screenshot: true,
            setting: true,
            loop: false,
            flip: true,
            playbackRate: true,
            aspectRatio: true,
            fullscreen: true,
            fullscreenWeb: false,
            subtitleOffset: true,
            miniProgressBar: true,
            mutex: true,
            backdrop: true,
            playsInline: true,
            autoPlayback: false,
            airplay: true,
            theme: '#23ade5',
            lang: navigator.language.toLowerCase(),
            moreVideoAttr: {
              crossOrigin: 'anonymous',
            },
            customType: {
              customHls: function(video, url) {
                if (Hls.isSupported()) {
                  const hls = new Hls({
                    maxBufferLength: 60,
                    maxMaxBufferLength: 90,
                    maxBufferSize: 100 * 1000 * 1000,
                    maxBufferHole: 1,
                    lowLatencyMode: false,
                    manifestLoadingMaxRetry: 6,
                    levelLoadingMaxRetry: 6,
                    fragLoadingMaxRetry: 6,
                    fragLoadingMaxRetryTimeout: 10000
                  });

                  hls.loadSource(url);
                  hls.attachMedia(video);

                  hls.on(Hls.Events.ERROR, function(event, data) {
                    if (data.fatal) {
                      switch (data.type) {
                        case Hls.ErrorTypes.NETWORK_ERROR:
                          // HLS network error
                          hls.startLoad();
                          break;
                        case Hls.ErrorTypes.MEDIA_ERROR:
                          // HLS media error
                          hls.recoverMediaError();
                          break;
                        default:
                          // HLS fatal error
                          break;
                      }
                    }
                  });
                } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                  video.src = url;
                }
              }
            }
        });

        window.art = art;

        // Add error handling
        art.on('error', (error) => {
            if (typeof window.showPlayerLoadingError === 'function') {
                window.showPlayerLoadingError('播放器加载失败: ' + (error.message || '未知错误'));
            }
        });

        // Fix for Chrome video display issue
        art.on('ready', () => {
            // Force video element to be visible
            const video = art.template.$video;
            if (video) {
                video.style.display = 'block';
                video.style.visibility = 'visible';
                video.style.opacity = '1';
                video.style.zIndex = '2';

                // Force hardware acceleration
                video.style.transform = 'translateZ(0)';
                video.style.backfaceVisibility = 'hidden';

                // Ensure poster doesn't block video
                const poster = art.template.$poster;
                if (poster) {
                    poster.style.zIndex = '1';
                }
            }
            
            // Dispatch event to signal that ArtPlayer is ready
            window.dispatchEvent(new CustomEvent('artplayerReady'));
            
            // Also directly remove the loading animation
            const playerPlaceholder = document.querySelector('.player-placeholder');
            if (playerPlaceholder) {
                playerPlaceholder.style.display = 'none';
                playerPlaceholder.style.opacity = '0';
                playerPlaceholder.style.visibility = 'hidden';
            }
            
            // Remove loading container if it exists
            const loadingContainer = document.querySelector('.player-loading-container');
            if (loadingContainer) {
                loadingContainer.style.display = 'none';
                loadingContainer.style.opacity = '0';
                loadingContainer.style.visibility = 'hidden';
            }
        });

        // Fix for Chrome - ensure video is visible when playing
        art.on('play', () => {
            const video = art.template.$video;
            if (video) {
                // Force redraw of video element
                video.style.display = 'none';
                setTimeout(() => {
                    video.style.display = 'block';
                    video.style.visibility = 'visible';
                    video.style.opacity = '1';
                }, 50);
            }
        });

        art.on('video:ended', tryFx(() => {
            removePlayProgressRate()
            gotoNextEp()
        }))

        art.on('video:timeupdate', tryFx(() => {
            updatePlayProgressRate(art.currentTime, art.duration)
        }))

        const playbackRate = Number(localStorage.getItem(rateKey))
        let seeked = false

        art.on('video:durationchange', tryFx(() => {
            if (!seeked && 0 < art.duration) {
                seeked = true
                // Fix for TypeError: art.seek is not a function
                // In newer versions of ArtPlayer, seek is accessed through currentTime
                art.currentTime = getSeekTime(art.duration)
                if (0 < playbackRate && playbackRate < 5) {
                    art.playbackRate = playbackRate
                }
            }
        }))

        art.on('video:ratechange', tryFx(() => {
            if (0 < art.playbackRate) {
                localStorage.setItem(rateKey, art.playbackRate)
            }
        }))
    })();
\x3c/script\x3e`)
    loadPlayer({
        code: '{{ ctx.ch }}',
        src: '{= ctx.vod.sources[ctx.ch].urls[ctx.ep].url =}',
        loader,
    });
</script>
<style>
    #player-wrapper {
        width: 100%;
        height: 250px;
        background: #000;
    }
    @media screen and (min-width: 600px) {
        #player-wrapper {
            height: 450px;
        }
    }
    /* Make all players fill the wrapper */
    .art-video-player, .art-video-player video {
        width: 100% !important;
        height: 100% !important;
        min-height: 150px !important;
        max-height: 100vh !important;
        background: #000 !important;
        object-fit: contain !important;
        position: relative !important;
        display: block !important;
    }

    /* Fix for Chrome-specific issues */
    @media screen and (-webkit-min-device-pixel-ratio: 0) {
        .art-video-player video {
            transform: translateZ(0) !important;
            will-change: transform !important;
        }

        /* Force visibility of video element */
        .art-video-player.art-playing video {
            visibility: visible !important;
            opacity: 1 !important;
        }
    }
</style>
<script>
    (() => {
        const hist = JSON.parse(localStorage.getItem('vodg-vod-history'))
        // Get current player from context or localStorage
        const currentPlayer = '{= ctx.player.name =}' || localStorage.getItem('vodg-player-name') || 'artplayer'
        const now = Date.now();
        const item = {
            id: '{= ctx.vod.id =}',
            url: '/vod/play/?id={= ctx.vod.id =}&ch={= ctx.ch =}&ep={= ctx.ep =}&ply=' + currentPlayer,
            title: '{{ ctx.vod.title }}',
            poster: '{= ctx.vod.poster =}',
            episode: '{= ctx.ep =}',
            timestamp: now, // Add timestamp for sorting
            lastWatched: now // Add lastWatched for consistency with favorites
        }
        if (hist) {
            for (let i = 0; i < hist.length; i++) {
                if (item.id == hist[i].id) {
                    if (hist[i]) {
                        if (item.id == hist[i].id) {
                            hist.splice(i, 1)
                        }
                    } else {
                        hist.splice(i, 1)
                    }
                }
            }
            hist.unshift(item)
            localStorage.setItem('vodg-vod-history', JSON.stringify(hist))

            // If user is logged in, sync directly to Firebase
            if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
                const userId = window.getCurrentUserId();
                if (userId && window.syncManager) {
                    console.log('Syncing viewing history on page load');
                    window.syncManager.debouncedSync('viewingHistory');
                }
            } else {
                // If not logged in, just dispatch the event
                window.dispatchEvent(new CustomEvent('viewingHistoryUpdated'));
            }
        } else {
            localStorage.setItem('vodg-vod-history', JSON.stringify([item]))

            // If user is logged in, sync directly to Firebase
            if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
                const userId = window.getCurrentUserId();
                if (userId && window.syncManager) {
                    console.log('Syncing initial viewing history');
                    window.syncManager.debouncedSync('viewingHistory');
                }
            } else {
                // If not logged in, just dispatch the event
                window.dispatchEvent(new CustomEvent('viewingHistoryUpdated'));
            }
        }

        // Update favorites with latest episode info from history and sort by most recent
        if (typeof window.updateFavoritesFromHistory === 'function') {
            window.updateFavoritesFromHistory();

            // Also update the lastWatched timestamp for the current item in favorites
            try {
                const favorites = JSON.parse(localStorage.getItem('vodg-favorites')) || [];
                const favoriteIndex = favorites.findIndex(fav => fav && fav.id === item.id);

                if (favoriteIndex !== -1) {
                    // Update the lastWatched timestamp for sorting
                    favorites[favoriteIndex].lastWatched = Date.now();

                    // Sort favorites by lastWatched (most recent first)
                    favorites.sort((a, b) => {
                        return ((b.lastWatched || 0) - (a.lastWatched || 0));
                    });

                    // Save the updated favorites
                    localStorage.setItem('vodg-favorites', JSON.stringify(favorites));

                    // Sync favorites on meaningful updates
                    if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
                        const userId = window.getCurrentUserId();
                        if (userId && window.syncManager) {
                            console.log('Syncing favorites update');
                            window.syncManager.debouncedSync('favorites');
                        }
                    } else {
                        // Dispatch event for UI updates
                        window.dispatchEvent(new CustomEvent('favoritesUpdated'));
                    }
                }
            } catch (e) {
                console.error('Error updating lastWatched timestamp for favorite:', e);
            }
        }

        // Set up UI update handlers for dropdowns if they are opened
        // This avoids periodic polling and only updates when the user actually opens the dropdowns
        document.addEventListener('click', function(event) {
            // Check if user clicked on or in a dropdown toggle button
            const isHistoryToggle = event.target.closest('.history-button');
            const isFavoritesToggle = event.target.closest('.favorites-button');
            
            // If history dropdown was toggled, update the history list
            if (isHistoryToggle && typeof window.loadHistoryItems === 'function') {
                // Small timeout to ensure the dropdown is fully opened
                setTimeout(() => {
                    window.loadHistoryItems();
                }, 50);
            }
            
            // If favorites dropdown was toggled, update the favorites list
            if (isFavoritesToggle && typeof window.loadFavoritesItems === 'function') {
                // Small timeout to ensure the dropdown is fully opened
                setTimeout(() => {
                    window.loadFavoritesItems();
                }, 50);
            }
        });
    })();
</script>
{| end block script |}

{| block style |}
<style>
    /* Additional styles specific to this page */
    @media screen and (min-width: 600px) {
        #player-wrapper {
            height: 450px;
        }
    }

    .chan-attention {
        color: #f60;
    }

    /* Player button styles */
    .player-controls button {
        padding: 0.5rem 1rem;
        margin-right: 0.5rem;
        border-radius: 0.25rem;
        background-color: var(--bg-card, #23272e);
        border: 1px solid var(--border, #444);
        color: var(--text-primary, #eee);
        cursor: pointer;
        transition: background-color 0.2s, border-color 0.2s;
    }

    .player-controls button:hover {
        background-color: var(--bg-card-hover, #2c3038);
        border-color: var(--primary, #23ade5);
    }

    .vod-prev-next a {
        padding: 0.25em 0.5em;
        border: 1px solid #444;
        margin-right: 0.5em;
        border-radius: 0.25rem;
        text-decoration: none;
        transition: background-color 0.2s;
    }

    .vod-prev-next a:hover {
        background-color: var(--bg-card-hover, #2c3038);
    }
    
    /* Mobile-specific styles for episode navigation */
    @media (max-width: 768px) {
        .vod-title-bar {
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .vod-prev-next {
            display: flex;
            width: 100%;
            justify-content: space-between;
            margin-top: 0.5rem;
        }
        
        .vod-prev-next a {
            padding: 0.5em 1em;
            margin: 0;
            flex: 1;
            text-align: center;
            font-weight: 500;
            font-size: 1.1rem;
            max-width: 48%;
            background-color: var(--bg-card, #23272e);
        }
        
        .vod-prev-next a:first-child {
            margin-right: 0.25rem;
        }
        
        .vod-prev-next a:last-child {
            margin-left: 0.25rem;
        }
    }
</style>
{| end block style |}

<style>
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
