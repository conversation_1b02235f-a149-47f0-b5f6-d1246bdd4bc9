/* Custom confirmation dialog styles */
.confirm-dialog-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.confirm-dialog-backdrop.active {
  opacity: 1;
  visibility: visible;
}

.confirm-dialog {
  background-color: var(--bg-card);
  border-radius: 8px;
  padding: 20px;
  width: 90%;
  max-width: 320px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.confirm-dialog-backdrop.active .confirm-dialog {
  transform: translateY(0);
}

.confirm-dialog-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-primary);
}

.confirm-dialog-message {
  font-size: 16px;
  margin-bottom: 20px;
  color: var(--text-secondary);
}

.confirm-dialog-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.confirm-dialog-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: none;
}

.confirm-dialog-cancel {
  background-color: var(--bg-dark);
  color: var(--text-secondary);
  border: 1px solid var(--border);
}

.confirm-dialog-confirm {
  background-color: var(--primary);
  color: white;
}

.confirm-dialog-cancel:hover {
  background-color: var(--bg-card-hover);
}

.confirm-dialog-confirm:hover {
  background-color: var(--primary-dark);
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .confirm-dialog-backdrop,
  .confirm-dialog {
    transition: none;
  }
}
