/**
 * Mobile-specific styles for video player navigation
 * Improves the position and appearance of previous/next episode navigation on mobile devices
 */

/* VOD Mobile Navigation Styles
 * These styles enhance the mobile navigation experience for VOD pages
 * using a cleaner approach with properly scoped selectors instead of !important
 */

.vod-mobile-nav-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.vod-mobile-nav-title {
  width: 100%;
  text-align: center;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.vod-mobile-nav-buttons {
  display: flex;
  width: 100%;
  justify-content: space-between;
  margin-top: 0.5rem;
}

.vod-mobile-nav-btn {
  padding: 0.75em 1em;
  margin: 0;
  flex: 1;
  text-align: center;
  font-weight: 500;
  font-size: 1.1rem;
  max-width: 48%;
  background-color: var(--bg-card, #23272e);
  color: var(--text-primary, #f9fafb);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border, #444);
  border-radius: 0.5rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.vod-mobile-nav-btn-prev .icon {
  margin-right: 0.5rem;
}

.vod-mobile-nav-btn-next .icon {
  margin-left: 0.5rem;
}

.vod-mobile-nav-btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Bottom area adjustments */
.vod-content.mobile {
  padding-bottom: 0;
  margin-bottom: 1rem;
}

.vod-mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(17, 24, 39, 0.9);
  padding: 0.75rem 1rem;
  z-index: 100;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
}

.vod-mobile-bottom-nav .vod-mobile-nav-btn {
  border-radius: 0.75rem;
  font-size: 1rem;
  padding: 0.75em 0.5em;
}

/* Page content fix to avoid content being hidden behind fixed nav */
.vod-page.mobile {
  padding-bottom: 5rem;
}

/* Media queries for improved responsiveness */
@media (max-width: 480px) {
  .vod-mobile-nav-btn {
    font-size: 0.9rem;
    padding: 0.6em 0.8em;
  }
}

@media (max-width: 360px) {
  .vod-mobile-nav-btn {
    font-size: 0.8rem;
    padding: 0.5em 0.6em;
  }
}

/* Legacy selectors for backward compatibility */
@media (max-width: 768px) {
  .vod-title-bar {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    margin-bottom: 1.5rem;
    width: 100%;
    padding: 0 1rem;
  }
  
  .vod-title-bar h2 {
    margin: 0;
    font-size: 1.1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 60%;
  }
  
  .vod-prev-next {
    display: flex;
    gap: 0.5rem;
    margin-left: auto;
  }
  
  .vod-prev-next a {
    padding: 0.5em 0.8em;
    margin: 0;
    text-align: center;
    font-weight: 500;
    font-size: 0.9rem;
    background-color: var(--bg-card, #23272e);
    color: var(--text-primary, #f9fafb);
    border: 1px solid var(--border, #444);
    border-radius: var(--radius);
    text-decoration: none;
    transition: all var(--transition) var(--transition-ease);
    white-space: nowrap;
  }
  
  .vod-prev-next a:first-child {
    margin-right: 0;
  }
  
  .vod-prev-next a:last-child {
    margin-left: 0;
  }
  
  .vod-prev-next a:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  @media (max-width: 480px) {
    .vod-title-bar {
      padding-bottom: 0;
      margin-bottom: 1rem;
    }
    
    .vod-prev-next {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: rgba(17, 24, 39, 0.9);
      padding: 0.75rem 1rem;
      z-index: 100;
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
    }
    
    .vod-prev-next a {
      border-radius: 0.75rem;
      font-size: 1rem;
      padding: 0.75em 0.5em;
    }
    
    .tabs {
      padding-bottom: 5rem;
    }
  }
} 