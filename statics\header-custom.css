/* Custom Header Styles - Optimized for Performance */

/* Desktop Header - Keep Sticky */
@media (min-width: 769px) {
  .site-header {
    position: sticky;
    top: 0;
    z-index: 10;
    padding: var(--spacing-4) 0;
    transition: transform 0.3s ease, padding 0.3s ease, box-shadow 0.3s ease;
    will-change: transform, padding, box-shadow; /* Hint for browser optimization */
    contain: layout style; /* Contain layout and style changes for better performance */
  }

  .site-header.header-collapsed {
    padding: var(--spacing-2) 0;
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
  }

  .site-logo {
    font-size: var(--font-size-xl);
    font-weight: 700;
    transition: transform 0.3s ease;
    will-change: transform; /* Hint for browser optimization */
  }

  .site-header.header-collapsed .site-logo {
    transform: scale(0.9);
    transform-origin: left center;
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .site-header {
      transition: none;
    }

    .site-logo {
      transition: none;
    }

    .site-header.header-collapsed .site-logo {
      transform: none;
    }
  }

  /* Class-based reduced motion support */
  .site-header.reduced-motion {
    transition: none;
  }

  .site-header.reduced-motion .site-logo {
    transition: none;
  }

  .site-header.reduced-motion.header-collapsed .site-logo {
    transform: none;
  }
}

/* Mobile Header - Sticky */
@media (max-width: 768px) {
  .site-header {
    position: sticky; /* Make sticky on mobile */
    top: 0;
    padding: var(--spacing-8) var(--spacing-4) var(--spacing-8) var(--spacing-3); /* Further increased vertical padding */
    box-shadow: var(--shadow-md);
    z-index: 100; /* Higher z-index to ensure it stays above other elements */
    background-color: var(--bg-dark);
  }

  .header-container {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-right: 0 !important; /* Remove right padding completely */
    position: relative !important;
  }

  .site-logo {
    text-align: left;
    margin-bottom: 0;
    font-size: var(--font-size-xl);
    font-weight: 700;
  }

  .main-nav {
    justify-content: flex-end;
    gap: var(--spacing-3);
    position: absolute !important;
    right: 0 !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    padding-right: var(--spacing-3) !important; /* Add padding to ensure elements don't touch the edge */
  }

  /* Mobile header buttons removed - using bottom navigation instead */
}

/* Small Mobile Devices */
@media (max-width: 480px) {
  .site-header {
    padding: var(--spacing-6) var(--spacing-3) var(--spacing-6) var(--spacing-2); /* Further increased vertical padding */
    position: sticky;
    top: 0;
  }

  .header-container {
    padding-right: 0 !important; /* No padding on small devices */
    width: 100% !important;
  }

  .site-logo {
    font-size: var(--font-size-xl);
  }

  .main-nav {
    gap: var(--spacing-2);
    position: absolute !important;
    right: 0 !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
  }

  .nav-link {
    font-size: var(--font-size-sm);
  }

  /* Mobile header buttons removed - using bottom navigation instead */
}
