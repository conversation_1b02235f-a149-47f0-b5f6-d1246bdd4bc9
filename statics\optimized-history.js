﻿/**
 * Optimized History Functionality
 * This file contains optimized code for handling history functionality
 */

// Helper functions for storage and DOM manipulation
function getStorageItem(key, defaultValue) {
  if (typeof Core !== 'undefined' && Core.Storage) {
    return Core.Storage.get(key, defaultValue);
  }
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (e) {
    console.error('Storage get error:', e);
    return defaultValue;
  }
}

function setStorageItem(key, value) {
  if (typeof Core !== 'undefined' && Core.Storage) {
    return Core.Storage.set(key, value);
  }
  try {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (e) {
    console.error('Storage set error:', e);
    return false;
  }
}

function createElement(tag, attributes = {}, children = []) {
  if (typeof Core !== 'undefined' && Core.DOM && Core.DOM.createElement) {
    return Core.DOM.createElement(tag, attributes, children);
  }

  const element = document.createElement(tag);

  // Set attributes
  Object.entries(attributes).forEach(([key, value]) => {
    if (key === 'className') {
      element.className = value;
    } else if (key === 'innerHTML') {
      element.innerHTML = value;
    } else if (key === 'textContent') {
      element.textContent = value;
    } else if (key.startsWith('on') && typeof value === 'function') {
      element[key] = value;
    } else {
      element.setAttribute(key, value);
    }
  });

  // Add children
  if (Array.isArray(children)) {
    children.forEach(child => {
      if (typeof child === 'string') {
        element.appendChild(document.createTextNode(child));
      } else if (child instanceof Node) {
        element.appendChild(child);
      }
    });
  } else if (typeof children === 'string') {
    element.textContent = children;
  }

  return element;
}

function emptyElement(element) {
  if (typeof Core !== 'undefined' && Core.DOM && Core.DOM.emptyElement) {
    return Core.DOM.emptyElement(element);
  }
  while (element.firstChild) {
    element.removeChild(element.firstChild);
  }
}

function removeStorageItem(key) {
  if (typeof Core !== 'undefined' && Core.Storage) {
    return Core.Storage.remove(key);
  }
  try {
    localStorage.removeItem(key);
    return true;
  } catch (e) {
    console.error('Storage remove error:', e);
    return false;
  }
}

document.addEventListener('DOMContentLoaded', function() {
  // Initialize history functionality
  initHistoryDropdown();

  // Load history items on page load
  loadHistoryItems();
});

/**
 * Initialize history dropdown functionality
 */
function initHistoryDropdown() {
  // Add event listeners to tab buttons
  document.querySelectorAll('.history-tab').forEach(tab => {
    tab.addEventListener('click', () => {
      switchHistoryTab(tab.dataset.tab);
    });
  });
}

/**
 * Function to open history dropdown
 */
window.openHistory = function() {
  const historyContent = document.getElementById('history-dropdown');
  const historyButton = document.querySelector('.history-button');
  const historyBackdrop = document.querySelector('.history-backdrop');
  const isMobile = window.innerWidth <= 768;

  // Check if we're using the direct navigation system
  const isUsingDirectNav = typeof window.directToggleHistory === 'function';

  if (!historyContent) return;

  // If we're on mobile and using direct navigation, use that instead
  if (isMobile && isUsingDirectNav) {
    window.directToggleHistory();
    return;
  }

  // For mobile, use our custom fullscreen implementation
  if (isMobile) {
    // Reset any inline styles that might interfere with our CSS
    historyContent.style.top = '';
    historyContent.style.right = '';
    historyContent.style.left = '';
    historyContent.style.margin = '';
    historyContent.style.width = '';
    historyContent.style.maxWidth = '';
    historyContent.style.maxHeight = '';
    historyContent.style.position = 'fixed';

    // Set initial transform for animation
    historyContent.style.transform = 'translateY(100%)';

    // Force a reflow to ensure the initial state is rendered
    void historyContent.offsetWidth;

    // Make sure visibility and z-index are properly set
    historyContent.style.visibility = 'visible';
    historyContent.style.zIndex = '1000'; // Higher z-index for mobile
    historyContent.style.pointerEvents = 'auto'; // Ensure it captures clicks

    // Ensure the history lists are visible
    const viewingListEl = document.querySelector('.viewing-list');
    const searchListEl = document.querySelector('.search-list');

    if (viewingListEl) {
      viewingListEl.style.display = 'block';
      viewingListEl.style.width = '100%';
      viewingListEl.style.visibility = 'visible';
      viewingListEl.style.opacity = '1';
    }

    if (searchListEl) {
      searchListEl.style.display = 'block';
      searchListEl.style.width = '100%';
      searchListEl.style.visibility = 'visible';
      searchListEl.style.opacity = '1';
    }

    // Load history items before animation starts
    if (typeof window.loadHistoryItems === 'function') {
      window.loadHistoryItems();

      // Force another reflow to ensure content is loaded
      void historyContent.offsetWidth;
    }

    // Show the dropdown with a small delay to allow the transform to take effect
    setTimeout(() => {
      // Reset transform to show the dropdown
      historyContent.style.transform = 'translateY(0)';
      historyContent.classList.add('show');
      if (historyBackdrop) {
        historyBackdrop.classList.add('show');
      }

      // Disable scrolling on mobile
      document.body.style.overflow = 'hidden';
    }, 50);
  } else {
    // For desktop, show the dropdown
    historyContent.classList.add('show');
    historyContent.style.visibility = 'visible';
    historyContent.style.zIndex = '100';
    historyContent.style.pointerEvents = 'auto';

    // Load history items
    loadHistoryItems();
  }
};

/**
 * Function to close history dropdown
 */
window.closeHistory = function() {
  const historyContent = document.getElementById('history-dropdown');
  const historyBackdrop = document.querySelector('.history-backdrop');
  const isMobile = window.innerWidth <= 768;

  // Check if we're using the direct navigation system
  const isUsingDirectNav = typeof window.directCloseAll === 'function';

  if (!historyContent) return;

  // If we're on mobile and using direct navigation, use that instead
  if (isMobile && isUsingDirectNav) {
    window.directCloseAll();
    return;
  }

  // For mobile, use our custom fullscreen implementation without animation
  if (isMobile) {
    // No animation - immediately hide
    historyContent.style.transform = '';

    // Hide the history dropdown
    historyContent.classList.remove('show');
    if (historyBackdrop) {
      historyBackdrop.classList.remove('show');
    }

    // Enable scrolling on mobile
    document.body.style.overflow = '';

    // Immediately hide the dropdown without animation
    historyContent.style.display = 'none';
    historyContent.style.visibility = 'hidden';
    historyContent.style.zIndex = '-1';
    historyContent.style.pointerEvents = 'none';
  } else {
    // For desktop, hide the dropdown
    historyContent.classList.remove('show');
    historyContent.style.visibility = 'hidden';
    historyContent.style.zIndex = '-1';
    historyContent.style.pointerEvents = 'none';
  }
};

/**
 * Function to toggle history dropdown
 */
window.toggleHistory = function() {
  const historyContent = document.getElementById('history-dropdown');
  const CLS = 'show';
  const isMobile = window.innerWidth <= 768;

  // Always use direct navigation on mobile to avoid conflicts
  if (isMobile) {
    if (typeof window.directToggleHistory === 'function') {
      window.directToggleHistory();
    }
    return;
  }

  // For desktop only - toggle the visibility
  if (historyContent.classList.contains(CLS)) {
    closeHistory();
  } else {
    openHistory();
  }
};

/**
 * Function to handle tab switching
 * @param {string} tabName - The name of the tab to switch to
 */
window.switchHistoryTab = function(tabName) {
  // Update tab buttons
  document.querySelectorAll('.history-tab').forEach(tab => {
    if (tab.dataset.tab === tabName) {
      tab.classList.add('active');
    } else {
      tab.classList.remove('active');
    }
  });

  // Update tab content
  document.querySelectorAll('.history-tab-content').forEach(content => {
    if (content.id === tabName + '-history') {
      content.classList.add('active');
      content.style.display = 'block';
      content.style.visibility = 'visible';
      content.style.opacity = '1';

      // Ensure the list inside this tab is visible
      const list = content.querySelector('.history-list');
      if (list) {
        list.style.display = 'block';
        list.style.width = '100%';
        list.style.visibility = 'visible';
        list.style.opacity = '1';
      }
    } else {
      content.classList.remove('active');
      content.style.display = 'none';
    }
  });

  // Update footer visibility - we now have multiple elements with the same ID
  const viewingFooters = document.querySelectorAll('#viewing-footer');
  const searchFooters = document.querySelectorAll('#search-footer');
  const favoritesFooters = document.querySelectorAll('.favorites-footer');

  // Hide all footers first
  viewingFooters.forEach(footer => {
    if (footer) footer.style.display = 'none';
  });

  searchFooters.forEach(footer => {
    if (footer) footer.style.display = 'none';
  });

  favoritesFooters.forEach(footer => {
    if (footer) footer.style.display = 'none';
  });

  // Show the appropriate footer
  if (tabName === 'viewing') {
    viewingFooters.forEach(footer => {
      if (footer) footer.style.display = 'flex';
    });
  } else if (tabName === 'search') {
    searchFooters.forEach(footer => {
      if (footer) footer.style.display = 'flex';
    });
  } else if (tabName === 'favorites') {
    // For favorites, we need to find the one inside the body
    const favoritesFooter = document.querySelector('.favorites-body .clear-favorites-button').closest('div');
    if (favoritesFooter) favoritesFooter.style.display = 'flex';
  }

  // If we're switching to favorites tab, load the favorites items
  if (tabName === 'favorites' && typeof loadFavoritesItems === 'function') {
    loadFavoritesItems();
  }

  // Save the active tab preference
  setStorageItem('history-active-tab', tabName);
};

/**
 * Function to load and display history items
 */
window.loadHistoryItems = function() {
  // Get history items from localStorage
  let hist = getStorageItem('vodg-vod-history', []);

  // Filter out null items
  hist = hist.filter((x) => x != null);

  // Separate viewing and search history
  let viewingHistory = hist.filter(item => item && item.id && !item.id.startsWith('search-'));
  let searchHistory = hist.filter(item => item && item.id && item.id.startsWith('search-'));

  // Filter out VOD records without the latest watching episode
  viewingHistory = viewingHistory.filter(item => {
    if (!item || !item.url) return false;

    // Check if the URL contains episode parameter
    const urlParams = new URLSearchParams(item.url.split('?')[1] || '');
    const epParam = urlParams.get('ep');

    // Check if the title contains episode information
    const hasEpisodeInTitle = item.title && item.title.includes('绗?) && item.title.includes('闆?);

    // Check if there's watch progress for this VOD
    let hasWatchProgress = false;
    try {
      const playCache = getStorageItem('vodg-play', {});
      if (playCache[item.id] && Object.keys(playCache[item.id]).length > 0) {
        hasWatchProgress = true;
      }
    } catch (e) {
      console.error('Error checking watch progress:', e);
    }

    // Keep the item if it has episode info in URL, title, or has watch progress
    return epParam || hasEpisodeInTitle || hasWatchProgress;
  });

  // Sort viewing history by lastWatched (most recent first)
  try {
    viewingHistory.sort((a, b) => {
      // First try to use lastWatched property (consistent with favorites)
      const aLastWatched = a.lastWatched || 0;
      const bLastWatched = b.lastWatched || 0;

      // If lastWatched values are different, use them for sorting
      if (aLastWatched !== bLastWatched) {
        return bLastWatched - aLastWatched;
      }

      // Fallback to timestamp if lastWatched is the same
      const aTimestamp = a.timestamp || 0;
      const bTimestamp = b.timestamp || 0;

      if (aTimestamp !== bTimestamp) {
        return bTimestamp - aTimestamp;
      }

      // If both are the same, check for watch progress in play cache
      const playCache = getStorageItem('vodg-play', {});

      // Get last updated time from play cache if available
      const aPlayUpdated = playCache[a.id] && Object.keys(playCache[a.id]).length > 0 ?
        Math.max(...Object.values(playCache[a.id]).map(ep => ep.updated || 0)) : 0;
      const bPlayUpdated = playCache[b.id] && Object.keys(playCache[b.id]).length > 0 ?
        Math.max(...Object.values(playCache[b.id]).map(ep => ep.updated || 0)) : 0;

      // Sort by play cache updated time
      return bPlayUpdated - aPlayUpdated;
    });
  } catch (e) {
    console.error('Error sorting viewing history:', e);
  }

  // Sort search history by lastWatched (most recent first)
  try {
    searchHistory.sort((a, b) => {
      // First try to use lastWatched property (consistent with favorites)
      const aLastWatched = a.lastWatched || 0;
      const bLastWatched = b.lastWatched || 0;

      // If lastWatched values are different, use them for sorting
      if (aLastWatched !== bLastWatched) {
        return bLastWatched - aLastWatched;
      }

      // Fallback to timestamp
      const aTimestamp = a.timestamp || 0;
      const bTimestamp = b.timestamp || 0;
      return bTimestamp - aTimestamp;
    });
  } catch (e) {
    console.error('Error sorting search history:', e);
  }

  // Limit each to 10 items
  viewingHistory.length = Math.min(10, viewingHistory.length);
  searchHistory.length = Math.min(10, searchHistory.length);

  // Ensure the history lists are visible
  const viewingListEl = document.querySelector('.viewing-list');
  const searchListEl = document.querySelector('.search-list');

  if (viewingListEl) {
    viewingListEl.style.display = 'block';
    viewingListEl.style.width = '100%';
    viewingListEl.style.visibility = 'visible';
    viewingListEl.style.opacity = '1';
  }

  if (searchListEl) {
    searchListEl.style.display = 'block';
    searchListEl.style.width = '100%';
    searchListEl.style.visibility = 'visible';
    searchListEl.style.opacity = '1';
  }

  // Update viewing history list
  updateHistoryList(viewingHistory, '.viewing-list', '瑙傜湅');

  // Update search history list
  updateHistoryList(searchHistory, '.search-list', '鎼滅储');

  // Restore active tab
  const activeTab = getStorageItem('history-active-tab', 'viewing');

  // Only switch to favorites tab if it exists in the DOM
  if (activeTab === 'favorites' && !document.querySelector('.history-tab[data-tab="favorites"]')) {
    // If favorites tab doesn't exist yet but was previously active,
    // wait a bit for it to be created by favorites.js
    setTimeout(() => {
      if (document.querySelector('.history-tab[data-tab="favorites"]')) {
        switchHistoryTab('favorites');
      } else {
        switchHistoryTab('viewing'); // Fallback to viewing tab
      }
    }, 100);
  } else {
    switchHistoryTab(activeTab);
  }
};

/**
 * Helper function to update a specific history list
 * @param {Array} items - The history items
 * @param {string} selector - The selector for the list element
 * @param {string} type - The type of history
 */
window.updateHistoryList = function(items, selector, type) {
  const listElement = document.querySelector(selector);
  if (!listElement) return;

  // Create a document fragment for better performance
  const fragment = document.createDocumentFragment();

  if (items.length === 0) {
    // Create empty state
    const emptyItem = createElement('li', { className: 'history-empty' }, [
      createElement('div', { className: 'history-empty-icon' }, type === '鎼滅储' ? '馃攳' : '馃憗锔?),
      createElement('div', { className: 'history-empty-text' }, `鏆傛棤${type}璁板綍`)
    ]);

    fragment.appendChild(emptyItem);
  } else {
    // Create list items
    items.forEach(item => {
      // Ensure the item has all required properties
      if (item && item.url && item.title) {
        if (item.id.startsWith('search-')) {
          // Search history item
          const searchItem = createSearchHistoryItem(item);
          fragment.appendChild(searchItem);
        } else {
          // Viewing history item
          const viewingItem = createViewingHistoryItem(item);
          fragment.appendChild(viewingItem);
        }
      }
    });
  }

  // Clear the list and append the fragment
  emptyElement(listElement);
  listElement.appendChild(fragment);

  // Ensure the list is visible
  listElement.style.display = 'block';
  listElement.style.width = '100%';
  listElement.style.visibility = 'visible';
  listElement.style.opacity = '1';
};

/**
 * Create a search history item
 * @param {Object} item - The search history item
 * @returns {HTMLElement} The search history item element
 */
function createSearchHistoryItem(item) {
  // Create the container
  const container = createElement('li', { className: 'history-item' });

  // Create the inner container
  const innerContainer = createElement('div', { className: 'history-item-container' });

  // Create the link
  const link = createElement('a', {
    href: item.url,
    rel: 'nofollow',
    title: item.title,
    onclick: function(e) {
      // Don't call closeHistory() here - let the browser navigate naturally
      // This allows the link to be clicked multiple times
    }
  }, [
    createElement('span', { className: 'history-item-icon' }, '馃攳'),
    createElement('span', { className: 'history-item-title' }, item.title)
  ]);

  // Create the delete button
  const deleteButton = createElement('button', {
    className: 'history-item-delete',
    onclick: (e) => {
      e.preventDefault();
      e.stopPropagation();
      removeHistoryItem(item.id);
    },
    title: '鍒犻櫎姝よ褰?,
    'aria-label': '鍒犻櫎姝よ褰?
  }, '脳');

  // Assemble the item
  innerContainer.appendChild(link);
  innerContainer.appendChild(deleteButton);
  container.appendChild(innerContainer);

  return container;
}

/**
 * Create a viewing history item
 * @param {Object} item - The viewing history item
 * @returns {HTMLElement} The viewing history item element
 */
function createViewingHistoryItem(item) {
  // Get item details
  const vodId = item.id;
  const urlParams = new URLSearchParams(item.url.split('?')[1] || '');
  const epParam = urlParams.get('ep');
  const chParam = urlParams.get('ch');

  // Default values
  let posterUrl = item.poster || ''; // Will use fallback if empty
  let episodeText = '';
  let progressPercent = 0;

  // Check if this item is in favorites
  let isFavorite = false;
  try {
    const favorites = getStorageItem('vodg-favorites', []);
    isFavorite = favorites.some(fav => fav && fav.id === vodId);
  } catch (e) {
    console.error('Error checking favorites status:', e);
  }

  // Try to extract episode info from URL or title
  if (epParam) {
    episodeText = `绗?${epParam} 闆哷;
  } else if (item.title.includes('绗?) && item.title.includes('闆?)) {
    const match = item.title.match(/绗琝s*(\d+)\s*闆?);
    if (match && match[1]) {
      episodeText = `绗?${match[1]} 闆哷;
    }
  }

  // Try to get watch progress
  try {
    const playCache = getStorageItem('vodg-play', {});
    if (playCache[vodId] && epParam && playCache[vodId][epParam]) {
      // If we have a specific episode parameter and progress for that episode
      progressPercent = Math.round(playCache[vodId][epParam].playProgressRate * 100);

      // If the play cache has channel info and the URL doesn't, update the URL
      if (playCache[vodId][epParam].ch && !chParam) {
        const urlObj = new URL(item.url, window.location.origin);
        urlObj.searchParams.set('ch', playCache[vodId][epParam].ch);
        item.url = urlObj.pathname + urlObj.search;
      }
    } else if (playCache[vodId]) {
      // If no specific episode parameter or no progress for that episode,
      // look for the latest episode with progress (same as favorites.js)
      const episodes = Object.keys(playCache[vodId]);
      if (episodes.length > 0) {
        // Sort episodes by last updated time
        episodes.sort((a, b) => {
          return (playCache[vodId][b].updated || 0) - (playCache[vodId][a].updated || 0);
        });

        const latestEp = episodes[0];
        episodeText = `绗?${latestEp} 闆哷;
        progressPercent = Math.round(playCache[vodId][latestEp].playProgressRate * 100);

        // Update URL to point to the latest episode
        const urlObj = new URL(item.url, window.location.origin);
        urlObj.searchParams.set('ep', latestEp);

        // Use the channel from the play cache if available
        if (playCache[vodId][latestEp].ch) {
          urlObj.searchParams.set('ch', playCache[vodId][latestEp].ch);
        }

        if (urlObj.pathname.includes('/detail/')) {
          urlObj.pathname = urlObj.pathname.replace('/detail/', '/play/');
        }
        item.url = urlObj.pathname + urlObj.search;
      }
    }
  } catch (e) {
    console.error('Error getting watch progress:', e);
  }

  // Format the title (remove episode info if it's in the title)
  let cleanTitle = item.title;
  if (cleanTitle.includes('绗?) && cleanTitle.includes('闆?)) {
    cleanTitle = cleanTitle.replace(/绗琝s*\d+\s*闆?, '').trim();
  }

  // Create the container
  const container = createElement('li', {
    className: `viewing-history-item ${isFavorite ? 'is-favorite' : ''}`
  });

  // Create the inner container
  const innerContainer = createElement('div', {
    className: 'viewing-history-item-container',
    onclick: function(e) {
      // Don't call closeHistory() here - just navigate to the URL
      // This allows the item to be clicked multiple times
      window.location.href = item.url;
    }
  });

  // Create the poster element
  const posterContainer = createElement('div', { className: 'viewing-history-item-poster' });

  if (posterUrl) {
    const posterImg = createElement('img', {
      src: posterUrl,
      alt: cleanTitle,
      onerror: function() {
        this.parentNode.innerHTML = '<div class="viewing-history-item-poster-fallback">馃幀</div>';
      }
    });
    posterContainer.appendChild(posterImg);
  } else {
    posterContainer.appendChild(createElement('div', {
      className: 'viewing-history-item-poster-fallback'
    }, '馃幀'));
  }

  // Add favorite badge if in favorites
  if (isFavorite) {
    posterContainer.appendChild(createElement('div', {
      className: 'viewing-history-item-favorite-badge'
    }, '鈽?));
  }

  // Create the content container
  const contentContainer = createElement('div', { className: 'viewing-history-item-content' });

  // Add title
  contentContainer.appendChild(createElement('span', {
    className: 'viewing-history-item-title'
  }, cleanTitle));

  // Add episode info
  contentContainer.appendChild(createElement('span', {
    className: 'viewing-history-item-episode'
  }, episodeText ? episodeText : '灏氭湭瑙傜湅'));

  // Add progress bar if we have episode info
  if (episodeText) {
    const progressContainer = createElement('div', {
      className: 'viewing-history-item-progress-container'
    });

    progressContainer.appendChild(createElement('div', {
      className: 'viewing-history-item-progress-bar',
      style: { width: `${progressPercent}%` }
    }));

    contentContainer.appendChild(progressContainer);
    contentContainer.appendChild(createElement('span', {
      className: 'viewing-history-item-progress-text'
    }, `${progressPercent}%`));
  }

  // Create the actions container
  const actionsContainer = createElement('div', { className: 'viewing-history-item-actions' });

  // Add favorite toggle button
  const favoriteToggleButton = createElement('button', {
    className: `viewing-history-item-favorite-toggle ${isFavorite ? 'active' : ''}`,
    onclick: (e) => {
      e.preventDefault();
      e.stopPropagation();
      toggleFavoriteFromHistory(vodId, item.url, cleanTitle.replace(/'/g, "\\'"), posterUrl, epParam || '', chParam || '');
    },
    title: isFavorite ? '浠庢敹钘忓す涓垹闄? : '娣诲姞鍒版敹钘忓す',
    'aria-label': isFavorite ? '浠庢敹钘忓す涓垹闄? : '娣诲姞鍒版敹钘忓す'
  }, isFavorite ? '鈽? : '鈽?);

  // Add delete button
  const deleteButton = createElement('button', {
    className: 'viewing-history-item-delete',
    onclick: (e) => {
      e.preventDefault();
      e.stopPropagation();
      removeHistoryItem(item.id);
    },
    title: '鍒犻櫎姝よ褰?,
    'aria-label': '鍒犻櫎姝よ褰?
  }, '脳');

  // Assemble the actions
  actionsContainer.appendChild(favoriteToggleButton);
  actionsContainer.appendChild(deleteButton);

  // Assemble the item
  innerContainer.appendChild(posterContainer);
  innerContainer.appendChild(contentContainer);
  innerContainer.appendChild(actionsContainer);
  container.appendChild(innerContainer);

  return container;
}

/**
 * Function to remove a single history item
 * @param {string} id - The ID of the item to remove
 */
window.removeHistoryItem = function(id) {
  // Get history items from localStorage
  let hist = getStorageItem('vodg-vod-history', []);

  // Find and remove the item with the matching id
  hist = hist.filter(item => item && item.id !== id);

  // Save the updated history
  setStorageItem('vodg-vod-history', hist);

  // Track this item as deleted to prevent it from reappearing during sync
  if (window.syncManager && typeof window.syncManager.addDeletedHistoryItem === 'function') {
    window.syncManager.addDeletedHistoryItem(id);
  }

  // If user is logged in, sync directly to Firebase
  if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
    const userId = window.getCurrentUserId();
    if (userId && window.syncManager) {
      // Determine if this is a viewing history or search history item
      if (id.startsWith('search-')) {
        if (typeof window.syncManager.forceSearchHistorySync === 'function') {
          window.syncManager.forceSearchHistorySync(userId);
        } else {
          window.syncManager.syncToFirestore(userId, 'searchHistory');
        }
      } else {
        if (typeof window.syncManager.forceViewingHistorySync === 'function') {
          window.syncManager.forceViewingHistorySync(userId);
        } else {
          window.syncManager.syncToFirestore(userId, 'viewingHistory');
        }
      }
    }
  } else {
    // If not logged in, just dispatch the event
    if (id.startsWith('search-')) {
      window.dispatchEvent(new CustomEvent('searchHistoryUpdated'));
    } else {
      window.dispatchEvent(new CustomEvent('viewingHistoryUpdated'));
    }
  }

  // Refresh the history list display
  loadHistoryItems();

  // Show feedback message
  showFeedbackMessage('宸插垹闄?);
};

/**
 * Function to clear all history or specific type
 * @param {string} type - The type of history to clear ('all', 'viewing', or 'search')
 */
window.clearHistory = function(type = 'all') {
  // Get history items from localStorage
  let hist = getStorageItem('vodg-vod-history', []);

  if (type === 'all') {
    // Track all items as deleted before clearing
    if (window.syncManager && typeof window.syncManager.addDeletedHistoryItem === 'function') {
      hist.forEach(item => {
        if (item && item.id) {
          window.syncManager.addDeletedHistoryItem(item.id);
        }
      });
    }

    // Clear all history
    removeStorageItem('vodg-vod-history');

    // If user is logged in, sync directly to Firebase
    if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
      const userId = window.getCurrentUserId();
      if (userId && window.syncManager) {
        if (typeof window.syncManager.forceViewingHistorySync === 'function') {
          window.syncManager.forceViewingHistorySync(userId);
        } else {
          window.syncManager.syncToFirestore(userId, 'viewingHistory');
        }

        if (typeof window.syncManager.forceSearchHistorySync === 'function') {
          window.syncManager.forceSearchHistorySync(userId);
        } else {
          window.syncManager.syncToFirestore(userId, 'searchHistory');
        }
      }
    } else {
      // If not logged in, just dispatch the events
      window.dispatchEvent(new CustomEvent('viewingHistoryUpdated'));
      window.dispatchEvent(new CustomEvent('searchHistoryUpdated'));
    }
  } else if (type === 'viewing') {
    // Track viewing history items as deleted
    if (window.syncManager && typeof window.syncManager.addDeletedHistoryItem === 'function') {
      const viewingItems = hist.filter(item => item && item.id && !item.id.startsWith('search-'));
      viewingItems.forEach(item => {
        if (item && item.id) {
          window.syncManager.addDeletedHistoryItem(item.id);
        }
      });
    }

    // Keep only search history
    hist = hist.filter(item => item && item.id && item.id.startsWith('search-'));
    setStorageItem('vodg-vod-history', hist);

    // If user is logged in, sync directly to Firebase
    if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
      const userId = window.getCurrentUserId();
      if (userId && window.syncManager) {
        if (typeof window.syncManager.forceViewingHistorySync === 'function') {
          window.syncManager.forceViewingHistorySync(userId);
        } else {
          window.syncManager.syncToFirestore(userId, 'viewingHistory');
        }
      }
    } else {
      // If not logged in, just dispatch the event
      window.dispatchEvent(new CustomEvent('viewingHistoryUpdated'));
    }
  } else if (type === 'search') {
    // Track search history items as deleted
    if (window.syncManager && typeof window.syncManager.addDeletedHistoryItem === 'function') {
      const searchItems = hist.filter(item => item && item.id && item.id.startsWith('search-'));
      searchItems.forEach(item => {
        if (item && item.id) {
          window.syncManager.addDeletedHistoryItem(item.id);
        }
      });
    }

    // Keep only viewing history
    hist = hist.filter(item => item && item.id && !item.id.startsWith('search-'));
    setStorageItem('vodg-vod-history', hist);

    // If user is logged in, sync directly to Firebase
    if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
      const userId = window.getCurrentUserId();
      if (userId && window.syncManager) {
        if (typeof window.syncManager.forceSearchHistorySync === 'function') {
          window.syncManager.forceSearchHistorySync(userId);
        } else {
          window.syncManager.syncToFirestore(userId, 'searchHistory');
        }
      }
    } else {
      // If not logged in, just dispatch the event
      window.dispatchEvent(new CustomEvent('searchHistoryUpdated'));
    }
  }

  // Refresh the history list display
  loadHistoryItems();

  // Show feedback message on all clear buttons
  const clearButtons = document.querySelectorAll('.clear-history-button');
  clearButtons.forEach(button => {
    const feedbackElem = document.createElement('span');
    feedbackElem.className = 'feedback-message';
    feedbackElem.textContent = '宸叉竻闄?;
    feedbackElem.style.marginLeft = '10px';
    feedbackElem.style.color = '#2ecc71';

    // Remove any existing feedback message
    const existingFeedback = button.querySelector('.feedback-message');
    if (existingFeedback) {
      existingFeedback.remove();
    }

    button.appendChild(feedbackElem);

    // Remove feedback message after 2 seconds
    setTimeout(() => {
      if (feedbackElem.parentNode === button) {
        button.removeChild(feedbackElem);
      }
    }, 2000);
  });
};

/**
 * Show a feedback message in the history dropdown
 * @param {string} message - The message to show
 */
function showFeedbackMessage(message) {
  const historyContent = document.getElementById('history-dropdown');
  if (!historyContent) return;

  // Create feedback element
  const feedbackEl = createElement('div', {
    className: 'history-feedback',
    style: {
      position: 'absolute',
      bottom: '10px',
      left: '50%',
      transform: 'translateX(-50%)',
      backgroundColor: 'var(--secondary)',
      color: 'white',
      padding: '5px 10px',
      borderRadius: '4px',
      fontSize: 'var(--font-size-sm)',
      opacity: '0',
      transition: 'opacity 0.3s ease'
    }
  }, message);

  // Add to the dropdown
  historyContent.appendChild(feedbackEl);

  // Show and then hide the feedback
  setTimeout(() => {
    feedbackEl.style.opacity = '1';
    setTimeout(() => {
      feedbackEl.style.opacity = '0';
      setTimeout(() => {
        if (historyContent.contains(feedbackEl)) {
          historyContent.removeChild(feedbackEl);
        }
      }, 300);
    }, 1500);
  }, 10);
}

