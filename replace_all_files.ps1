# PowerShell script to replace all files with refactored versions
Write-Host "Starting comprehensive file replacement..." -ForegroundColor Green

# Function to safely copy files
function Safe-Copy {
    param($Source, $Destination)
    
    try {
        if (Test-Path $Source) {
            Copy-Item $Source $Destination -Force
            Write-Host "✓ Copied $Source to $Destination" -ForegroundColor Green
        } else {
            Write-Host "✗ Source file not found: $Source" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ Failed to copy $Source to $Destination : $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Replace main template files
Write-Host "`nReplacing main template files..." -ForegroundColor Yellow
Safe-Copy "index-refactored.html" "index.html"

# Replace VOD components
Write-Host "`nReplacing VOD components..." -ForegroundColor Yellow
Safe-Copy "vod\incl_list_refactored.html" "vod\incl_list.html"

# Create templates directory if it doesn't exist
if (!(Test-Path "templates")) {
    New-Item -ItemType Directory -Path "templates" -Force
    Write-Host "✓ Created templates directory" -ForegroundColor Green
}

# Verify critical files exist
Write-Host "`nVerifying critical files..." -ForegroundColor Yellow

$criticalFiles = @(
    "base.html",
    "index.html", 
    "statics\main.css",
    "statics\core.js",
    "statics\navigation.js",
    "statics\css-variables.css",
    "templates\dropdowns.html",
    "templates\dialogs.html",
    "vod\incl_list.html"
)

foreach ($file in $criticalFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file exists" -ForegroundColor Green
    } else {
        Write-Host "✗ $file missing" -ForegroundColor Red
    }
}

Write-Host "`nFile replacement completed!" -ForegroundColor Green
Write-Host "Please test your application to ensure everything works correctly." -ForegroundColor Cyan

# Pause to see results
Read-Host "Press Enter to continue..."
