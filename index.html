{| extend("base.html") |}

{| block top_bar_search_form |}{| end block top_bar_search_form |}

{| block content |}
{[
local total = func.vod.count(0)
local amount = total
if total > 9999 then
amount = string.format('%.1f 万', total / 10000)
end
]}

<!-- Hero Section -->
<section class="hero">
    <h1 class="hero-title">发现精彩影视内容</h1>
    <p class="hero-subtitle">探索最新、最热门的电影和电视剧</p>

    <form class="search-form" style="max-width: 600px; margin: 0 auto var(--spacing-6);" action="/vod/search/" method="GET">
        <select name="field" class="search-select">
            <option value="title">搜标题</option>
            <option value="tag">搜标签</option>
            <option value="staff">搜人员</option>
        </select>
        <input name="kw" id="idx-search-input" class="search-input" placeholder="输入关键词">
        <button type="submit" class="search-button">搜索</button>
    </form>

    <div class="updated" style="color: var(--text-tertiary); margin-bottom: var(--spacing-6);">
        <span>最近更新：{= func.vod.count(86400000) =} &nbsp; 全部：<span title="{= total =}">{= amount =}</span></span>
    </div>
</section>

<!-- Tabs Section -->
<section class="tabs">
    <!-- Tab buttons -->
    <div class="tab-buttons">
        <button class="tab-button active" data-tab="recent">最近更新</button>
        <button class="tab-button" data-tab="douban">豆瓣推荐</button>
    </div>

    <!-- Tab content -->
    <div class="tab-content-container">
        <!-- Recent updates tab (visible by default) -->
        <div class="tab-content active" id="recent-tab">
            {| include("vod/incl_list.html", { vod_list = func.vod.last_updated() }) |}
        </div>

        <!-- Douban recommendations tab (hidden by default) -->
        <div class="tab-content" id="douban-tab">
            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--spacing-6); flex-wrap: wrap;">
                <div class="toggle-container">
                    <div class="toggle-slider"></div>
                    <button id="douban-movie-toggle" class="toggle-button active" aria-pressed="true" role="button">电影</button>
                    <button id="douban-tv-toggle" class="toggle-button" aria-pressed="false" role="button">电视剧</button>
                </div>
            </div>

            <div class="douban-tags-container" style="display: flex; align-items: flex-start; justify-content: space-between; margin-bottom: var(--spacing-4); flex-wrap: wrap;">
                <div id="douban-tags" class="tags-container"></div>
                <button id="douban-refresh" class="tag-button" aria-label="换一批推荐内容">
                    换一批 <span aria-hidden="true">&#x21bb;</span>
                </button>
            </div>

            <div id="douban-results" class="card-grid fixed-grid" style="display: grid !important; grid-template-columns: repeat(auto-fill, minmax(140px, 1fr)) !important; gap: 1rem !important; min-height: 300px;"></div>
        </div>
    </div>
</section>
{| end block content |}

{| block style |}
<style>
    /* Grid styles */
    #douban-results {
        display: grid !important;
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr)) !important;
        gap: 1rem !important;
        min-height: 300px;
        position: relative;
    }

    @media (min-width: 400px) {
        #douban-results {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
        }
    }

    @media (min-width: 600px) {
        #douban-results {
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr)) !important;
        }
    }

    @media (min-width: 800px) {
        #douban-results {
            grid-template-columns: repeat(auto-fill, minmax(170px, 1fr)) !important;
        }
    }

    .fixed-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr)) !important;
        gap: 1rem !important;
    }

    .media-card {
        width: 100% !important;
        margin: 0 !important;
    }

    /* Header and Search */
    .top-bar h2 {
        padding-bottom: 0;
        margin-bottom: 0.5em;
    }

    .idx-search {
        display: flex;
        justify-content: center;
        gap: 0.25em;
        height: 2em;
    }

    .idx-search select,
    #idx-search-input,
    .idx-search-btn {
        background: #23272e;
        color: #eee;
        border: 1px solid #444;
        border-radius: 1em;
        padding: 0 1em;
        transition: border-color 0.2s;
    }

    .idx-search select {
        border-radius: 1em;
    }

    #idx-search-input {
        flex-grow: 1;
        max-width: 30em;
    }

    .idx-search-btn {
        cursor: pointer;
        transition: border-color 0.2s, background 0.2s;
    }

    .idx-search select:focus,
    #idx-search-input:focus,
    .idx-search-btn:hover,
    .idx-search-btn:focus {
        border-color: #6cf;
        outline: none;
    }

    .idx-search-btn:hover,
    .idx-search-btn:focus {
        background: #2a2e35;
        color: #fff;
    }

    .updated {
        display: flex;
        justify-content: space-between;
        margin: 1em 0;
    }

    /* Tab styles */
    .tab-container {
        width: 100%;
        margin-bottom: 1.5rem;
    }

    .tab-buttons {
        display: flex;
        gap: 1rem;
        margin-bottom: 1rem;
        border-bottom: 1px solid #3a3f44;
        padding-bottom: 0.5rem;
        flex-wrap: wrap;
    }

    .tab-button {
        position: relative;
        transition: all 0.3s;
        color: #d1d5db;
        font-size: 1.1rem;
        font-weight: 500;
        padding: 0.5rem 1rem;
        border-radius: 0.25rem 0.25rem 0 0;
        cursor: pointer;
        background-color: rgba(255, 255, 255, 0.05);
        border-bottom: 3px solid transparent;
    }

    .tab-button:hover {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.1);
    }

    .tab-button.active {
        color: #fff;
        background-color: rgba(231, 76, 60, 0.1);
        border-bottom: 3px solid #e74c3c;
        margin-bottom: -1px;
    }

    .tab-content {
        display: none;
        animation: fadeIn 0.5s;
    }

    .tab-content.active {
        display: block;
    }

    /* Toggle styles */
    .toggle-container {
        display: flex;
        background-color: var(--bg-card);
        border-radius: var(--radius);
        padding: 0.25rem;
        position: relative;
        width: fit-content;
        margin-bottom: var(--spacing-4);
        height: 38px;
    }

    .toggle-button,
    #douban-movie-toggle,
    #douban-tv-toggle {
        padding: 0.5rem 1.25rem;
        font-weight: 500;
        transition: color var(--transition) var(--transition-ease);
        border: none;
        outline: none;
        cursor: pointer;
        position: relative;
        z-index: 1;
        background-color: transparent;
        border-radius: var(--radius);
        min-width: 80px;
        text-align: center;
    }

    .toggle-button.active,
    #douban-movie-toggle.active,
    #douban-tv-toggle.active {
        color: white;
    }

    .toggle-button:not(.active),
    #douban-movie-toggle:not(.active),
    #douban-tv-toggle:not(.active) {
        color: var(--text-tertiary);
    }

    .toggle-button:not(.active):hover,
    #douban-movie-toggle:not(.active):hover,
    #douban-tv-toggle:not(.active):hover {
        color: var(--text-secondary);
    }

    .toggle-slider {
        position: absolute;
        top: 0.25rem;
        left: 0.25rem;
        height: calc(100% - 0.5rem);
        width: calc(50% - 0.25rem);
        background-color: var(--primary);
        border-radius: var(--radius);
        transition: transform var(--transition-slow) var(--transition-bounce), left var(--transition-slow) var(--transition-bounce);
        z-index: 0;
    }

    .toggle-slider.right {
        transform: translateX(100%);
    }

    /* Douban tags and refresh styles */
    #douban-refresh {
        padding: 0 1rem;
        border-radius: 0.375rem;
        font-weight: 500;
        transition: all 0.3s ease;
        background-color: #3a3f44;
        border: none;
        outline: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        height: 38px;
        font-size: 0.9rem;
        margin-left: 0.75rem;
        white-space: nowrap;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    #douban-refresh:hover {
        background-color: #4a5568;
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    }

    #douban-refresh:active {
        transform: translateY(0);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    #douban-refresh span {
        display: inline-block;
        margin-left: 0.25rem;
        transition: transform 0.3s ease;
        font-size: 0.9rem;
    }

    #douban-refresh:hover span {
        transform: rotate(90deg);
    }

    /* Tags container */
        .douban-tags-container {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: var(--spacing-4);
        flex-wrap: wrap;
    }

    #douban-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        margin-bottom: 0;
        flex: 1;
    }

    #douban-tags button {
        position: relative;
        overflow: hidden;
        z-index: 1;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;
        border: none;
        outline: none;
        cursor: pointer;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    #douban-tags button::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #e74c3c;
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.3s ease;
        z-index: -1;
    }

    #douban-tags button.active {
        background-color: transparent !important;
        color: white !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    #douban-tags button.active::before {
        transform: scaleX(1);
    }

    #douban-tags button:not(.active) {
        background-color: #2d3748;
        color: #a0aec0;
    }

    #douban-tags button:not(.active):hover {
        background-color: #3a4556;
        color: #e2e8f0;
        transform: translateY(-1px);
    }

    #douban-tags button:not(.active):hover::before {
        transform: scaleX(0.05);
    }

    /* Animations */
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* Responsive styles */
    @media (max-width: 640px) {
        .tab-buttons {
            gap: 0.5rem;
        }

        .tab-button {
            font-size: 1rem;
            padding: 0.4rem 0.8rem;
        }
        
        .douban-tags-container {
            flex-direction: column;
            align-items: stretch;
        }

        #douban-tags {
            margin-bottom: 0.75rem;
        }

        #douban-refresh {
            margin-top: 0.25rem;
            margin-left: 0;
            align-self: flex-end;
            width: auto;
            max-width: 120px;
        }
    }
</style>
{| end block style |}

{| block script |}
<script>
    // Tab switching functionality with smooth animations
    document.addEventListener('DOMContentLoaded', function() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                const tabName = this.getAttribute('data-tab');

                // Don't do anything if the current tab is already active
                if (this.classList.contains('active')) {
                    return;
                }

                // Update tabs UI
                tabButtons.forEach(btn => {
                    btn.classList.remove('active');
                    btn.setAttribute('aria-selected', 'false');
                });
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });

                // Activate current tab
                this.classList.add('active');
                this.setAttribute('aria-selected', 'true');
                document.getElementById(tabName + '-tab').classList.add('active');

                // Initialize Douban tab if needed
                if (tabName === 'douban' && typeof initDoubanTab === 'function') {
                    setTimeout(initDoubanTab, 100);
                }
            });
        });

        // Handle refresh button icon animation
        const refreshButton = document.getElementById('douban-refresh');
        if (refreshButton) {
            refreshButton.addEventListener('mouseover', function() {
                const icon = this.querySelector('span');
                if (icon) icon.style.transform = 'rotate(90deg)';
            });

            refreshButton.addEventListener('mouseout', function() {
                const icon = this.querySelector('span');
                if (icon) icon.style.transform = 'rotate(0deg)';
            });
        }

        // Initialize toggle slider state
        const toggleSlider = document.querySelector('.toggle-slider');
        const tvToggle = document.getElementById('douban-tv-toggle');
        const movieToggle = document.getElementById('douban-movie-toggle');

        if (toggleSlider && tvToggle && movieToggle) {
            toggleSlider.style.transform = tvToggle.classList.contains('active') ? 'translateX(100%)' : 'translateX(0)';
            toggleSlider.classList.toggle('right', tvToggle.classList.contains('active'));
        }
    });
</script>
<script src="/statics/vod/douban_tab_new.js?v=2"></script>
<script src="/statics/optimized-history.js"></script>
{| end block script |}
