/**
 * Enhanced Stylesheet
 * A consolidated and optimized stylesheet that combines
 * critical styles and improves organization
 */

/* Import CSS variables - the single source of truth for design tokens */
@import url('/statics/css-variables.css');

/* Base styles and resets */
*, *::before, *::after {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-dark);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  text-rendering: optimizeSpeed;
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: color var(--transition);
}

a:hover {
  color: var(--primary-light);
}

button {
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Layout components */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.main-container {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  position: relative;
}

.main-content {
  flex: 1;
}

/* Grid layouts */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
  contain: layout style paint;
}

.fixed-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr)) !important;
  gap: 1rem !important;
}

/* Responsive grid adjustments */
@media (min-width: 400px) {
  .card-grid, 
  .fixed-grid,
  #douban-results {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
  }
}

@media (min-width: 600px) {
  .card-grid, 
  .fixed-grid,
  #douban-results {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr)) !important;
  }
}

@media (min-width: 800px) {
  .card-grid, 
  .fixed-grid,
  #douban-results {
    grid-template-columns: repeat(auto-fill, minmax(170px, 1fr)) !important;
  }
}

/* Header components */
.top-bar {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap-reverse;
  position: relative;
}

.top-bar-left, .top-bar-right {
  display: flex;
  align-items: center;
}

.top-bar-left a {
  text-decoration: none;
  color: var(--text-tertiary);
}

.top-bar-right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background-color: var(--bg-card);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius);
  z-index: var(--z-above);
  position: relative;
}

.top-bar-right > div {
  margin-left: var(--spacing-4);
  position: relative;
}

@media screen and (min-width: 601px) {
  .top-bar {
    padding-top: 0;
  }
}

/* Hero section */
.hero {
  text-align: center;
  padding: var(--spacing-8) 0;
  margin-bottom: var(--spacing-8);
}

.hero-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  margin-bottom: var(--spacing-4);
  color: var(--text-primary);
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-6);
  color: var(--text-secondary);
}

/* Search components */
.search-form {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-6);
  width: 100%;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.search-select {
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  background-color: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border);
}

.search-input {
  flex: 1;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  background-color: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border);
}

.search-button {
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  background-color: var(--primary);
  color: white;
  font-weight: 500;
  transition: background-color var(--transition);
}

.search-button:hover {
  background-color: var(--primary-dark);
}

/* Tab components */
.tabs {
  margin-bottom: var(--spacing-8);
}

.tab-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.5rem;
  flex-wrap: wrap;
}

.tab-button {
  position: relative;
  transition: all 0.3s;
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  font-weight: 500;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius) var(--radius) 0 0;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.05);
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  color: var(--text-primary);
  background-color: rgba(255, 255, 255, 0.1);
}

.tab-button.active {
  color: var(--primary);
  border-bottom: 3px solid var(--primary);
}

.tab-content-container {
  position: relative;
}

.tab-content {
  display: none;
  animation: fadeIn 0.5s ease;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Media card styles */
.media-card {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-lg);
  background-color: var(--bg-card);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  transform: translateZ(0);
  width: 100% !important;
  margin: 0 !important;
}

.media-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.media-poster {
  width: 100%;
  aspect-ratio: 2/3;
  position: relative;
  overflow: hidden;
  background-color: var(--bg-card);
}

@supports not (aspect-ratio: 2/3) {
  .media-poster {
    height: 0;
    padding-bottom: 150%;
  }
}

.media-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.media-card:hover .media-poster img {
  transform: scale(1.05);
}

.media-info {
  padding: var(--spacing-2) var(--spacing-3);
}

.media-title {
  font-size: var(--font-size-sm);
  font-weight: 500;
  margin-bottom: var(--spacing-1);
  color: var(--text-primary);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  height: 2.4em;
}

.media-meta {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

/* Utility classes */
.reduced-motion * {
  transition: none !important;
  animation: none !important;
}

.simplified-animation {
  animation-duration: 0.2s !important;
}

/* === Responsive adjustments === */
@media (max-width: 768px) {
  .hero-title {
    font-size: var(--font-size-2xl);
  }
  
  .hero-subtitle {
    font-size: var(--font-size-lg);
  }
  
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }
  
  .tab-buttons {
    overflow-x: auto;
    padding-bottom: var(--spacing-2);
  }
  
  .tab-button {
    white-space: nowrap;
  }
}
