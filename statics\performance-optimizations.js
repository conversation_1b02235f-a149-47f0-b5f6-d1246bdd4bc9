/**
 * Performance Optimizations for UI
 * This file contains various performance optimizations for enhancing application efficiency
 */

document.addEventListener('DOMContentLoaded', () => {
    // Initialize performance optimizations
    initPerformanceOptimizations();
});

/**
 * Initialize all performance optimizations
 */
function initPerformanceOptimizations() {
    // Apply optimizations in sequence, with the most important ones first
    optimizeImageLoading();
    optimizeAnimations();
    optimizeEventListeners();
    optimizeScrollPerformance();
    optimizeFirebaseOperations();
    optimizeDOMUpdates();
}

/**
 * Optimize image loading
 */
function optimizeImageLoading() {
    // Use native image lazy loading where supported
    if ('loading' in HTMLImageElement.prototype) {
        document.querySelectorAll('img:not([loading])').forEach(img => {
            if (!img.hasAttribute('loading') && !img.classList.contains('progressive-image')) {
                img.loading = 'lazy';
            }
        });
    }

    // Set attributes for optimal image loading
    document.querySelectorAll('img').forEach(img => {
    // Add fetchpriority to important images
        if (img.closest('.hero, .featured')) {
        img.fetchPriority = 'high';
        }

    // Add decoding attribute to images
        if (!img.hasAttribute('decoding')) {
        img.decoding = 'async';
        }

        // Add explicit width and height to prevent layout shifts
        if (!img.hasAttribute('width') && !img.hasAttribute('height') && img.naturalWidth && img.naturalHeight) {
            const aspectRatio = img.naturalWidth / img.naturalHeight;
            
            if (img.style.width) {
                const width = parseInt(img.style.width);
                if (!isNaN(width)) {
                    img.height = Math.round(width / aspectRatio);
                }
            } else if (img.style.height) {
                const height = parseInt(img.style.height);
                if (!isNaN(height)) {
                    img.width = Math.round(height * aspectRatio);
                }
            }
        }
    });
}

/**
 * Optimize animations based on device capabilities
 */
function optimizeAnimations() {
    // Get utility functions if available
    const utils = window.Utils || {};
    
    // Check for reduced motion preference
    const prefersReducedMotion = typeof utils.prefersReducedMotion === 'function'
        ? utils.prefersReducedMotion()
        : window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    if (prefersReducedMotion) {
        document.body.classList.add('reduced-motion');
        document.querySelectorAll('.site-header').forEach(el => {
            el.classList.add('reduced-motion');
        });
    }

    // Check for low-end devices
    const isLowEnd = typeof utils.isLowEndDevice === 'function'
        ? utils.isLowEndDevice()
        : checkLowEndDevice();

    if (isLowEnd) {
        document.body.classList.add('low-end-device');

        // Disable complex animations
        document.querySelectorAll('.skeleton-loader, .progressive-image-container').forEach(el => {
            el.classList.add('simplified-animation');
        });
    }
}

/**
 * Check if device is low-end (fallback implementation)
 * @returns {boolean} True if the device is low-end
 */
function checkLowEndDevice() {
    return (
        (navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2) ||
        /Android [4-6]/.test(navigator.userAgent) ||
        /Mobile.*Firefox/.test(navigator.userAgent) ||
        (navigator.deviceMemory && navigator.deviceMemory <= 2)
    );
}

/**
 * Utility function: Debounce
 * Limits the rate at which a function can fire
 * 
 * @param {Function} func - The function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
function debounce(func, wait) {
    let timeout;
    return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
    };
}

/**
 * Utility function: Throttle
 * Ensures a function is called at most once in a specified period
 * 
 * @param {Function} func - The function to throttle
 * @param {number} limit - Throttle period in milliseconds
 * @returns {Function} Throttled function
 */
function throttle(func, limit) {
    let inThrottle = false;
    return function() {
        const context = this;
        const args = arguments;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => {
                inThrottle = false;
            }, limit);
        }
    };
}

/**
 * Optimize event listeners
 */
function optimizeEventListeners() {
    // Use passive event listeners for touch and wheel events
    const passiveEvents = ['touchstart', 'touchmove', 'wheel'];
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    
    EventTarget.prototype.addEventListener = function(type, listener, options) {
        let newOptions = options;
        
        if (passiveEvents.includes(type)) {
            if (typeof options === 'object') {
                if (options.passive === undefined) {
                    newOptions = {...options, passive: true};
                }
            } else {
                newOptions = {passive: true};
            }
        }
        
        originalAddEventListener.call(this, type, listener, newOptions);
    };

    // Debounce window resize events
    const utils = window.Utils || {};
    const originalResize = window.onresize;
    const debounceFn = typeof utils.debounce === 'function' ? utils.debounce : debounce;
    
    window.onresize = debounceFn(function(e) {
            if (originalResize) {
                originalResize(e);
            }
        }, 150);
}

/**
 * Optimize scroll performance
 */
function optimizeScrollPerformance() {
    // Elements that may cause scroll jank
    const heavyElements = document.querySelectorAll('.card-grid, .media-grid, .vod-list');
    const largeContainers = document.querySelectorAll('.container, .card-grid, .media-grid, .vod-list');

    // Optimize heavy elements
    heavyElements.forEach(el => {
        // Add will-change to elements that will be animated during scroll
        el.style.willChange = 'transform';

        // Remove will-change after a delay to free up resources
        setTimeout(() => {
            el.style.willChange = 'auto';
        }, 5000);
    });

    // Optimize large containers
    largeContainers.forEach(el => {
        el.style.contain = 'content';
    });
}

/**
 * Optimize Firebase operations
 */
function optimizeFirebaseOperations() {
    // Create loading indicator for Firebase operations
    const createFirebaseLoadingIndicator = () => {
        // Check if the indicator already exists
        if (document.getElementById('firebase-loading-indicator')) {
            return;
        }

        // Create indicator elements
        const indicator = document.createElement('div');
        indicator.id = 'firebase-loading-indicator';
        indicator.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background-color: transparent;
            z-index: 9999;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s, background-color 0.3s;
        `;

        const progressBar = document.createElement('div');
        progressBar.className = 'progress-bar';
        progressBar.style.cssText = `
            height: 100%;
            width: 0%;
            background-color: var(--primary, #3B82F6);
            transition: width 0.3s ease-out;
        `;

        // Append elements
        indicator.appendChild(progressBar);
        document.body.appendChild(indicator);

        // Track Firebase operations
        if (window.firebase) {
            let operationsCount = 0;
            let timeout;

            // Create a function to update the loading indicator
            const updateLoadingIndicator = () => {
                const progressBar = document.querySelector('#firebase-loading-indicator .progress-bar');
                if (!progressBar) return;

                // Simulate progress based on operations count
                const progress = Math.min(95, 100 - (100 / (operationsCount + 1)));
                progressBar.style.width = `${progress}%`;
            };

            // Show indicator when operations count increases
            const showIndicator = () => {
                const indicator = document.getElementById('firebase-loading-indicator');
                if (!indicator) return;

                indicator.style.opacity = '1';
                indicator.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
                
                clearTimeout(timeout);
                updateLoadingIndicator();
            };

            // Hide indicator when operations count reaches 0
            const hideIndicator = () => {
                const indicator = document.getElementById('firebase-loading-indicator');
                const progressBar = document.querySelector('#firebase-loading-indicator .progress-bar');
                
                if (!indicator || !progressBar) return;

                // Complete the progress animation
                progressBar.style.width = '100%';
        
                // Fade out the indicator
                timeout = setTimeout(() => {
                    indicator.style.opacity = '0';
                    
                    // Reset progress after fade out
        setTimeout(() => {
                        progressBar.style.width = '0%';
                    }, 300);
                }, 300);
    };

            // Mock intercepting Firebase operations
            // Note: This is a simplified approach that should be adapted for real-world use
            const originalGet = window.firebase.database?.reference?.get;
            const originalSet = window.firebase.database?.reference?.set;
            
            if (originalGet) {
                window.firebase.database.reference.get = function() {
                    operationsCount++;
                    showIndicator();
                    
                    return originalGet.apply(this, arguments)
                        .finally(() => {
                            operationsCount--;
                            if (operationsCount <= 0) {
                                hideIndicator();
                            } else {
                                updateLoadingIndicator();
            }
                        });
                };
            }
            
            if (originalSet) {
                window.firebase.database.reference.set = function() {
                    operationsCount++;
                    showIndicator();
                    
                    return originalSet.apply(this, arguments)
                        .finally(() => {
                            operationsCount--;
                            if (operationsCount <= 0) {
                                hideIndicator();
                            } else {
                                updateLoadingIndicator();
            }
                        });
                };
            }
        }
    };

    // Create the loading indicator
    createFirebaseLoadingIndicator();
}

/**
 * Optimize DOM updates with efficient batch processing
 */
function optimizeDOMUpdates() {
    // Add batch rendering for large lists
    const batchRender = (items, container, renderFn, batchSize = 20, delay = 10) => {
        // Clear the container
        while (container.firstChild) {
            container.removeChild(container.firstChild);
        }
        
        // Use document fragment for better performance
        const fragment = document.createDocumentFragment();
        const totalItems = items.length;
        
        // Render batches of items with small delays to prevent UI blocking
        const renderBatch = (startIndex) => {
            const endIndex = Math.min(startIndex + batchSize, totalItems);
            
            // Render current batch
            for (let i = startIndex; i < endIndex; i++) {
                const item = items[i];
                const element = renderFn(item);
                fragment.appendChild(element);
            }
            
            // Append fragment if this is the last batch or first batch
            if (endIndex === totalItems || startIndex === 0) {
                container.appendChild(fragment.cloneNode(true));
            }
            
            // Continue with next batch if needed
            if (endIndex < totalItems) {
                setTimeout(() => {
                    renderBatch(endIndex);
                }, delay);
            }
        };
        
        // Start rendering
        renderBatch(0);
    };
    
    // Make batch render available globally
    window.optimizedBatchRender = batchRender;
    
    // Apply to existing elements if needed
    const largeLists = document.querySelectorAll('.card-grid[data-optimize="true"], .media-grid[data-optimize="true"]');
    largeLists.forEach(container => {
        // Check if the container has a large number of children
        if (container.children.length > 50) {
            // Convert to array and apply batch rendering
            const items = Array.from(container.children);
            const renderFn = (item) => item.cloneNode(true);
            
            // Use batch rendering
            batchRender(items, container, renderFn);
        }
    });
}
