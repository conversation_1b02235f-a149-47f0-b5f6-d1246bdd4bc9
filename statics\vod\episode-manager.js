/**
 * Episode Manager - Handles episode pagination and watched status
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize watched status first
    updateWatchedEpisodes();

    // Then initialize pagination (so watched status is already applied)
    initEpisodePagination();

    // Scroll to active episode
    setTimeout(scrollToActiveEpisode, 100);
});

/**
 * Initialize episode pagination functionality
 */
function initEpisodePagination() {
    // Get all pagination buttons
    const paginationButtons = document.querySelectorAll('.episode-page-button');

    // Add click event to each button
    paginationButtons.forEach(button => {
        button.addEventListener('click', function() {
            const page = this.getAttribute('data-page');
            const channel = this.getAttribute('data-channel');

            // Remove active class from all buttons in this channel
            document.querySelectorAll(`.episodes-pagination[data-channel="${channel}"] .episode-page-button`).forEach(btn => {
                btn.classList.remove('active');
            });

            // Add active class to clicked button
            this.classList.add('active');

            // Show episodes for this page
            showEpisodesForPage(channel, page);
        });
    });

    // Check if there's a page parameter in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const urlPage = urlParams.get('page');
    const urlChannel = urlParams.get('ch');

    // For each channel, show the active page
    const channels = new Set();
    paginationButtons.forEach(button => {
        channels.add(button.getAttribute('data-channel'));
    });

    // For each channel, show the active page
    channels.forEach(channel => {
        // If this is the channel from the URL and there's a page parameter, use that
        if (channel === urlChannel && urlPage) {
            const pageButton = document.querySelector(`.episodes-pagination[data-channel="${channel}"] .episode-page-button[data-page="${urlPage}"]`);
            if (pageButton) {
                // Remove active class from all buttons in this channel
                document.querySelectorAll(`.episodes-pagination[data-channel="${channel}"] .episode-page-button`).forEach(btn => {
                    btn.classList.remove('active');
                });

                // Add active class to this button
                pageButton.classList.add('active');

                // Show episodes for this page
                showEpisodesForPage(channel, urlPage);
                return;
            }
        }

        // Otherwise, use the active button or the first button
        const activeButton = document.querySelector(`.episodes-pagination[data-channel="${channel}"] .episode-page-button.active`);
        if (activeButton) {
            const page = activeButton.getAttribute('data-page');
            showEpisodesForPage(channel, page);
        } else {
            // If no active button, show the first page
            const firstButton = document.querySelector(`.episodes-pagination[data-channel="${channel}"] .episode-page-button`);
            if (firstButton) {
                firstButton.classList.add('active');
                showEpisodesForPage(channel, firstButton.getAttribute('data-page'));
            }
        }
    });
}

/**
 * Show episodes for the selected page
 * @param {string} channel - The channel code
 * @param {number} page - The page number to show
 */
function showEpisodesForPage(channel, page) {
    // Get all episode buttons for this channel
    const allEpisodes = document.querySelectorAll(`.tab-content[data-tab-id="${channel}"] .episode-button`);

    // Get episodes per page
    const episodesPerPage = 24;

    // Convert page to number
    const pageNum = parseInt(page, 10);

    // Calculate start and end indices
    const startIdx = (pageNum - 1) * episodesPerPage;
    const endIdx = pageNum * episodesPerPage - 1;

    // Hide all episodes
    allEpisodes.forEach((episode, index) => {
        if (index >= startIdx && index <= endIdx) {
            episode.style.display = '';
        } else {
            episode.style.display = 'none';
        }
    });

    // Update URL with the current page (without reloading)
    try {
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('ch') === channel) {
            // Only update if we're on this channel
            const url = new URL(window.location.href);
            url.searchParams.set('page', pageNum);
            window.history.replaceState({}, '', url);
        }
    } catch (error) {
        // Error updating URL
    }

    // Scroll to the top of the episodes grid for better UX
    const episodesGrid = document.querySelector(`.tab-content[data-tab-id="${channel}"] .episodes-grid`);
    if (episodesGrid) {
        // Smooth scroll to the top of the episodes grid
        episodesGrid.scrollIntoView({ behavior: 'smooth', block: 'start' });

        // Also scroll to the active episode if there is one
        setTimeout(scrollToActiveEpisode, 100);
    }
}

/**
 * Update watched status for episodes
 */
function updateWatchedEpisodes() {
    try {
        // Get the VOD ID from the URL
        const urlParams = new URLSearchParams(window.location.search);
        const vodId = urlParams.get('id');

        if (!vodId) return;

        // Get play cache from localStorage
        const playCache = JSON.parse(localStorage.getItem('vodg-play') || '{}');

        // If no cache for this VOD, return
        if (!playCache[vodId]) return;

        // Loop through all episodes
        document.querySelectorAll('.episode-button').forEach(episode => {
            const episodeNum = episode.getAttribute('data-episode');

            // If this episode has been watched
            if (playCache[vodId][episodeNum] && playCache[vodId][episodeNum].playProgressRate > 0) {
                // Add watched class
                episode.classList.add('watched');

                // If progress is more than 90%, consider it fully watched
                if (playCache[vodId][episodeNum].playProgressRate > 0.9) {
                    episode.classList.add('fully-watched');
                }
            }
        });
    } catch (error) {
        // Error updating watched episodes
    }
}

/**
 * Scroll to the active episode in the episodes grid
 */
function scrollToActiveEpisode() {
    try {
        // Find the active episode
        const activeEpisode = document.querySelector('.episode-button.active');

        if (!activeEpisode) return;

        // Get the parent episodes grid
        const episodesGrid = activeEpisode.closest('.episodes-grid');

        if (!episodesGrid) return;

        // Calculate the scroll position
        // We want the active episode to be in the middle of the visible area if possible
        const episodeRect = activeEpisode.getBoundingClientRect();
        const gridRect = episodesGrid.getBoundingClientRect();

        // Calculate the target scroll position
        const targetScroll = activeEpisode.offsetTop - (gridRect.height / 2) + (episodeRect.height / 2);

        // Scroll to the target position
        episodesGrid.scrollTo({
            top: targetScroll,
            behavior: 'smooth'
        });
    } catch (error) {
        // Error scrolling to active episode
    }
}

/**
 * Update watched status for a specific episode
 * This function can be called from play.html to update the status in real-time
 * @param {string} vodId - The VOD ID
 * @param {string} episodeNum - The episode number
 * @param {number} progress - The watch progress (0-1)
 */
window.updateEpisodeWatchStatus = function(vodId, episodeNum, progress) {
    try {
        // If we're on the detail page for this VOD
        const urlParams = new URLSearchParams(window.location.search);
        const currentVodId = urlParams.get('id');

        if (currentVodId !== vodId) return;

        // Find the episode button
        const episode = document.querySelector(`.episode-button[data-episode="${episodeNum}"]`);

        if (!episode) return;

        // Update classes based on progress
        if (progress > 0) {
            episode.classList.add('watched');

            if (progress > 0.9) {
                episode.classList.add('fully-watched');
            } else {
                episode.classList.remove('fully-watched');
            }
        } else {
            episode.classList.remove('watched', 'fully-watched');
        }
    } catch (error) {
        // Error updating episode watch status
    }
};
