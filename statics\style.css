/* Import CSS variables */
@import url('/statics/css-variables.css');

.top-bar-left,
.top-bar-right,
.top-bar {
    display: flex;
    align-items: baseline;
}

.top-bar {
    flex-wrap: wrap-reverse;
    position: relative;
    display: flex;
    justify-content: space-between;
}

@media screen and (min-width: 601px) {
    .top-bar {
        padding-top: 0;
    }
}

.top-bar-left a {
    text-decoration: none;
    color: var(--text-tertiary);
}

.top-bar-right {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background-color: var(--bg-card);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius);
    z-index: var(--z-above);
    position: relative;
}

.top-bar-right>div {
    margin-left: var(--spacing-4);
    position: relative;
}

.histbody {
    font-family: var(--font-family);
    background: var(--bg-dark);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    -webkit-font-smoothing: antialiased;
}

.history {
    position: relative;
    z-index: var(--z-header);
}

.history-btn {
    cursor: pointer;
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius);
    transition: background-color var(--transition) var(--transition-ease), color var(--transition) var(--transition-ease);
    font-weight: 500;
    white-space: nowrap;
}

.history-btn:hover {
    background-color: var(--bg-card-hover);
    color: var(--text-primary);
}

/* History content styles moved to shared-containers.css for standardization */
.history-content {
    top: 2.2em; /* Specific positioning for history dropdown */
}

@media screen and (max-width: 600px) {
    .history-content {
        top: 2.5em; /* Adjust dropdown position in mobile view */
        right: 0;
        width: 90%; /* Use percentage width on mobile */
        max-width: 360px; /* Maximum width on mobile */
        left: 50%; /* Center horizontally */
        transform: translateX(-50%); /* Center horizontally */
        z-index: var(--z-modal); /* Ensure dropdown appears above other elements */
    }

    /* Ensure the history button is visible */
    .history-btn {
        background-color: var(--bg-card);
    }
}

.history-content.history-content-show {
    visibility: visible;
    z-index: var(--z-header);
}

/* History list styles moved to shared-containers.css for standardization */

.history-clear {
    padding: var(--spacing-3);
    background-color: var(--bg-dark);
    border-radius: 0 0 var(--radius) var(--radius);
    color: var(--text-primary);
    text-align: center;
    border-top: 1px solid var(--border);
}

.history-clear button {
    background-color: var(--accent);
    color: white;
    border: none;
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--radius);
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: background-color var(--transition) var(--transition-ease);
}

.history-clear button:hover {
    background-color: var(--accent-dark);
}

.history-list li {
    list-style: none;
    padding: 0;
    background-color: var(--bg-card);
    border-bottom: 1px solid var(--border);
    transition: background-color var(--transition) var(--transition-ease);
}

.history-list li:hover {
    background-color: var(--bg-card-hover);
}

.history-list li a {
    display: block;
    padding: var(--spacing-2) var(--spacing-3);
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--text-primary);
    text-decoration: none;
    transition: color var(--transition) var(--transition-ease);
}

.history-list li a:hover {
    color: var(--accent);
}

/* Responsive styles for history dropdown */
@media screen and (max-width: 600px) {
    /* Basic mobile styles for all pages */
    .top-bar {
        position: relative;
    }

    .top-bar-left h2 {
        margin-right: 0;
        /* Keep original font size for site title */
    }

    .top-bar-right {
        padding: calc(var(--spacing-1) / 2) var(--spacing-1);
    }

    .top-bar-right>div {
        margin-left: var(--spacing-2);
    }

    .history-btn {
        font-size: var(--font-size-xs);
        padding: calc(var(--spacing-1) / 2) var(--spacing-1);
        background-color: var(--bg-card);
        border-radius: var(--radius);
    }

    /* Simple fix to position the history button on the right side of the title */
    .top-bar-right {
        position: absolute;
        top: 0;
        right: 0;
        background-color: transparent;
    }

    /* Mobile history content styles moved to shared-containers.css for standardization */
    .history-content {
        top: 50%; /* Specific positioning for history dropdown on mobile */
        transform: translate(-50%, -50%) !important; /* Override the transform from shared styles */
    }

    /* Make history list items more readable on mobile */
    .history-list li a {
        padding: var(--spacing-3) var(--spacing-4);
        font-size: var(--font-size-base);
    }
}

@media screen and (max-width: 400px) {
    .top-bar-left h2 {
        /* Keep original font size for site title */
    }

    .top-bar-right {
        padding: 0.1em;
    }

    .top-bar-right>div {
        margin-left: 0.3em;
    }

    .history-btn {
        font-size: 0.75em;
        padding: 0.15em 0.3em;
    }
}

.vod-detail-box {
    display: flex;
    flex-wrap: wrap;
    background: linear-gradient(120deg, #23272e 80%, #23272e 100%);
    color: #eee;
    border-radius: 18px;
    box-shadow: 0 2px 18px 0 rgba(0,0,0,0.13);
    padding: 2em 1.5em;
    gap: 2em;
    margin-bottom: 1.5em;
    align-items: flex-start;
}

.tabs-head .tabs-head-btn {
    padding: 0.32em 1.1em;
    border: 1px solid #444;
    background: #181a1b;
    color: #eee;
    border-radius: 0.6em 0.6em 0 0;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.18s, color 0.18s, border-color 0.18s, box-shadow 0.14s, transform 0.14s;
    margin-bottom: -2px;
}
.tabs-head .tabs-head-btn:hover:not(.tabs-head-btn-active) {
    background: #ffe082 !important;
    color: #ff3c00 !important;
    border: 2px solid #ff3c00 !important;
    box-shadow: 0 2px 12px 0 #ffe08299, 0 0 0 2px #ff3c00;
    transform: scale(1.08);
    font-weight: 700;
    z-index: 1;
}
.tabs-head .tabs-head-btn.tabs-head-btn-active,
.tabs-head .tabs-head-btn.tabs-head-btn-active:hover,
.tabs-head .tabs-head-btn.tabs-head-btn-active:focus {
    background: #ff3c00 !important;
    color: #fff !important;
    border-bottom: 5px solid #fff200 !important;
    border-radius: 0.6em 0.6em 0 0 !important;
    box-shadow: 0 0 0 4px #fff200, 0 0 18px 4px #ff3c00, 0 4px 24px 0 #fff20099 !important;
    outline: 3px solid #fff200 !important;
    outline-offset: -3px !important;
    font-weight: 900 !important;
    z-index: 2 !important;
    transition: all 0.14s cubic-bezier(.4,2,.6,1);
}

.tabs-head {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 0.5em;
    margin-bottom: 0.5em;
    background: #23272e;
    border-radius: 10px 10px 0 0;
    padding: 0.5em 1em 0.3em 1em;
    box-shadow: 0 1px 6px 0 rgba(0,0,0,0.08);
}

.vod-detail-box .vod-poster {
    flex: 0 0 180px;
    margin-right: 1em;
    align-self: flex-start;
}

.vod-detail-box .vod-poster img {
    width: 180px;
    border-radius: 12px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.12);
    background: #181a1b;
    object-fit: cover;
}

.idx-search-btn {
    background: linear-gradient(90deg, var(--main-accent) 60%, var(--main-accent-dark) 100%);
    color: #fff;
    border: none;
    border-radius: 1em;
    padding: 0 1.4em;
    cursor: pointer;
    font-weight: 600;
    font-size: 1.05em;
    box-shadow: 0 1px 4px 0 rgba(255,152,0,0.10);
    transition: background 0.18s, box-shadow 0.18s;
    outline: none;
}
.idx-search-btn:focus {
    box-shadow: 0 0 0 3px var(--main-focus), 0 2px 8px 0 rgba(255,152,0,0.16);
}
.idx-search-btn:hover {
    background: linear-gradient(90deg, var(--main-accent-light) 60%, #ff7043 100%);
    box-shadow: 0 2px 8px 0 rgba(255,152,0,0.16);
}

.idx-search select {
    background: #23272e;
    color: #eee;
    border: 1px solid #444;
    border-radius: 1em;
    padding: 0 1.3em;
    font-size: 1.05em;
    transition: border-color 0.2s;
    height: 2.1em;
    outline: none;
}

.vod-list .vod-detail-box .vod-details {
    flex: 1;
    display: grid;
    grid-template-columns: 100px 1fr;
    gap: 0.5em 1em;
    font-size: 1.04em;
    background: none;
    border-radius: 0 0 12px 12px;
}

.vod-detail-box .vod-details div {
    padding: 0.12em 0.2em;
    line-height: 1.5;
}

.vod-list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 0.5em;
    background-color: #181a1b;
}

.vod-list .vod-item {
    width: calc(50% - 0.25em);
}

/* Loading Skeleton Styles */
.vod-item.skeleton {
    pointer-events: none;
    background: var(--main-card, #23272e);
    box-shadow: none;
    border-radius: 16px;
    min-height: 210px;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    animation: skeleton-fadein 0.3s;
}
.skeleton-box {
    width: 100%;
    height: 0;
    padding-bottom: 150%;
    border-radius: 12px 12px 0 0;
    background: linear-gradient(90deg, #23272e 25%, #2c2f36 37%, #23272e 63%);
    background-size: 400% 100%;
    animation: skeleton-shimmer 1.2s infinite linear;
}
.skeleton-bar {
    height: 1.1em;
    margin: 1em 0.7em 0.7em 0.7em;
    border-radius: 6px;
    background: linear-gradient(90deg, #23272e 25%, #2c2f36 37%, #23272e 63%);
    background-size: 400% 100%;
    animation: skeleton-shimmer 1.2s infinite linear;
}
@keyframes skeleton-shimmer {
    0% {
        background-position: 100% 0;
    }
    100% {
        background-position: 0 0;
    }
}
@keyframes skeleton-fadein {
    from { opacity: 0; }
    to { opacity: 1; }
}
    overflow: hidden;
    cursor: pointer;
    background-color: var(--main-card);
    border-radius: 16px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.10), 0 1.5px 4px 0 rgba(0,0,0,0.10);
    transition: transform 0.18s cubic-bezier(.4,2,.6,1), box-shadow 0.18s;
    margin-bottom: 0.5em;
    display: flex;
    flex-direction: column;
    outline: none;
}
.vod-list .vod-item:focus {
    box-shadow: 0 0 0 3px var(--main-focus), 0 2px 12px 0 rgba(0,0,0,0.12);
}
.vod-list .vod-item:hover {
    transform: translateY(-4px) scale(1.025);
    box-shadow: 0 4px 24px 0 rgba(0,0,0,0.18), 0 3px 8px 0 rgba(0,0,0,0.13), 0 0 0 3px var(--main-accent-light);
    z-index: 2;
}

.vod-list .vod-item:hover {
    transform: translateY(-4px) scale(1.025);
    box-shadow: 0 4px 24px 0 rgba(0,0,0,0.18), 0 3px 8px 0 rgba(0,0,0,0.13);
    z-index: 2;
}

@media screen and (min-width: 400px) {
    .vod-list .vod-item {
        width: calc(33.333% - 0.34em);
    }
}

@media screen and (min-width: 600px) {
    .vod-list .vod-item {
        width: calc(25% - 0.38em);
    }
}

@media screen and (min-width: 800px) {
    .vod-list .vod-item {
        width: calc(16.666% - 0.42em);
    }
}

@media screen and (min-width: 900px) {
    .vod-list .vod-item {
        width: calc(150px - 0.42em);
    }
}

.vod-list .vod-item a {
    color: #eee;
    text-decoration: none;
}

.vod-list .vod-poster {
    position: relative;
    width: 100%;
    height: auto;
    padding-bottom: 150%;
    background-position: center;
    background-color: #222;
    border-radius: 12px 12px 0 0;
    overflow: hidden;
    transition: box-shadow 0.18s;
}

.vod-list .vod-item:hover .vod-poster {
    box-shadow: 0 2px 16px 0 rgba(255,120,40,0.10);
}

.vod-list .vod-poster .vod-badge {
    font-family: inherit;
    font-size: 1em;
    color: #fff;
    background: linear-gradient(90deg, var(--main-accent) 60%, var(--main-accent-dark) 100%);
    border: none;
    filter: brightness(1.05);
    letter-spacing: 0.04em;
    position: absolute;
    background: linear-gradient(90deg, #ff9800 60%, #ff5722 100%);
    color: #fff;
    top: 0.5em;
    right: 0.5em;
    padding: 0.2em 0.7em;
    border-radius: 8px;
    font-weight: bold;
    font-size: 0.95em;
    box-shadow: 0 2px 8px 0 rgba(0,0,0,0.15);
    opacity: 0.95;
    letter-spacing: 0.04em;
    transition: opacity 0.18s;
}

.vod-list .vod-item:hover .vod-badge {
    opacity: 1;
}

.vod-list .vod-poster>img {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
    top: 0;
    right: 0;
}

.vod-list .vod-poster .vod-memo {
    position: absolute;
    background: rgba(30,30,30,0.88);
    bottom: 0.6em;
    left: 0.6em;
    padding: 0.15em 0.7em;
    border-radius: 8px;
    font-size: 0.75em;
    color: #fff;
    box-shadow: 0 1px 4px 0 rgba(0,0,0,0.13);
    opacity: 0.92;
    transition: opacity 0.18s;
    max-width: 80%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.vod-list .vod-item:hover .vod-memo {
    opacity: 1;
}

.vod-list .vod-title {
    overflow: hidden;
    color: #fff;
    font-size: 1.08em;
    font-weight: 600;
    text-align: center;
    margin: 0.8em 0 0.5em 0;
    letter-spacing: 0.02em;
    text-shadow: 0 1px 4px rgba(0,0,0,0.08);
    transition: color 0.15s;
}

.vod-list .vod-item:hover .vod-title {
    color: #ff9800;
}

@keyframes circle {
    0% {
        transform: rotate(0deg);
    }
    25% {
        transform: rotate(90deg);
    }
    50% {
        transform: rotate(180deg);
    }
    75% {
        transform: rotate(270deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.loading {
    position: relative;
    width: 2.5em;
    height: 2.5em;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: #ff9800; /* Use the site's accent color */
    border-radius: 100%;
    animation: circle infinite 0.8s cubic-bezier(0.5, 0.1, 0.5, 0.9);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

@keyframes flash-neon {
    0%,
    100% {
        text-shadow: none;
    }

    50% {
        color: #eee;
        text-shadow: 0px 0px 10px #f60;
    }
}

.neon-flash {
    animation: flash-neon 1s 8 ease-in-out;
}
