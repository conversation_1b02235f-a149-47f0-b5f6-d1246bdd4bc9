<!-- 
    Confirmation Dialogs - Modular dialog components
    Extracted from base.html for better maintainability
-->

<!-- Logout Confirmation Dialog -->
<div id="logout-confirm-dialog" class="confirm-dialog" role="dialog" aria-labelledby="logout-dialog-title" aria-hidden="true">
    <div class="confirm-dialog-backdrop" onclick="closeLogoutConfirm()"></div>
    <div class="confirm-dialog-content">
        <div class="confirm-dialog-header">
            <h3 id="logout-dialog-title">确认登出</h3>
            <button class="confirm-dialog-close" onclick="closeLogoutConfirm()" aria-label="关闭对话框">
                &times;
            </button>
        </div>
        <div class="confirm-dialog-body">
            <p>您确定要登出吗？</p>
        </div>
        <div class="confirm-dialog-footer">
            <button class="btn btn-outline" onclick="closeLogoutConfirm()">取消</button>
            <button class="btn btn-primary" onclick="executeLogout()">确认登出</button>
        </div>
    </div>
</div>

<!-- Clear History Confirmation Dialog -->
<div id="clear-history-confirm-dialog" class="confirm-dialog" role="dialog" aria-labelledby="clear-history-dialog-title" aria-hidden="true">
    <div class="confirm-dialog-backdrop" onclick="closeClearHistoryConfirm()"></div>
    <div class="confirm-dialog-content">
        <div class="confirm-dialog-header">
            <h3 id="clear-history-dialog-title">确认清除历史记录</h3>
            <button class="confirm-dialog-close" onclick="closeClearHistoryConfirm()" aria-label="关闭对话框">
                &times;
            </button>
        </div>
        <div class="confirm-dialog-body">
            <p>您确定要清除所有历史记录吗？此操作无法撤销。</p>
        </div>
        <div class="confirm-dialog-footer">
            <button class="btn btn-outline" onclick="closeClearHistoryConfirm()">取消</button>
            <button class="btn btn-primary" onclick="executeClearHistory()">确认清除</button>
        </div>
    </div>
</div>

<!-- Clear Favorites Confirmation Dialog -->
<div id="clear-favorites-confirm-dialog" class="confirm-dialog" role="dialog" aria-labelledby="clear-favorites-dialog-title" aria-hidden="true">
    <div class="confirm-dialog-backdrop" onclick="closeClearFavoritesConfirm()"></div>
    <div class="confirm-dialog-content">
        <div class="confirm-dialog-header">
            <h3 id="clear-favorites-dialog-title">确认清除收藏夹</h3>
            <button class="confirm-dialog-close" onclick="closeClearFavoritesConfirm()" aria-label="关闭对话框">
                &times;
            </button>
        </div>
        <div class="confirm-dialog-body">
            <p>您确定要清除所有收藏吗？此操作无法撤销。</p>
        </div>
        <div class="confirm-dialog-footer">
            <button class="btn btn-outline" onclick="closeClearFavoritesConfirm()">取消</button>
            <button class="btn btn-primary" onclick="executeClearFavorites()">确认清除</button>
        </div>
    </div>
</div>

<!-- Authentication Modal (Login/Register) -->
<div id="auth-modal" class="confirm-dialog" role="dialog" aria-labelledby="auth-modal-title" aria-hidden="true">
    <div class="confirm-dialog-backdrop" onclick="closeAuthModal()"></div>
    <div class="confirm-dialog-content auth-modal-content">
        <div class="confirm-dialog-header">
            <h3 id="auth-modal-title">用户认证</h3>
            <button class="confirm-dialog-close" onclick="closeAuthModal()" aria-label="关闭对话框">
                &times;
            </button>
        </div>
        <div class="confirm-dialog-body">
            <!-- Auth content will be loaded dynamically -->
            <div id="auth-modal-content">
                <div class="auth-tabs">
                    <button class="auth-tab active" data-tab="login">登录</button>
                    <button class="auth-tab" data-tab="register">注册</button>
                </div>
                <div class="auth-forms">
                    <div id="login-form" class="auth-form active">
                        <form>
                            <div class="form-group">
                                <label for="login-email" class="form-label">邮箱</label>
                                <input type="email" id="login-email" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="login-password" class="form-label">密码</label>
                                <input type="password" id="login-password" class="form-control" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">登录</button>
                        </form>
                    </div>
                    <div id="register-form" class="auth-form">
                        <form>
                            <div class="form-group">
                                <label for="register-email" class="form-label">邮箱</label>
                                <input type="email" id="register-email" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="register-password" class="form-label">密码</label>
                                <input type="password" id="register-password" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="register-confirm-password" class="form-label">确认密码</label>
                                <input type="password" id="register-confirm-password" class="form-control" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">注册</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Global Dialog Functions -->
<script>
    // Logout confirmation functions
    window.confirmLogout = function() {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            const confirmDialog = document.getElementById('logout-confirm-dialog');
            confirmDialog.classList.add('active');
            confirmDialog.setAttribute('aria-hidden', 'false');
            document.body.style.overflow = 'hidden';
        } else {
            // On desktop, just log out directly
            if (typeof window.signOutUser === 'function') {
                window.signOutUser();
            }
        }
    };

    window.closeLogoutConfirm = function() {
        const confirmDialog = document.getElementById('logout-confirm-dialog');
        confirmDialog.classList.remove('active');
        confirmDialog.setAttribute('aria-hidden', 'true');
        document.body.style.overflow = '';
    };

    window.executeLogout = function() {
        closeLogoutConfirm();
        if (typeof window.signOutUser === 'function') {
            window.signOutUser();
        }
    };

    // Clear History confirmation functions
    let historyTypeToBeCleared = 'all';

    window.confirmClearHistory = function(type = 'all') {
        historyTypeToBeCleared = type;
        const confirmDialog = document.getElementById('clear-history-confirm-dialog');
        confirmDialog.classList.add('active');
        confirmDialog.setAttribute('aria-hidden', 'false');
        document.body.style.overflow = 'hidden';
    };

    window.closeClearHistoryConfirm = function() {
        const confirmDialog = document.getElementById('clear-history-confirm-dialog');
        confirmDialog.classList.remove('active');
        confirmDialog.setAttribute('aria-hidden', 'true');
        document.body.style.overflow = '';
    };

    window.executeClearHistory = function() {
        closeClearHistoryConfirm();
        if (typeof window.clearHistory === 'function') {
            window.clearHistory(historyTypeToBeCleared);
        }
    };

    // Clear Favorites confirmation functions
    window.confirmClearFavorites = function() {
        const confirmDialog = document.getElementById('clear-favorites-confirm-dialog');
        confirmDialog.classList.add('active');
        confirmDialog.setAttribute('aria-hidden', 'false');
        document.body.style.overflow = 'hidden';
    };

    window.closeClearFavoritesConfirm = function() {
        const confirmDialog = document.getElementById('clear-favorites-confirm-dialog');
        confirmDialog.classList.remove('active');
        confirmDialog.setAttribute('aria-hidden', 'true');
        document.body.style.overflow = '';
    };

    window.executeClearFavorites = function() {
        closeClearFavoritesConfirm();
        if (typeof window.clearFavorites === 'function') {
            window.clearFavorites();
        }
    };

    // Authentication modal functions
    window.openLoginModal = function() {
        const authModal = document.getElementById('auth-modal');
        const loginTab = authModal.querySelector('[data-tab="login"]');
        const loginForm = authModal.querySelector('#login-form');
        
        // Reset to login tab
        authModal.querySelectorAll('.auth-tab').forEach(tab => tab.classList.remove('active'));
        authModal.querySelectorAll('.auth-form').forEach(form => form.classList.remove('active'));
        
        loginTab.classList.add('active');
        loginForm.classList.add('active');
        
        authModal.classList.add('active');
        authModal.setAttribute('aria-hidden', 'false');
        document.body.style.overflow = 'hidden';
    };

    window.openRegisterModal = function() {
        const authModal = document.getElementById('auth-modal');
        const registerTab = authModal.querySelector('[data-tab="register"]');
        const registerForm = authModal.querySelector('#register-form');
        
        // Reset to register tab
        authModal.querySelectorAll('.auth-tab').forEach(tab => tab.classList.remove('active'));
        authModal.querySelectorAll('.auth-form').forEach(form => form.classList.remove('active'));
        
        registerTab.classList.add('active');
        registerForm.classList.add('active');
        
        authModal.classList.add('active');
        authModal.setAttribute('aria-hidden', 'false');
        document.body.style.overflow = 'hidden';
    };

    window.closeAuthModal = function() {
        const authModal = document.getElementById('auth-modal');
        authModal.classList.remove('active');
        authModal.setAttribute('aria-hidden', 'true');
        document.body.style.overflow = '';
    };

    // Auth tab switching
    document.addEventListener('DOMContentLoaded', function() {
        const authTabs = document.querySelectorAll('.auth-tab');
        const authForms = document.querySelectorAll('.auth-form');
        
        authTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const targetTab = this.dataset.tab;
                
                // Remove active class from all tabs and forms
                authTabs.forEach(t => t.classList.remove('active'));
                authForms.forEach(f => f.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding form
                this.classList.add('active');
                document.getElementById(targetTab + '-form').classList.add('active');
            });
        });

        // Close dialogs when clicking on backdrop
        document.querySelectorAll('.confirm-dialog').forEach(dialog => {
            dialog.addEventListener('click', function(e) {
                if (e.target === dialog) {
                    const closeFunction = dialog.id.replace('-dialog', '').replace('-confirm', '') + 'Confirm';
                    if (typeof window['close' + closeFunction.charAt(0).toUpperCase() + closeFunction.slice(1)] === 'function') {
                        window['close' + closeFunction.charAt(0).toUpperCase() + closeFunction.slice(1)]();
                    }
                }
            });
        });

        // Handle escape key for dialogs
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const activeDialog = document.querySelector('.confirm-dialog.active');
                if (activeDialog) {
                    const closeButton = activeDialog.querySelector('.confirm-dialog-close');
                    if (closeButton) {
                        closeButton.click();
                    }
                }
            }
        });
    });
</script>

<style>
    /* Dialog base styles */
    .confirm-dialog {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }
    
    .confirm-dialog.active {
        opacity: 1;
        visibility: visible;
    }
    
    .confirm-dialog-backdrop {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1;
    }
    
    .confirm-dialog-content {
        position: relative;
        z-index: 2;
        background-color: var(--bg-card, #1f2937);
        border-radius: var(--radius-lg, 0.5rem);
        box-shadow: var(--shadow-lg);
        max-width: 90%;
        width: 400px;
        max-height: 90vh;
        overflow-y: auto;
    }
    
    .confirm-dialog-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-4, 1rem);
        border-bottom: 1px solid var(--border, #374151);
    }
    
    .confirm-dialog-header h3 {
        margin: 0;
        font-size: var(--font-size-lg, 1.125rem);
        font-weight: var(--font-weight-semibold, 600);
        color: var(--text-primary, #f9fafb);
    }
    
    .confirm-dialog-close {
        background: none;
        border: none;
        color: var(--text-tertiary, #9ca3af);
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0;
        line-height: 1;
        transition: color 0.2s ease;
    }
    
    .confirm-dialog-close:hover {
        color: var(--text-primary, #f9fafb);
    }
    
    .confirm-dialog-body {
        padding: var(--spacing-4, 1rem);
        color: var(--text-secondary, #e5e7eb);
    }
    
    .confirm-dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: var(--spacing-3, 0.75rem);
        padding: var(--spacing-4, 1rem);
        border-top: 1px solid var(--border, #374151);
    }
    
    /* Auth modal specific styles */
    .auth-modal-content {
        width: 450px;
    }
    
    .auth-tabs {
        display: flex;
        border-bottom: 1px solid var(--border, #374151);
        margin-bottom: var(--spacing-4, 1rem);
    }
    
    .auth-tab {
        flex: 1;
        padding: var(--spacing-3, 0.75rem);
        background: none;
        border: none;
        color: var(--text-tertiary, #9ca3af);
        cursor: pointer;
        transition: color 0.2s ease;
        border-bottom: 2px solid transparent;
    }
    
    .auth-tab.active {
        color: var(--primary, #2563eb);
        border-bottom-color: var(--primary, #2563eb);
    }
    
    .auth-form {
        display: none;
    }
    
    .auth-form.active {
        display: block;
    }
    
    .w-100 {
        width: 100%;
    }
</style>
