/**
 * Clean History Functionality
 */

// Simple history functionality
window.loadHistoryItems = function() {
  console.log('Loading history items...');
  
  try {
    // Get history items from localStorage
    const histStr = localStorage.getItem('vodg-vod-history');
    let hist = histStr ? JSON.parse(histStr) : [];
    hist = hist.filter((x) => x != null);

    // Separate viewing and search history
    let viewingHistory = hist.filter(item => item && item.id && !item.id.startsWith('search-'));
    let searchHistory = hist.filter(item => item && item.id && item.id.startsWith('search-'));

    // Limit items
    viewingHistory = viewingHistory.slice(0, 10);
    searchHistory = searchHistory.slice(0, 10);

    // Update lists
    updateHistoryList(viewingHistory, '.viewing-list', '观看');
    updateHistoryList(searchHistory, '.search-list', '搜索');
  } catch (e) {
    console.error('Error loading history:', e);
  }
};

window.updateHistoryList = function(items, selector, type) {
  const listElement = document.querySelector(selector);
  if (!listElement) return;

  listElement.innerHTML = '';

  if (items.length === 0) {
    listElement.innerHTML = `<li class="history-empty">暂无${type}记录</li>`;
  } else {
    items.forEach(item => {
      if (item && item.url && item.title) {
        const li = document.createElement('li');
        li.className = 'history-item';
        li.innerHTML = `<a href="${item.url}" class="history-link">${item.title}</a>`;
        listElement.appendChild(li);
      }
    });
  }
};

window.switchHistoryTab = function(tabName) {
  // Update tab buttons
  document.querySelectorAll('.history-tab').forEach(tab => {
    if (tab.dataset.tab === tabName) {
      tab.classList.add('active');
    } else {
      tab.classList.remove('active');
    }
  });

  // Update tab content
  document.querySelectorAll('.history-tab-content').forEach(content => {
    if (content.id === tabName + '-history') {
      content.classList.add('active');
      content.style.display = 'block';
    } else {
      content.classList.remove('active');
      content.style.display = 'none';
    }
  });

  try {
    localStorage.setItem('history-active-tab', tabName);
  } catch (e) {
    console.error('Error saving tab preference:', e);
  }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  console.log('History script loaded');
  
  // Add event listeners to tab buttons
  document.querySelectorAll('.history-tab').forEach(tab => {
    tab.addEventListener('click', () => {
      switchHistoryTab(tab.dataset.tab);
    });
  });

  // Load history items after a short delay
  setTimeout(() => {
    loadHistoryItems();
  }, 100);
});
