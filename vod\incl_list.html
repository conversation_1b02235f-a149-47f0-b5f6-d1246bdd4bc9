<div class="card-grid">
    {[ for _, vod in ipairs(ctx.vod_list) do ]}
    <div class="media-card">
        <a href="/vod/detail/?id={= vod.id =}" title="{{ vod.title }}">
            <div class="media-poster">
                <img loading="lazy" src="{= vod.poster =}" alt="{{ vod.title }}">
                {[ if vod.douban.score ~= '0.0' then ]}
                <div class="media-badge">{{ vod.douban.score }}</div>
                {[ end ]}
                <div class="media-info">
                    {{ vod.memo
                        :gsub('更新至第', '第')
                        :gsub('更新至', '第')
                        :gsub('更新第', '第')
                        :gsub('更新(%d)', '第%1')
                        :gsub('更新', '')
                        :gsub('连载中 连载到', '第')
                    }}
                </div>
            </div>
            <div class="media-title">{{ vod.title }}</div>
        </a>
    </div>
    {[ end ]}
</div>
<script src="/statics/vod/list_skeleton.js"></script>

<style>
    /* Ensure responsive grid layout for all screen sizes */
    .card-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr)) !important;
        gap: 1rem !important;
    }

    @media (min-width: 400px) {
        .card-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
        }
    }

    @media (min-width: 600px) {
        .card-grid {
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr)) !important;
        }
    }

    @media (min-width: 800px) {
        .card-grid {
            grid-template-columns: repeat(auto-fill, minmax(170px, 1fr)) !important;
        }
    }

    /* Mobile-specific adjustments */
    @media (max-width: 480px) {
        .media-title {
            font-size: var(--font-size-sm);
            line-height: 1.3;
        }

        .media-badge {
            font-size: var(--font-size-xs);
        }

        .media-info {
            font-size: var(--font-size-xs);
        }
    }
</style>
