<!--
    VOD List Component - Refactored and Optimized
    Enhanced with better accessibility, performance, and maintainability
-->

<div class="vod-grid" role="grid" aria-label="影视内容列表">
    {[ for index, vod in ipairs(ctx.vod_list) do ]}
    <article class="vod-card"
             role="gridcell"
             data-vod-id="{= vod.id =}"
             data-vod-type="{= vod.type or 'movie' =}">
        <a href="/vod/detail/?id={= vod.id =}"
           class="vod-link"
           aria-label="查看 {{ vod.title }} 详情">

            <!-- Poster Container -->
            <div class="vod-poster">
                <img src="{= vod.poster =}"
                     alt="{{ vod.title }} 海报"
                     loading="lazy"
                     decoding="async"
                     class="vod-image"
                     onerror="this.src='/statics/images/placeholder.jpg'">

                <!-- Rating Badge -->
                {[ if vod.douban and vod.douban.score and vod.douban.score ~= '0.0' then ]}
                <div class="vod-rating"
                     aria-label="豆瓣评分 {{ vod.douban.score }}">
                    <span class="rating-icon" aria-hidden="true">⭐</span>
                    <span class="rating-value">{{ vod.douban.score }}</span>
                </div>
                {[ end ]}

                <!-- Episode Info -->
                {[ if vod.memo and vod.memo ~= '' then ]}
                <div class="vod-episode"
                     aria-label="剧集信息">
                    {{ vod.memo
                        :gsub('更新至第', '第')
                        :gsub('更新至', '第')
                        :gsub('更新第', '第')
                        :gsub('更新(%d)', '第%1')
                        :gsub('更新', '')
                        :gsub('连载中 连载到', '第')
                        :gsub('全(%d+)集', '共%1集')
                    }}
                </div>
                {[ end ]}

                <!-- Play Overlay -->
                <div class="vod-overlay" aria-hidden="true">
                    <div class="play-button">
                        <svg xmlns="http://www.w3.org/2000/svg"
                             width="24"
                             height="24"
                             viewBox="0 0 24 24"
                             fill="currentColor">
                            <path d="M8 5v14l11-7z"/>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Content Info -->
            <div class="vod-content">
                <h3 class="vod-title">{{ vod.title }}</h3>

                {[ if vod.year or vod.area or vod.type_name then ]}
                <div class="vod-meta">
                    {[ if vod.year and vod.year ~= '' then ]}
                    <span class="meta-item meta-year">{{ vod.year }}</span>
                    {[ end ]}

                    {[ if vod.area and vod.area ~= '' then ]}
                    <span class="meta-item meta-area">{{ vod.area }}</span>
                    {[ end ]}

                    {[ if vod.type_name and vod.type_name ~= '' then ]}
                    <span class="meta-item meta-type">{{ vod.type_name }}</span>
                    {[ end ]}
                </div>
                {[ end ]}
            </div>
        </a>

        <!-- Quick Actions -->
        <div class="vod-actions" role="group" aria-label="快速操作">
            <button class="action-btn favorite-btn"
                    onclick="toggleFavorite('{= vod.id =}', '{{ vod.title }}', '{= vod.poster =}'); event.preventDefault();"
                    aria-label="添加到收藏夹"
                    title="添加到收藏夹">
                <svg xmlns="http://www.w3.org/2000/svg"
                     width="16"
                     height="16"
                     viewBox="0 0 24 24"
                     fill="none"
                     stroke="currentColor"
                     stroke-width="2">
                    <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"/>
                </svg>
            </button>

            <button class="action-btn share-btn"
                    onclick="shareVod('{= vod.id =}', '{{ vod.title }}'); event.preventDefault();"
                    aria-label="分享"
                    title="分享">
                <svg xmlns="http://www.w3.org/2000/svg"
                     width="16"
                     height="16"
                     viewBox="0 0 24 24"
                     fill="none"
                     stroke="currentColor"
                     stroke-width="2">
                    <circle cx="18" cy="5" r="3"/>
                    <circle cx="6" cy="12" r="3"/>
                    <circle cx="18" cy="19" r="3"/>
                    <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"/>
                    <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"/>
                </svg>
            </button>
        </div>
    </article>
    {[ end ]}
</div>

<!-- Loading Skeleton for Better UX -->
<div class="vod-skeleton-container" style="display: none;">
    {[ for i = 1, 12 do ]}
    <div class="vod-skeleton">
        <div class="skeleton-poster skeleton"></div>
        <div class="skeleton-content">
            <div class="skeleton-title skeleton"></div>
            <div class="skeleton-meta skeleton"></div>
        </div>
    </div>
    {[ end ]}
</div>

<script>
    // Enhanced VOD list functionality
    (function() {
        'use strict';

        // Favorite functionality
        window.toggleFavorite = function(vodId, title, poster) {
            if (!vodId || !title) return;

            const favorites = Core.Storage.get('vodg-favorites', []);
            const existingIndex = favorites.findIndex(item => item.id === vodId);
            const button = document.querySelector(`[data-vod-id="${vodId}"] .favorite-btn`);

            if (existingIndex > -1) {
                // Remove from favorites
                favorites.splice(existingIndex, 1);
                if (button) {
                    button.classList.remove('active');
                    button.setAttribute('aria-label', '添加到收藏夹');
                    button.title = '添加到收藏夹';
                }
                Core.Logger.log('Removed from favorites:', title);
            } else {
                // Add to favorites
                favorites.push({
                    id: vodId,
                    title: title,
                    poster: poster,
                    timestamp: Date.now()
                });
                if (button) {
                    button.classList.add('active');
                    button.setAttribute('aria-label', '从收藏夹移除');
                    button.title = '从收藏夹移除';
                }
                Core.Logger.log('Added to favorites:', title);
            }

            Core.Storage.set('vodg-favorites', favorites);

            // Sync to Firebase if user is logged in
            if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
                const userId = window.getCurrentUserId();
                if (userId && window.syncManager) {
                    window.syncManager.syncToFirestore(userId, 'favorites');
                }
            }

            // Dispatch event for other components
            window.dispatchEvent(new CustomEvent('favoritesUpdated', {
                detail: { vodId, title, action: existingIndex > -1 ? 'remove' : 'add' }
            }));
        };

        // Share functionality
        window.shareVod = function(vodId, title) {
            const url = `${window.location.origin}/vod/detail/?id=${vodId}`;

            if (navigator.share) {
                // Use native sharing if available
                navigator.share({
                    title: title,
                    text: `推荐观看：${title}`,
                    url: url
                }).catch(error => {
                    Core.Logger.log('Share failed:', error);
                    fallbackShare(url, title);
                });
            } else {
                fallbackShare(url, title);
            }
        };

        // Fallback share function
        function fallbackShare(url, title) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(() => {
                    showToast('链接已复制到剪贴板');
                }).catch(() => {
                    showShareDialog(url, title);
                });
            } else {
                showShareDialog(url, title);
            }
        }

        // Show share dialog
        function showShareDialog(url, title) {
            const dialog = Core.DOM.createElement('div', {
                className: 'share-dialog',
                innerHTML: `
                    <div class="share-backdrop" onclick="this.parentElement.remove()"></div>
                    <div class="share-content">
                        <h3>分享 ${title}</h3>
                        <input type="text" value="${url}" readonly onclick="this.select()">
                        <div class="share-actions">
                            <button onclick="this.closest('.share-dialog').remove()">关闭</button>
                        </div>
                    </div>
                `
            });

            document.body.appendChild(dialog);
            dialog.querySelector('input').select();
        }

        // Show toast notification
        function showToast(message) {
            const toast = Core.DOM.createElement('div', {
                className: 'toast',
                textContent: message
            });

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.classList.add('show');
            }, 10);

            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.parentElement.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // Initialize favorites state
        function initializeFavorites() {
            const favorites = Core.Storage.get('vodg-favorites', []);
            const favoriteIds = favorites.map(item => item.id);

            document.querySelectorAll('.favorite-btn').forEach(button => {
                const vodCard = button.closest('[data-vod-id]');
                if (vodCard) {
                    const vodId = vodCard.dataset.vodId;
                    if (favoriteIds.includes(vodId)) {
                        button.classList.add('active');
                        button.setAttribute('aria-label', '从收藏夹移除');
                        button.title = '从收藏夹移除';
                    }
                }
            });
        }

        // Lazy loading enhancement
        function enhanceLazyLoading() {
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src || img.src;
                            img.classList.add('loaded');
                            observer.unobserve(img);
                        }
                    });
                }, {
                    rootMargin: '50px'
                });

                document.querySelectorAll('.vod-image').forEach(img => {
                    imageObserver.observe(img);
                });
            }
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            initializeFavorites();
            enhanceLazyLoading();

            // Add keyboard navigation
            document.querySelectorAll('.vod-link').forEach(link => {
                link.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        this.click();
                    }
                });
            });
        });

    })();
</script>

<style>
    /* VOD Grid Layout */
    .vod-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(var(--card-min-width), 1fr));
        gap: var(--spacing-4);
        width: 100%;
        margin-bottom: var(--spacing-6);
    }

    /* VOD Card */
    .vod-card {
        position: relative;
        background-color: var(--bg-card);
        border-radius: var(--media-card-border-radius);
        overflow: hidden;
        transition: transform var(--transition) var(--ease-in-out),
                    box-shadow var(--transition) var(--ease-in-out);
        border: 1px solid var(--border);
        display: flex;
        flex-direction: column;
    }

    .vod-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
    }

    .vod-card:focus-within {
        outline: var(--focus-ring-width) solid var(--focus-ring-color);
        outline-offset: var(--focus-ring-offset);
    }

    /* VOD Link */
    .vod-link {
        display: flex;
        flex-direction: column;
        height: 100%;
        text-decoration: none;
        color: inherit;
    }

    .vod-link:focus {
        outline: none;
    }

    /* Poster Container */
    .vod-poster {
        position: relative;
        width: 100%;
        aspect-ratio: var(--card-aspect-ratio);
        overflow: hidden;
        background-color: var(--bg-darker);
    }

    .vod-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        transition: transform var(--transition-slow) var(--ease-in-out),
                    opacity var(--transition) var(--ease-in-out);
        opacity: 0;
    }

    .vod-image.loaded {
        opacity: 1;
    }

    .vod-card:hover .vod-image {
        transform: scale(1.05);
    }

    /* Rating Badge */
    .vod-rating {
        position: absolute;
        top: var(--spacing-2);
        left: var(--spacing-2);
        background-color: rgba(0, 0, 0, 0.8);
        color: #fbbf24;
        padding: var(--spacing-1) var(--spacing-2);
        border-radius: var(--radius-full);
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-medium);
        display: flex;
        align-items: center;
        gap: var(--spacing-1);
        backdrop-filter: blur(4px);
    }

    .rating-icon {
        font-size: 0.75em;
    }

    /* Episode Info */
    .vod-episode {
        position: absolute;
        bottom: var(--spacing-2);
        left: var(--spacing-2);
        background-color: rgba(37, 99, 235, 0.9);
        color: white;
        padding: var(--spacing-1) var(--spacing-2);
        border-radius: var(--radius);
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-medium);
        backdrop-filter: blur(4px);
    }

    /* Play Overlay */
    .vod-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity var(--transition) var(--ease-in-out);
    }

    .vod-card:hover .vod-overlay {
        opacity: 1;
    }

    .play-button {
        width: 48px;
        height: 48px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--bg-dark);
        transform: scale(0.8);
        transition: transform var(--transition) var(--ease-bounce);
    }

    .vod-card:hover .play-button {
        transform: scale(1);
    }

    /* Content Area */
    .vod-content {
        padding: var(--spacing-3);
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: var(--spacing-2);
    }

    .vod-title {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        color: var(--text-primary);
        margin: 0;
        line-height: var(--line-height-tight);
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .vod-meta {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-1);
        font-size: var(--font-size-xs);
        color: var(--text-tertiary);
    }

    .meta-item {
        padding: var(--spacing-1) var(--spacing-2);
        background-color: var(--bg-darker);
        border-radius: var(--radius-sm);
        white-space: nowrap;
    }

    .meta-year {
        color: var(--secondary);
    }

    .meta-area {
        color: var(--accent);
    }

    .meta-type {
        color: var(--info);
    }

    /* Quick Actions */
    .vod-actions {
        position: absolute;
        top: var(--spacing-2);
        right: var(--spacing-2);
        display: flex;
        gap: var(--spacing-1);
        opacity: 0;
        transition: opacity var(--transition) var(--ease-in-out);
    }

    .vod-card:hover .vod-actions {
        opacity: 1;
    }

    .action-btn {
        width: 32px;
        height: 32px;
        background-color: rgba(0, 0, 0, 0.8);
        border: none;
        border-radius: 50%;
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all var(--transition) var(--ease-in-out);
        backdrop-filter: blur(4px);
    }

    .action-btn:hover {
        background-color: var(--primary);
        transform: scale(1.1);
    }

    .action-btn:focus {
        outline: 2px solid var(--primary);
        outline-offset: 2px;
    }

    .favorite-btn.active {
        background-color: var(--accent);
        color: white;
    }

    /* Skeleton Loading */
    .vod-skeleton-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(var(--card-min-width), 1fr));
        gap: var(--spacing-4);
        width: 100%;
    }

    .vod-skeleton {
        background-color: var(--bg-card);
        border-radius: var(--media-card-border-radius);
        overflow: hidden;
        border: 1px solid var(--border);
    }

    .skeleton-poster {
        width: 100%;
        aspect-ratio: var(--card-aspect-ratio);
        background: var(--bg-card-hover);
    }

    .skeleton-content {
        padding: var(--spacing-3);
        display: flex;
        flex-direction: column;
        gap: var(--spacing-2);
    }

    .skeleton-title {
        height: 1rem;
        background: var(--bg-card-hover);
        border-radius: var(--radius-sm);
    }

    .skeleton-meta {
        height: 0.75rem;
        background: var(--bg-card-hover);
        border-radius: var(--radius-sm);
        width: 60%;
    }

    /* Toast Notifications */
    .toast {
        position: fixed;
        bottom: var(--spacing-6);
        left: 50%;
        transform: translateX(-50%) translateY(100px);
        background-color: var(--bg-dark);
        color: var(--text-primary);
        padding: var(--spacing-3) var(--spacing-4);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        z-index: var(--z-toast);
        transition: transform var(--transition) var(--ease-out);
        border: 1px solid var(--border);
    }

    .toast.show {
        transform: translateX(-50%) translateY(0);
    }

    /* Share Dialog */
    .share-dialog {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: var(--z-modal);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .share-backdrop {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
    }

    .share-content {
        position: relative;
        background-color: var(--bg-card);
        border-radius: var(--radius-lg);
        padding: var(--spacing-6);
        max-width: 400px;
        width: 90%;
        box-shadow: var(--shadow-xl);
    }

    .share-content h3 {
        margin: 0 0 var(--spacing-4) 0;
        color: var(--text-primary);
    }

    .share-content input {
        width: 100%;
        padding: var(--spacing-3);
        border: 1px solid var(--border);
        border-radius: var(--radius);
        background-color: var(--bg-darker);
        color: var(--text-primary);
        margin-bottom: var(--spacing-4);
    }

    .share-actions {
        display: flex;
        justify-content: flex-end;
    }

    .share-actions button {
        padding: var(--spacing-2) var(--spacing-4);
        background-color: var(--primary);
        color: white;
        border: none;
        border-radius: var(--radius);
        cursor: pointer;
    }
</style>
