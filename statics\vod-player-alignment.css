/* 
 * VOD Player Alignment Styles
 * Aligns the player page elements similar to the VOD details page
 * without centering everything
 */

/* Main player wrapper adjustments */
#player-wrapper {
  width: 100%;
  max-width: 1024px;
  margin: 2rem auto 1rem;
  position: relative;
}

/* Title bar alignment - similar to VOD details page */
.vod-title-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1024px;
  margin: 0 auto 1rem;
  padding: 0 1rem;
  gap: 1rem;
  flex-wrap: nowrap;
}

/* Navigation buttons container */
.vod-prev-next {
  display: flex;
  gap: 0.5rem;
  margin-left: auto;
  white-space: nowrap;
}

.vod-prev-next a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  background-color: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-size: 0.9rem;
  text-decoration: none;
  transition: all var(--transition) var(--transition-ease);
}

.vod-prev-next a:hover {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

/* Player controls alignment */
.player-controls {
  max-width: 1024px;
  margin: 0.5rem auto;
  padding: 0 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
}

/* Tab container alignment */
.tabs {
  max-width: 1024px;
  margin: 1rem auto;
  padding: 0 1rem;
}

/* Tab content container */
.tab-content-container {
  max-width: 1024px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Detail box alignment */
.detail-container {
  max-width: 1024px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Recommendations section alignment */
.recommendations {
  max-width: 1024px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.recommendations-title {
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

/* Card grid alignment in recommendations */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 1rem;
}

/* Media card styling */
.media-card {
  transition: transform 0.2s, box-shadow 0.2s;
  border-radius: 12px;
  overflow: hidden;
}

.media-card:hover {
  transform: translateY(-4px) scale(1.025);
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.18);
}

/* Media poster styling */
.media-poster {
  position: relative;
  width: 100%;
  aspect-ratio: 2/3;
  background: #1a1a1f;
  overflow: hidden;
}

.media-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Media badge styling */
.media-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background: #ffb300;
  color: #23232b;
  font-weight: bold;
  border-radius: 8px;
  padding: 0.2em 0.7em;
  font-size: 0.9rem;
}

/* Media info styling */
.media-info {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  background: rgba(40,40,50,0.85);
  color: #fff;
  font-size: 0.9rem;
  border-radius: 6px;
  padding: 0.3em 0.7em;
}

/* Media title styling */
.media-title {
  font-size: 1rem;
  font-weight: 600;
  padding: 0.7rem;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #player-wrapper {
    margin-top: 1rem;
  }
  
  .vod-title-bar,
  .player-controls,
  .tabs,
  .tab-content-container,
  .detail-container,
  .recommendations {
    padding: 0 0.5rem;
  }
  
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
  }
  
  .media-title {
    font-size: 0.9rem;
    padding: 0.5rem;
  }
}
