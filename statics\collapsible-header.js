// Collapsible Header Functionality - Optimized for Performance
document.addEventListener('DOMContentLoaded', function() {
    const header = document.querySelector('.site-header');
    if (!header) return; // Exit if header doesn't exist

    let lastScrollTop = 0;
    let scrollThreshold = 50; // Minimum scroll amount before collapsing
    let ticking = false; // For requestAnimationFrame

    // Function to handle scroll events with requestAnimation<PERSON>rame for better performance
    function handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (!ticking) {
            window.requestAnimationFrame(function() {
                // Add collapsed class when scrolling down past threshold
                if (scrollTop > scrollThreshold) {
                    if (!header.classList.contains('header-collapsed')) {
                        header.classList.add('header-collapsed');
                    }
                } else {
                    if (header.classList.contains('header-collapsed')) {
                        header.classList.remove('header-collapsed');
                    }
                }

                // Store the last scroll position
                lastScrollTop = scrollTop;
                ticking = false;
            });

            ticking = true;
        }
    }

    // Debounced version for better performance
    const debouncedHandleScroll = (function() {
        let timeout;
        return function() {
            clearTimeout(timeout);
            timeout = setTimeout(handleScroll, 10);
        };
    })();

    // Use the appropriate handler based on device performance
    const isLowEndDevice = () => {
        // Simple detection for low-end devices
        return navigator.hardwareConcurrency <= 2 ||
               /Android [4-6]/.test(navigator.userAgent) ||
               /Mobile.*Firefox/.test(navigator.userAgent);
    };

    // Add scroll event listener with the appropriate handler
    if (isLowEndDevice()) {
        window.addEventListener('scroll', debouncedHandleScroll, { passive: true });
    } else {
        window.addEventListener('scroll', handleScroll, { passive: true });
    }

    // Initial check in case page is loaded scrolled down
    handleScroll();

    // Check for reduced motion preference
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        // Add a class to disable animations
        header.classList.add('reduced-motion');
    }
});
