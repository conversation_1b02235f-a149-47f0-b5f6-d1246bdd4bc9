/**
 * Firebase Synchronization Manager
 * Handles synchronization between localStorage and Firebase Firestore
 * with debouncing, offline support, and improved conflict resolution
 * Optimized for performance with lazy loading and batched operations
 */

// Sync manager class
class FirebaseSyncManager {
  constructor() {
    // Configuration
    this.syncDelay = 10000; // Debounce delay in ms (10 seconds)
    this.maxRetries = 3;   // Maximum number of retries for failed syncs
    this.maxHistoryItems = 20; // Maximum number of history items to sync
    this.syncInProgress = false; // Flag to track if sync is in progress

    // State
    this.pendingSyncs = new Map(); // Map of pending sync operations
    this.syncTimers = new Map();   // Map of sync timers for debouncing
    this.isOnline = navigator.onLine;
    this.offlineQueue = [];        // Queue for operations when offline
    this.isInitialSync = false;    // Flag to track initial sync on login
    this.hasFirebaseData = false;  // Flag to track if user has Firebase data
    this.isDirectSyncCall = false; // Flag to track direct sync calls from syncAll
    this.syncPriority = ['watchProgress', 'favorites', 'viewingHistory', 'searchHistory']; // Sync priority order
    this.firestoreListeners = {}; // Store active Firestore listeners
    this.periodicRefreshTimer = null; // Timer for periodic refresh of data from Firestore
    this.deletedHistoryItems = new Set(); // Track deleted history items to prevent them from reappearing

    // Bind methods
    this.syncToFirestore = this.syncToFirestore.bind(this);
    this.syncFromFirestore = this.syncFromFirestore.bind(this);
    this.syncAll = this.syncAll.bind(this);
    this.handleOnlineStatusChange = this.handleOnlineStatusChange.bind(this);
    this.showSyncProgress = this.showSyncProgress.bind(this);
    this.hideSyncProgress = this.hideSyncProgress.bind(this);
    this.setupRealtimeListeners = this.setupRealtimeListeners.bind(this);
    this.removeRealtimeListeners = this.removeRealtimeListeners.bind(this);

    // Set up online/offline event listeners
    window.addEventListener('online', this.handleOnlineStatusChange);
    window.addEventListener('offline', this.handleOnlineStatusChange);

    // Set up data change event listeners
    this.setupEventListeners();

    // Set up realtime listeners if user is already logged in
    if (this.isUserLoggedIn()) {
      const userId = this.getCurrentUserId();
      if (userId) {
        this.setupRealtimeListeners(userId);
      }
    }

    // Load deleted history items from localStorage if available
    try {
      const deletedItems = localStorage.getItem('vodg-deleted-history-items');
      if (deletedItems) {
        this.deletedHistoryItems = new Set(JSON.parse(deletedItems));
      }
    } catch (error) {
      console.error('Error loading deleted history items:', error);
    }

    console.log('Firebase Sync Manager initialized');
  }

  /**
   * Show sync progress indicator
   * @param {string} message - Message to display
   * @param {number} progress - Progress percentage (0-100)
   */
  showSyncProgress(message, progress = 0) {
    // Skip showing progress indicator - sync will happen silently in the background
    return;
    
    // Original code commented out below
    /*
    // Check if progress indicator already exists
    let progressIndicator = document.getElementById('firebase-sync-progress');

    if (!progressIndicator) {
      // Create progress indicator
      progressIndicator = document.createElement('div');
      progressIndicator.id = 'firebase-sync-progress';
      progressIndicator.style.position = 'fixed';
      progressIndicator.style.top = '0';
      progressIndicator.style.left = '0';
      progressIndicator.style.right = '0';
      progressIndicator.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
      progressIndicator.style.color = 'white';
      progressIndicator.style.padding = '8px 16px';
      progressIndicator.style.fontSize = '14px';
      progressIndicator.style.zIndex = '9999';
      progressIndicator.style.display = 'flex';
      progressIndicator.style.justifyContent = 'space-between';
      progressIndicator.style.alignItems = 'center';
      progressIndicator.style.transition = 'opacity 0.3s';

      // Add progress bar
      const progressBarContainer = document.createElement('div');
      progressBarContainer.style.flex = '1';
      progressBarContainer.style.marginLeft = '10px';
      progressBarContainer.style.height = '4px';
      progressBarContainer.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
      progressBarContainer.style.borderRadius = '2px';

      const progressBar = document.createElement('div');
      progressBar.id = 'firebase-sync-progress-bar';
      progressBar.style.height = '100%';
      progressBar.style.backgroundColor = '#f97316';
      progressBar.style.borderRadius = '2px';
      progressBar.style.width = `${progress}%`;
      progressBar.style.transition = 'width 0.3s';

      progressBarContainer.appendChild(progressBar);

      // Add message
      const messageEl = document.createElement('div');
      messageEl.id = 'firebase-sync-message';
      messageEl.textContent = message;

      progressIndicator.appendChild(messageEl);
      progressIndicator.appendChild(progressBarContainer);

      document.body.appendChild(progressIndicator);
    } else {
      // Update existing progress indicator
      const messageEl = document.getElementById('firebase-sync-message');
      const progressBar = document.getElementById('firebase-sync-progress-bar');

      if (messageEl) messageEl.textContent = message;
      if (progressBar) progressBar.style.width = `${progress}%`;

      // Make sure it's visible
      progressIndicator.style.display = 'flex';
      progressIndicator.style.opacity = '1';
    }
    */
  }

  /**
   * Hide sync progress indicator
   */
  hideSyncProgress() {
    // No need to hide anything since we're not showing progress
    return;

    const progressIndicator = document.getElementById('firebase-sync-progress');

    if (progressIndicator) {
      // Fade out
      progressIndicator.style.opacity = '0';

      // Remove after animation
      setTimeout(() => {
        if (progressIndicator.parentNode) {
          progressIndicator.parentNode.removeChild(progressIndicator);
        }
      }, 300);
    }
  }

  /**
   * Set up event listeners for data changes
   */
  setupEventListeners() {
    window.addEventListener('favoritesUpdated', () => {
      // Only sync if user is logged in and not in initial sync process
      // Also, don't sync if we know user has Firebase data and we're still in initial sync
      if (this.isUserLoggedIn() && (!this.isInitialSync || !this.hasFirebaseData)) {
        this.debouncedSync('favorites');
      }
    });

    window.addEventListener('viewingHistoryUpdated', () => {
      if (this.isUserLoggedIn() && (!this.isInitialSync || !this.hasFirebaseData)) {
        this.debouncedSync('viewingHistory');
      }
    });

    window.addEventListener('searchHistoryUpdated', () => {
      if (this.isUserLoggedIn() && (!this.isInitialSync || !this.hasFirebaseData)) {
        this.debouncedSync('searchHistory');
      }
    });

    window.addEventListener('watchProgressUpdated', () => {
      if (this.isUserLoggedIn() && (!this.isInitialSync || !this.hasFirebaseData)) {
        this.debouncedSync('watchProgress');
      }
    });

    // Listen for the historyUpdated event to update the history UI
    window.addEventListener('historyUpdated', (e) => {
      console.log('Received historyUpdated event, updating history UI');
      
      // If detail data is available, use it
      if (e.detail && Array.isArray(e.detail)) {
        try {
          // Split the combined history into viewing and search history
          const viewingHistory = e.detail.filter(item => item && item.id && !item.id.startsWith('search-'));
          const searchHistory = e.detail.filter(item => item && item.id && item.id.startsWith('search-'));
          
          // Update both history lists if the updateHistoryList function is available
          if (typeof window.updateHistoryList === 'function') {
            // Update viewing history list
            window.updateHistoryList(viewingHistory, '.viewing-list', '观看');
            
            // Update search history list
            window.updateHistoryList(searchHistory, '.search-list', '搜索');
            
            console.log('History UI updated successfully');
          }
        } catch (err) {
          console.error('Error updating history UI from event:', err);
        }
      }
    });

    // Listen for auth state changes to setup/remove realtime listeners
    window.addEventListener('userLoggedIn', (e) => {
      if (e.detail && e.detail.userId) {
        this.setupRealtimeListeners(e.detail.userId);
      }
    });

    window.addEventListener('userLoggedOut', () => {
      this.removeRealtimeListeners();
    });
  }

  /**
   * Check if user is logged in
   * @returns {boolean} True if user is logged in
   */
  isUserLoggedIn() {
    // First try to use the global utility function
    if (typeof window.isUserLoggedIn === 'function') {
      return window.isUserLoggedIn();
    }
    // Fallback to direct check
    return window.firebaseAuth && window.firebaseAuth.currentUser !== null;
  }

  /**
   * Get current user ID
   * @returns {string|null} User ID or null if not logged in
   */
  getCurrentUserId() {
    // First try to use the global utility function
    if (typeof window.getCurrentUserId === 'function') {
      return window.getCurrentUserId();
    }
    // Fallback to direct check
    return window.firebaseAuth && window.firebaseAuth.currentUser ? window.firebaseAuth.currentUser.uid : null;
  }

  /**
   * Handle online/offline status changes
   */
  handleOnlineStatusChange() {
    const wasOffline = !this.isOnline;
    this.isOnline = navigator.onLine;

    console.log(`Network status changed: ${this.isOnline ? 'online' : 'offline'}`);

    // If we're back online and had pending operations, process them
    if (this.isOnline && wasOffline && this.offlineQueue.length > 0) {
      console.log(`Processing ${this.offlineQueue.length} operations from offline queue`);

      // Process offline queue
      const operations = [...this.offlineQueue];
      this.offlineQueue = [];

      operations.forEach(op => {
        this.debouncedSync(op.dataType);
      });
    }
  }

  /**
   * Debounced sync to Firestore
   * @param {string} dataType - Type of data to sync
   */
  debouncedSync(dataType) {
    const userId = this.getCurrentUserId();
    if (!userId) return;

    // Cancel any existing timer for this data type
    if (this.syncTimers.has(dataType)) {
      clearTimeout(this.syncTimers.get(dataType));
    }

    // Set a new timer
    const timerId = setTimeout(() => {
      this.syncToFirestore(userId, dataType);
      this.syncTimers.delete(dataType);
    }, this.syncDelay);

    this.syncTimers.set(dataType, timerId);

    console.log(`Scheduled sync for ${dataType} in ${this.syncDelay}ms`);
  }

  /**
   * Get data from localStorage
   * @param {string} dataType - Type of data to get
   * @returns {any} Data from localStorage
   */
  getLocalData(dataType) {
    try {
      if (dataType === 'favorites') {
        return JSON.parse(localStorage.getItem('vodg-favorites') || '[]');
      } else if (dataType === 'viewingHistory') {
        return JSON.parse(localStorage.getItem('vodg-vod-history') || '[]')
          .filter(item => item && item.id && !item.id.startsWith('search-'));
      } else if (dataType === 'searchHistory') {
        return JSON.parse(localStorage.getItem('vodg-vod-history') || '[]')
          .filter(item => item && item.id && item.id.startsWith('search-'));
      } else if (dataType === 'watchProgress') {
        return JSON.parse(localStorage.getItem('vodg-play') || '{}');
      } else {
        console.warn(`Unknown data type: ${dataType}`);
        return null;
      }
    } catch (error) {
      console.error(`Error getting local data for ${dataType}:`, error);
      return null;
    }
  }

  /**
   * Sync data from localStorage to Firestore
   * @param {string} userId - User ID
   * @param {string} dataType - Type of data to sync
   * @param {number} retryCount - Current retry count
   * @returns {Promise<void>}
   */
  async syncToFirestore(userId, dataType, retryCount = 0) {
    if (!userId || !this.isOnline) {
      if (!this.isOnline) {
        console.log(`Cannot sync ${dataType} to Firestore: User is offline`);
        this.queueOfflineOperation(() => this.syncToFirestore(userId, dataType));
      }
      return Promise.resolve();
    }

    // Set flag to indicate we're in a direct sync operation
    this.isDirectSyncCall = true;

    console.log(`Syncing ${dataType} to Firestore`);
    this.showSyncProgress(`Syncing ${dataType}...`);

    try {
      // Get local data
      const localData = this.getLocalData(dataType);
      
      // For history, filter out deleted items
      let dataToSync = localData;
      if ((dataType === 'viewingHistory' || dataType === 'searchHistory') && 
          this.deletedHistoryItems && this.deletedHistoryItems.size > 0) {
        dataToSync = localData.filter(item => item && item.id && !this.isHistoryItemDeleted(item.id));
        console.log(`Filtered out ${localData.length - dataToSync.length} deleted items from ${dataType}`);
      }

      // Reference to the user's document
      const userDocRef = window.doc(window.db, 'userData', userId);

      // Check if document exists and needs to be merged with existing data
      const userDoc = await window.getDoc(userDocRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();
        let firestoreData = userData[dataType];

        // If we already have Firestore data for this type, merge it
        if (firestoreData) {
          console.log(`Merging local ${dataType} with Firestore data`);

          // For different data types, use different merging strategies
          if (dataType === 'watchProgress') {
            // For watch progress, merge the objects
            dataToSync = { ...firestoreData };
            
            // Add local data, but only if it's newer than what's in Firestore
            Object.keys(localData).forEach(key => {
              const localItem = localData[key];
              const firestoreItem = firestoreData[key];
              
              if (!firestoreItem || !firestoreItem.timestamp || (localItem.timestamp > firestoreItem.timestamp)) {
                dataToSync[key] = localItem;
              }
            });
          } 
          else if (dataType === 'favorites') {
            // For favorites, use a Map to avoid duplicates
            const favMap = new Map();
            
            // First add Firestore favorites
            if (Array.isArray(firestoreData)) {
              firestoreData.forEach(item => {
                if (item && item.id) {
                  favMap.set(item.id, item);
                }
              });
            }
            
            // Then add local favorites, but only if they're more recent
            if (Array.isArray(localData)) {
              localData.forEach(item => {
                if (item && item.id) {
                  const firestoreItem = favMap.get(item.id);
                  // Use lastWatched for comparison
                  if (!firestoreItem || (item.lastWatched > firestoreItem.lastWatched)) {
                    favMap.set(item.id, item);
                  }
                }
              });
            }
            
            // Convert map back to array and sort by lastWatched (most recent first)
            dataToSync = Array.from(favMap.values());
            dataToSync.sort((a, b) => ((b.lastWatched || 0) - (a.lastWatched || 0)));
          } 
          else if (dataType === 'viewingHistory' || dataType === 'searchHistory') {
            // For history, use a map to merge by ID
            const mergeMap = new Map();
            
            // Add Firestore history to the map, skipping deleted items
            if (Array.isArray(firestoreData)) {
              firestoreData.forEach(item => {
                if (item && item.id && !this.isHistoryItemDeleted(item.id)) {
                  mergeMap.set(item.id, item);
                }
              });
            }
            
            // Update with local history if items are newer
            if (Array.isArray(dataToSync)) {
              dataToSync.forEach(item => {
                if (item && item.id) {
                  const firestoreItem = mergeMap.get(item.id);
                  
                  // Use timestamp for history comparison
                  if (!firestoreItem || (item.timestamp > firestoreItem.timestamp)) {
                    mergeMap.set(item.id, item);
                  }
                }
              });
            }
            
            // Convert map back to array and sort
            dataToSync = Array.from(mergeMap.values());
            dataToSync.sort((a, b) => ((b.timestamp || 0) - (a.timestamp || 0)));
            
            // Limit size to prevent too large documents
            if (dataToSync.length > this.maxHistoryItems) {
              dataToSync = dataToSync.slice(0, this.maxHistoryItems);
            }
          }
        }
        
        // Update document with merged data
        await window.updateDoc(userDocRef, {
          [dataType]: dataToSync,
          lastUpdated: window.serverTimestamp()
        });
      } else {
        // Create new document
        await window.setDoc(userDocRef, {
          [dataType]: dataToSync,
          lastUpdated: window.serverTimestamp()
        });
      }

      console.log(`Successfully synced ${dataType} to Firestore with timestamp-based conflict resolution`);
      this.hideSyncProgress();
      
      // Record the sync time
      localStorage.setItem('vodg-last-sync-time', Date.now().toString());
      
      // If initial sync, flag this data type as synced
      if (this.isInitialSync) {
        this.markDataTypeSynced(dataType);
      }
      
      // Clear flag
      this.isDirectSyncCall = false;
      
      return Promise.resolve();
    } catch (error) {
      console.error(`Error syncing ${dataType} to Firestore:`, error);
      this.hideSyncProgress();
      
      // Clear flag
      this.isDirectSyncCall = false;
      
      // Retry logic
      if (retryCount < this.maxRetries) {
        console.log(`Retrying sync ${dataType} (${retryCount + 1}/${this.maxRetries})`);
        // Exponential backoff
        const delay = Math.pow(2, retryCount) * 1000 + Math.random() * 1000;
        
        setTimeout(() => {
          this.syncToFirestore(userId, dataType, retryCount + 1)
            .catch(e => console.error(`Retry ${retryCount + 1} failed:`, e));
        }, delay);
      } else {
        console.error(`Max retries reached for syncing ${dataType}`);
        this.showSyncError(`Failed to sync ${dataType} after multiple attempts`);
      }
      
      return Promise.reject(error);
    }
  }

  /**
   * Sync data from Firestore to localStorage
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async syncFromFirestore(userId) {
    if (!userId || !this.isOnline) return Promise.resolve();

    try {
      console.log(`Checking if sync from Firestore is needed for user ${userId}`);

      // Check if local data exists for each data type
      const localFavorites = JSON.parse(localStorage.getItem('vodg-favorites') || '[]');
      const localHistory = JSON.parse(localStorage.getItem('vodg-vod-history') || '[]');
      const localPlayCache = JSON.parse(localStorage.getItem('vodg-play') || '{}');

      // Check if user has any local data
      const hasLocalFavorites = localFavorites.length > 0;
      const hasLocalHistory = localHistory.length > 0;
      const hasLocalWatchProgress = Object.keys(localPlayCache).length > 0;
      const hasLocalData = hasLocalFavorites || hasLocalHistory || hasLocalWatchProgress;

      // If user has any local data and we're in initial sync, don't sync from Firebase
      // This is to preserve local data during login
      if (hasLocalData && this.isInitialSync) {
        console.log('User has local data and we are in initial sync, preserving local data');
        return Promise.resolve();
      }

      // If user has any local data outside of initial sync, don't sync from Firebase
      // This is for normal operation after login
      if (hasLocalData && !this.isInitialSync) {
        console.log('User has local data, skipping sync from Firestore');
        return Promise.resolve();
      }

      // If we get here, user has no local data, so we should sync from Firebase
      console.log('No local data found, syncing from Firestore for user', userId);

      // Get Firestore references
      const db = window.db; // Assuming db is available globally
      if (!db) {
        console.error('Firestore not initialized');
        return Promise.resolve();
      }

      const userDocRef = window.doc(db, 'userData', userId);
      const userDoc = await window.getDoc(userDocRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();

        // Sync favorites
        if (userData.favorites) {
          // Sort favorites by lastWatched (most recent first) if the property exists
          try {
            const favorites = userData.favorites;
            if (Array.isArray(favorites)) {
              favorites.sort((a, b) => {
                return ((b.lastWatched || 0) - (a.lastWatched || 0));
              });
            }
            localStorage.setItem('vodg-favorites', JSON.stringify(favorites));
            console.log('Favorites synced from Firestore:', favorites.length, 'items');

            // Update UI if favorites component is initialized
            if (typeof window.updateFavoritesList === 'function') {
              window.updateFavoritesList(favorites);
            }
          } catch (e) {
            console.error('Error sorting favorites from Firestore:', e);
            // Fallback to original behavior
            localStorage.setItem('vodg-favorites', JSON.stringify(userData.favorites));
            console.log('Favorites synced from Firestore (unsorted):', userData.favorites.length, 'items');

            // Update UI if favorites component is initialized
            if (typeof window.updateFavoritesList === 'function') {
              window.updateFavoritesList(userData.favorites);
            }
          }
        }

        // Sync viewing history and search history
        const viewingHistory = userData.viewingHistory || [];
        const searchHistory = userData.searchHistory || [];

        if (viewingHistory.length > 0 || searchHistory.length > 0) {
          // Combine viewing and search history
          const combinedHistory = [...viewingHistory, ...searchHistory];

          localStorage.setItem('vodg-vod-history', JSON.stringify(combinedHistory));
          console.log('History synced from Firestore:', {
            viewingHistory: viewingHistory.length,
            searchHistory: searchHistory.length,
            total: combinedHistory.length
          });
        }

        // Sync watch progress
        if (userData.watchProgress) {
          localStorage.setItem('vodg-play', JSON.stringify(userData.watchProgress));
          console.log('Watch progress synced from Firestore');
        }
      } else {
        console.log('No user data found in Firestore');
        // We'll let syncAll handle whether to sync local data to Firebase
      }

      // Return a resolved promise to indicate completion
      return Promise.resolve();
    } catch (error) {
      console.error('Error syncing data from Firestore:', error);

      // Show error message to user
      this.showErrorMessage('同步数据错误: ' + error.message);

      return Promise.reject(error);
    }
  }

  /**
   * Sync all data types
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async syncAll(userId) {
    if (!userId) return;

    // Prevent multiple syncs from running simultaneously
    if (this.syncInProgress) {
      console.log('Sync already in progress, skipping');
      return;
    }

    this.syncInProgress = true;
    this.showSyncProgress('正在同步数据...', 5);

    try {
      // Set the initial sync flag to prevent automatic syncing during login
      this.isInitialSync = true;

      // First check if user has Firebase data
      const db = window.db;
      if (!db) {
        console.error('Firestore not initialized');
        this.isInitialSync = false;
        this.syncInProgress = false;
        this.hideSyncProgress();
        return Promise.resolve();
      }

      // Set up realtime listeners if they're not already set up
      this.setupRealtimeListeners(userId);

      this.showSyncProgress('正在检查用户数据...', 10);

      // Use a timeout to allow UI to update before continuing with heavy operations
      await new Promise(resolve => setTimeout(resolve, 0));

      const userDocRef = window.doc(db, 'userData', userId);
      const userDoc = await window.getDoc(userDocRef);
      this.hasFirebaseData = userDoc.exists();

      this.showSyncProgress('正在分析本地数据...', 20);

      // Use a timeout to allow UI to update before continuing with heavy operations
      await new Promise(resolve => setTimeout(resolve, 0));

      // Check if local data exists for each data type - use try/catch for safety
      let localFavorites = [];
      let localHistory = [];
      let localPlayCache = {};

      try {
        localFavorites = JSON.parse(localStorage.getItem('vodg-favorites') || '[]');
      } catch (e) {
        console.error('Error parsing favorites:', e);
        localFavorites = [];
      }

      try {
        localHistory = JSON.parse(localStorage.getItem('vodg-vod-history') || '[]');
      } catch (e) {
        console.error('Error parsing history:', e);
        localHistory = [];
      }

      try {
        localPlayCache = JSON.parse(localStorage.getItem('vodg-play') || '{}');
      } catch (e) {
        console.error('Error parsing play cache:', e);
        localPlayCache = {};
      }

      // Check if user has any local data
      const hasLocalFavorites = localFavorites.length > 0;
      const hasLocalHistory = localHistory.length > 0;
      const hasLocalWatchProgress = Object.keys(localPlayCache).length > 0;
      const hasLocalData = hasLocalFavorites || hasLocalHistory || hasLocalWatchProgress;

      console.log('Sync status:', {
        hasFirebaseData: this.hasFirebaseData,
        hasLocalData: hasLocalData,
        hasLocalFavorites: hasLocalFavorites,
        hasLocalHistory: hasLocalHistory,
        hasLocalWatchProgress: hasLocalWatchProgress
      });

      // Prioritize loading watch progress first as it's most important for user experience
      if (this.hasFirebaseData) {
        this.showSyncProgress('正在同步观看进度...', 30);

        // Load watch progress first (most important for user experience)
        if (userDoc.exists() && userDoc.data().watchProgress) {
          localStorage.setItem('vodg-play', JSON.stringify(userDoc.data().watchProgress));
          console.log('Watch progress loaded from Firestore');
        }

        // Use a timeout to allow UI to update before continuing with heavy operations
        await new Promise(resolve => setTimeout(resolve, 0));

        this.showSyncProgress('正在同步收藏夹...', 50);

        // Then load favorites (second most important)
        if (userDoc.exists() && userDoc.data().favorites) {
          try {
            const favorites = userDoc.data().favorites;
            if (Array.isArray(favorites)) {
              favorites.sort((a, b) => {
                return ((b.lastWatched || 0) - (a.lastWatched || 0));
              });
            }
            localStorage.setItem('vodg-favorites', JSON.stringify(favorites));
            console.log('Favorites loaded from Firestore:', favorites.length, 'items');

            // Update UI if favorites component is initialized
            if (typeof window.updateFavoritesList === 'function') {
              window.updateFavoritesList(favorites);
            }
          } catch (e) {
            console.error('Error sorting favorites from Firestore:', e);
          }
        }

        // Use a timeout to allow UI to update before continuing with heavy operations
        await new Promise(resolve => setTimeout(resolve, 0));

        this.showSyncProgress('正在同步历史记录...', 70);

        // Finally load history (least critical but potentially largest)
        if (userDoc.exists()) {
          const userData = userDoc.data();
          const firebaseViewingHistory = userData.viewingHistory || [];
          const firebaseSearchHistory = userData.searchHistory || [];

          // If we have local history, we need to merge it with Firebase history
          if (hasLocalHistory) {
            console.log('Merging local history with Firebase history');

            // Separate local viewing and search history
            const localViewingHistory = localHistory.filter(item => item && item.id && !item.id.startsWith('search-'));
            const localSearchHistory = localHistory.filter(item => item && item.id && item.id.startsWith('search-'));

            // Create maps to remove duplicates and keep the most recent items
            const viewingHistoryMap = new Map();
            const searchHistoryMap = new Map();

            // Process Firebase viewing history
            firebaseViewingHistory.forEach(item => {
              if (item && item.id) {
                viewingHistoryMap.set(item.id, item);
              }
            });

            // Process local viewing history (will overwrite Firebase items with same ID)
            localViewingHistory.forEach(item => {
              if (item && item.id) {
                viewingHistoryMap.set(item.id, item);
              }
            });

            // Process Firebase search history
            firebaseSearchHistory.forEach(item => {
              if (item && item.id) {
                searchHistoryMap.set(item.id, item);
              }
            });

            // Process local search history (will overwrite Firebase items with same ID)
            localSearchHistory.forEach(item => {
              if (item && item.id) {
                searchHistoryMap.set(item.id, item);
              }
            });

            // Convert maps back to arrays and limit size
            let mergedViewingHistory = Array.from(viewingHistoryMap.values());
            let mergedSearchHistory = Array.from(searchHistoryMap.values());

            // Sort by timestamp (most recent first) and limit size
            mergedViewingHistory.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
            mergedSearchHistory.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));

            // Limit size to improve performance
            mergedViewingHistory = mergedViewingHistory.slice(0, this.maxHistoryItems);
            mergedSearchHistory = mergedSearchHistory.slice(0, this.maxHistoryItems);

            // Combine the merged histories
            const combinedHistory = [...mergedViewingHistory, ...mergedSearchHistory];

            localStorage.setItem('vodg-vod-history', JSON.stringify(combinedHistory));

            // Also update Firebase with the merged history (in background)
            this.isDirectSyncCall = true;
            window.updateDoc(userDocRef, {
              viewingHistory: mergedViewingHistory,
              searchHistory: mergedSearchHistory,
              lastUpdated: window.serverTimestamp()
            }).catch(error => {
              console.error('Error updating merged history in Firebase:', error);
            });
            this.isDirectSyncCall = false;
          } else {
            // If no local history, just use Firebase history (limited to improve performance)
            const limitedViewingHistory = firebaseViewingHistory.slice(0, this.maxHistoryItems);
            const limitedSearchHistory = firebaseSearchHistory.slice(0, this.maxHistoryItems);
            const combinedHistory = [...limitedViewingHistory, ...limitedSearchHistory];

            localStorage.setItem('vodg-vod-history', JSON.stringify(combinedHistory));
          }
        }
      } else if (hasLocalData) {
        // If user has no Firebase data but has local data, sync local data to Firebase
        this.showSyncProgress('正在上传本地数据到云端...', 50);

        // Directly sync without debouncing to ensure it happens immediately
        // Set a flag to indicate this is a direct call from syncAll
        this.isDirectSyncCall = true;

        try {
          // Prepare data for update
          const userData = {};
          
          // Add watch progress
          if (hasLocalWatchProgress) {
            userData.watchProgress = localPlayCache;
          }
          
          // Add favorites
          if (hasLocalFavorites) {
            userData.favorites = localFavorites;
          }
          
          // Add history (viewing and search)
          if (hasLocalHistory) {
            const viewingHistory = localHistory.filter(item => item && item.id && !item.id.startsWith('search-'));
            const searchHistory = localHistory.filter(item => item && item.id && item.id.startsWith('search-'));
            
            userData.viewingHistory = viewingHistory;
            userData.searchHistory = searchHistory;
          }
          
          // Add timestamp
          userData.lastUpdated = window.serverTimestamp();
          
          // Set the document
          await window.setDoc(userDocRef, userData);
          
          this.isDirectSyncCall = false;
          console.log('Local data synced to Firebase using setDoc');
        } catch (error) {
          console.error('Error syncing local data to Firebase:', error);
          this.isDirectSyncCall = false;
        }
      } else {
        // If user has neither Firebase data nor local data, nothing to do
        console.log('User has neither Firebase data nor local data, nothing to sync');
      }

      this.showSyncProgress('同步完成', 100);

      // Reset the initial sync flag
      this.isInitialSync = false;

      // Hide progress indicator after a short delay
      setTimeout(() => {
        this.hideSyncProgress();
        this.syncInProgress = false;
      }, 500);

      return Promise.resolve();
    } catch (error) {
      console.error('Error in syncAll:', error);
      // Make sure to reset the flag even if there's an error
      this.isInitialSync = false;
      this.syncInProgress = false;

      // Show error in progress indicator
      this.showSyncProgress('同步出错: ' + error.message, 100);

      // Hide progress indicator after a delay
      setTimeout(() => {
        this.hideSyncProgress();
      }, 3000);

      return Promise.reject(error);
    }
  }

  /**
   * Load data from Firestore to localStorage without checking if local data exists
   * This is used when we want to force loading Firebase data
   * Optimized for performance with progressive loading and size limits
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async loadFromFirestore(userId, localHistory = null) {
    if (!userId || !this.isOnline) return Promise.resolve();

    console.log('Loading user data from Firestore');
    this.showSyncProgress('Loading your data...');

    try {
      // Check if the document exists
      const userDocRef = window.doc(window.db, 'userData', userId);
      const userDoc = await window.getDoc(userDocRef);

      if (!userDoc.exists()) {
        console.log('No user data found in Firestore');
        this.hideSyncProgress();
        return Promise.resolve();
      }

      this.hasFirebaseData = true;
      const userData = userDoc.data();
      console.log('Found user data in Firestore');

      // Flag to track if we have local history to merge
      const hasLocalHistory = !!localHistory && localHistory.length > 0;

      // Process data types with priority
      // Favorites first (quick and important)
      if (userData.favorites) {
        // Create a map to manage favorites
        const favMap = new Map();
        
        // Add Firebase favorites
        userData.favorites.forEach(item => {
          if (item && item.id) {
            favMap.set(item.id, item);
          }
        });
        
        // Check for local favorites data
        const localFavorites = JSON.parse(localStorage.getItem('vodg-favorites') || '[]');
        
        // If we have local favorites, merge them
        if (localFavorites.length > 0) {
          console.log('Merging local favorites with Firebase favorites');
          
          // Add local favorites, but only if they're more recent
          localFavorites.forEach(item => {
            if (item && item.id) {
              const firebaseItem = favMap.get(item.id);
              // Use lastWatched timestamp for comparison
              if (!firebaseItem || (item.lastWatched > firebaseItem.lastWatched)) {
                favMap.set(item.id, item);
              }
            }
          });
        }
        
        // Convert map back to array and sort
        let mergedFavorites = Array.from(favMap.values());
        mergedFavorites.sort((a, b) => ((b.lastWatched || 0) - (a.lastWatched || 0)));
        
        // Save to localStorage
        localStorage.setItem('vodg-favorites', JSON.stringify(mergedFavorites));
        
        // Also update Firestore to ensure it has the latest data
        await window.updateDoc(userDocRef, {
          favorites: mergedFavorites,
          lastUpdated: window.serverTimestamp()
        });
        
        console.log('Favorites loaded and synchronized');
      }

      // Yield for UI thread
      await new Promise(resolve => setTimeout(resolve, 0));

      // Then load watch progress (also important for resuming videos)
      if (userData.watchProgress) {
        // Create a merged object
        const mergedProgress = { ...userData.watchProgress };
        
        // Check for local watch progress
        const localProgress = JSON.parse(localStorage.getItem('vodg-play') || '{}');
        
        // If we have local progress data, merge it
        if (Object.keys(localProgress).length > 0) {
          console.log('Merging local watch progress with Firebase watch progress');
          
          // For each local entry, check if it's newer than Firebase
          Object.keys(localProgress).forEach(key => {
            const localItem = localProgress[key];
            const firebaseItem = mergedProgress[key];
            
            // Use timestamp for comparison
            if (!firebaseItem || !firebaseItem.timestamp || (localItem.timestamp > firebaseItem.timestamp)) {
              mergedProgress[key] = localItem;
            }
          });
        }
        
        // Save to localStorage
        localStorage.setItem('vodg-play', JSON.stringify(mergedProgress));
        
        // Also update Firestore to ensure it has the latest data
        await window.updateDoc(userDocRef, {
          watchProgress: mergedProgress,
          lastUpdated: window.serverTimestamp()
        });
        
        console.log('Watch progress loaded and synchronized');
      }

      // Yield for UI thread
      await new Promise(resolve => setTimeout(resolve, 0));

      // Load viewing history and search history
      const firebaseViewingHistory = userData.viewingHistory || [];
      const firebaseSearchHistory = userData.searchHistory || [];

      // Filter out any deleted items from Firebase history
      const filteredViewingHistory = firebaseViewingHistory.filter(
        item => item && item.id && !this.isHistoryItemDeleted(item.id)
      );
      
      const filteredSearchHistory = firebaseSearchHistory.filter(
        item => item && item.id && !this.isHistoryItemDeleted(item.id)
      );

      // If we have local history, we need to merge it with Firebase history
      if (localHistory) {
        console.log('Merging local history with Firebase history');

        // Separate local viewing and search history
        const localViewingHistory = localHistory.filter(item => item && item.id && !item.id.startsWith('search-'));
        const localSearchHistory = localHistory.filter(item => item && item.id && item.id.startsWith('search-'));

        // Create maps to remove duplicates and keep the most recent items
        const viewingHistoryMap = new Map();
        const searchHistoryMap = new Map();

        // Process filtered Firebase viewing history
        filteredViewingHistory.forEach(item => {
          if (item && item.id) {
            viewingHistoryMap.set(item.id, item);
          }
        });

        // Process local viewing history (will overwrite Firebase items with same ID)
        localViewingHistory.forEach(item => {
          if (item && item.id) {
            // Skip deleted items
            if (this.isHistoryItemDeleted(item.id)) {
              return;
            }
            
            const firebaseItem = viewingHistoryMap.get(item.id);
            // Only use local item if it's newer
            if (!firebaseItem || (item.timestamp > firebaseItem.timestamp)) {
              viewingHistoryMap.set(item.id, item);
            }
          }
        });

        // Process filtered Firebase search history
        filteredSearchHistory.forEach(item => {
          if (item && item.id) {
            searchHistoryMap.set(item.id, item);
          }
        });

        // Process local search history (will overwrite Firebase items with same ID)
        localSearchHistory.forEach(item => {
          if (item && item.id) {
            // Skip deleted items
            if (this.isHistoryItemDeleted(item.id)) {
              return;
            }
            
            const firebaseItem = searchHistoryMap.get(item.id);
            // Only use local item if it's newer
            if (!firebaseItem || (item.timestamp > firebaseItem.timestamp)) {
              searchHistoryMap.set(item.id, item);
            }
          }
        });

        // Convert maps back to arrays and limit size
        let mergedViewingHistory = Array.from(viewingHistoryMap.values());
        let mergedSearchHistory = Array.from(searchHistoryMap.values());

        // Sort by timestamp (most recent first) and limit size
        mergedViewingHistory.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));
        mergedSearchHistory.sort((a, b) => (b.timestamp || 0) - (a.timestamp || 0));

        // Limit size to improve performance
        mergedViewingHistory = mergedViewingHistory.slice(0, this.maxHistoryItems);
        mergedSearchHistory = mergedSearchHistory.slice(0, this.maxHistoryItems);

        // Combine the merged histories
        const combinedHistory = [...mergedViewingHistory, ...mergedSearchHistory];

        localStorage.setItem('vodg-vod-history', JSON.stringify(combinedHistory));

        // Also update Firebase with the merged history (in background)
        this.isDirectSyncCall = true;
        window.updateDoc(userDocRef, {
          viewingHistory: mergedViewingHistory,
          searchHistory: mergedSearchHistory,
          lastUpdated: window.serverTimestamp()
        }).catch(error => {
          console.error('Error updating merged history in Firebase:', error);
        });
        this.isDirectSyncCall = false;
      } else {
        // If no local history, just use filtered Firebase history (limited to improve performance)
        const limitedViewingHistory = filteredViewingHistory.slice(0, this.maxHistoryItems);
        const limitedSearchHistory = filteredSearchHistory.slice(0, this.maxHistoryItems);
        const combinedHistory = [...limitedViewingHistory, ...limitedSearchHistory];

        localStorage.setItem('vodg-vod-history', JSON.stringify(combinedHistory));
      }

      console.log('User data successfully loaded from Firestore');
      this.hideSyncProgress();

      // Record the time of this initial sync
      localStorage.setItem('vodg-last-sync-time', Date.now().toString());

      // Update UI if relevant functions exist
      if (typeof window.updateFavoritesList === 'function') {
        const favorites = JSON.parse(localStorage.getItem('vodg-favorites') || '[]');
        window.updateFavoritesList(favorites);
      }

      if (typeof window.loadHistoryItems === 'function') {
        window.loadHistoryItems();
      }

      return Promise.resolve();
    } catch (error) {
      console.error('Error loading data from Firestore:', error);
      this.hideSyncProgress();
      this.showSyncError('Failed to load your data. Please try again later.');
      return Promise.reject(error);
    }
  }

  /**
   * Force sync watch progress to Firestore
   * This is a convenience method to ensure watch progress is synced immediately
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async forceWatchProgressSync(userId) {
    if (!userId || !this.isOnline) return Promise.resolve();

    try {
      console.log(`Force syncing watch progress to Firestore for user ${userId}`);

      // Set a flag to bypass any restrictions
      const wasInitialSync = this.isInitialSync;
      const wasDirectSyncCall = this.isDirectSyncCall;

      this.isInitialSync = false;
      this.isDirectSyncCall = true;

      // Sync watch progress
      await this.syncToFirestore(userId, 'watchProgress');

      // Restore flags
      this.isInitialSync = wasInitialSync;
      this.isDirectSyncCall = wasDirectSyncCall;

      return Promise.resolve();
    } catch (error) {
      console.error('Error force syncing watch progress:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Force sync viewing history to Firestore
   * This is a convenience method to ensure viewing history is synced immediately
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async forceViewingHistorySync(userId) {
    if (!userId || !this.isOnline) return Promise.resolve();

    try {
      console.log(`%c Force syncing viewing history to Firestore for user ${userId}`, 'background: #e74c3c; color: white; padding: 2px 5px; border-radius: 3px;');

      // Log the viewing history data
      const viewingHistoryData = this.getLocalData('viewingHistory');
      console.log(`%c Viewing history data:`, 'background: #e74c3c; color: white; padding: 2px 5px; border-radius: 3px;', viewingHistoryData);

      // Set a flag to bypass any restrictions
      const wasInitialSync = this.isInitialSync;
      const wasDirectSyncCall = this.isDirectSyncCall;

      this.isInitialSync = false;
      this.isDirectSyncCall = true;

      // Sync viewing history
      await this.syncToFirestore(userId, 'viewingHistory');

      // Restore flags
      this.isInitialSync = wasInitialSync;
      this.isDirectSyncCall = wasDirectSyncCall;

      return Promise.resolve();
    } catch (error) {
      console.error('Error force syncing viewing history:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Force sync search history to Firestore
   * This is a convenience method to ensure search history is synced immediately
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async forceSearchHistorySync(userId) {
    if (!userId || !this.isOnline) return Promise.resolve();

    try {
      console.log(`%c Force syncing search history to Firestore for user ${userId}`, 'background: #9b59b6; color: white; padding: 2px 5px; border-radius: 3px;');

      // Log the search history data
      const searchHistoryData = this.getLocalData('searchHistory');
      console.log(`%c Search history data:`, 'background: #9b59b6; color: white; padding: 2px 5px; border-radius: 3px;', searchHistoryData);

      // Set a flag to bypass any restrictions
      const wasInitialSync = this.isInitialSync;
      const wasDirectSyncCall = this.isDirectSyncCall;

      this.isInitialSync = false;
      this.isDirectSyncCall = true;

      // Sync search history
      await this.syncToFirestore(userId, 'searchHistory');

      // Restore flags
      this.isInitialSync = wasInitialSync;
      this.isDirectSyncCall = wasDirectSyncCall;

      return Promise.resolve();
    } catch (error) {
      console.error('Error force syncing search history:', error);
      return Promise.reject(error);
    }
  }

  /**
   * Show error message to user
   * @param {string} message - Error message
   */
  showSyncError(message) {
    // Skip showing error message in the UI
    console.error('Sync error:', message);
    return;
  }

  /**
   * Set up realtime listeners for Firestore data changes
   * @param {string} userId - User ID
   */
  setupRealtimeListeners(userId) {
    if (!userId || !window.db || !window.onSnapshot) {
      console.log('Cannot setup realtime listeners: missing dependencies');
      return;
    }

    // Remove any existing listeners first
    this.removeRealtimeListeners();

    console.log('Setting up realtime Firestore listeners for user', userId);

    try {
      const userDocRef = window.doc(window.db, 'userData', userId);
      
      // First, always get the latest data from Firestore to ensure we're up to date
      window.getDoc(userDocRef).then((doc) => {
        if (doc.exists()) {
          console.log('Got latest data from Firestore on listener setup');
          const userData = doc.data();
          
          // Force update local storage with the latest server data
          let dataUpdated = false;
          
          // Process latest data from Firestore
          if (userData.watchProgress) {
            this.handleRealtimeUpdate('watchProgress', userData.watchProgress);
            dataUpdated = true;
          }
          
          if (userData.favorites) {
            this.handleRealtimeUpdate('favorites', userData.favorites);
            dataUpdated = true;
          }
          
          if (userData.viewingHistory) {
            this.handleRealtimeUpdate('viewingHistory', userData.viewingHistory);
            dataUpdated = true;
          }
          
          if (userData.searchHistory) {
            this.handleRealtimeUpdate('searchHistory', userData.searchHistory);
            dataUpdated = true;
          }
          
          if (dataUpdated) {
            // Record the sync time
            localStorage.setItem('vodg-last-sync-time', Date.now().toString());
            console.log('Initial data sync completed, local data updated with server data');
          }
        }
      }).catch(error => {
        console.error('Error getting initial data for realtime listener:', error);
      });
      
      // Then set up the listener for future changes
      this.firestoreListeners.userData = window.onSnapshot(userDocRef, (doc) => {
        if (!doc.exists()) {
          console.log('No user data in Firestore yet');
          return;
        }

        console.log('Received realtime update from Firestore');
        const userData = doc.data();
        
        // Process watch progress updates
        if (userData.watchProgress) {
          this.handleRealtimeUpdate('watchProgress', userData.watchProgress);
        }
        
        // Process favorites updates
        if (userData.favorites) {
          this.handleRealtimeUpdate('favorites', userData.favorites);
        }
        
        // Process viewing history updates
        if (userData.viewingHistory) {
          this.handleRealtimeUpdate('viewingHistory', userData.viewingHistory);
        }
        
        // Process search history updates
        if (userData.searchHistory) {
          this.handleRealtimeUpdate('searchHistory', userData.searchHistory);
        }
      }, (error) => {
        console.error('Error in Firestore realtime listener:', error);
      });

      console.log('Realtime Firestore listeners set up successfully');
      
      // Set up a periodic refresh to ensure we have the latest data
      // This helps in cases where the realtime listener might miss updates
      const refreshInterval = 10000; // 10 seconds (reduced from 30 seconds for more frequent updates)
      this.periodicRefreshTimer = setInterval(() => {
        if (this.isUserLoggedIn() && this.isOnline) {
          console.log('Running periodic data refresh to ensure sync between browsers');
          window.getDoc(userDocRef).then((doc) => {
            if (doc.exists()) {
              const userData = doc.data();
              const lastUpdated = userData.lastUpdated ? userData.lastUpdated.toDate().getTime() : 0;
              
              // Check if the data on server is newer than what we have by comparing timestamps
              let serverDataNewer = false;
              
              // Check if we have local timestamps to compare
              try {
                const localLastSync = parseInt(localStorage.getItem('vodg-last-sync-time') || '0');
                serverDataNewer = lastUpdated > localLastSync;
                
                if (serverDataNewer) {
                  console.log('Server data is newer than local data. Last server update:', new Date(lastUpdated), 'Last local sync:', new Date(localLastSync));
                }
              } catch (e) {
                console.error('Error comparing timestamps:', e);
                // If we can't determine, assume server data might be newer
                serverDataNewer = true;
              }
              
              // If server data is newer or we're specifically not in a direct sync call, update local data
              if (serverDataNewer || !this.isDirectSyncCall) {
                console.log('Periodic refresh: getting latest data from Firestore');
                
                if (userData.viewingHistory) {
                  this.handleRealtimeUpdate('viewingHistory', userData.viewingHistory);
                }
                
                if (userData.searchHistory) {
                  this.handleRealtimeUpdate('searchHistory', userData.searchHistory);
                }
                
                if (userData.watchProgress) {
                  this.handleRealtimeUpdate('watchProgress', userData.watchProgress);
                }
                
                if (userData.favorites) {
                  this.handleRealtimeUpdate('favorites', userData.favorites);
                }
                
                // Record the time of this sync
                localStorage.setItem('vodg-last-sync-time', Date.now().toString());
              }
            }
          }).catch(error => {
            console.error('Error in periodic refresh:', error);
          });
        }
      }, refreshInterval);
      
    } catch (error) {
      console.error('Error setting up realtime listeners:', error);
    }
  }

  /**
   * Handle realtime updates from Firestore
   * @param {string} dataType - Type of data
   * @param {any} firestoreData - Data from Firestore
   */
  handleRealtimeUpdate(dataType, firestoreData) {
    // Skip if we're in the middle of syncing to Firestore
    // This prevents infinite loops where local changes trigger Firestore updates
    // which then trigger local changes again
    if (this.isDirectSyncCall) {
      console.log(`Skipping realtime update for ${dataType} during direct sync call`);
      return;
    }

    console.log(`Processing realtime update for ${dataType}`);

    try {
      // Get local data
      let localData;
      
      if (dataType === 'watchProgress') {
        localData = JSON.parse(localStorage.getItem('vodg-play') || '{}');
      } else if (dataType === 'favorites') {
        localData = JSON.parse(localStorage.getItem('vodg-favorites') || '[]');
      } else if (dataType === 'viewingHistory' || dataType === 'searchHistory') {
        // For history, we need to get the combined history first
        const allHistory = JSON.parse(localStorage.getItem('vodg-vod-history') || '[]');
        
        if (dataType === 'viewingHistory') {
          localData = allHistory.filter(item => item && item.id && !item.id.startsWith('search-'));
        } else {
          localData = allHistory.filter(item => item && item.id && item.id.startsWith('search-'));
        }
      }

      // Merge data with preference for newer timestamps
      let mergedData;
      
      if (dataType === 'watchProgress') {
        // For watch progress, merge objects
        mergedData = { ...localData };
        
        // Only update entries that are newer in Firestore
        Object.keys(firestoreData).forEach(key => {
          const firestoreItem = firestoreData[key];
          const localItem = localData[key];
          
          if (!localItem || (firestoreItem.timestamp > localItem.timestamp)) {
            mergedData[key] = firestoreItem;
          }
        });
        
        localStorage.setItem('vodg-play', JSON.stringify(mergedData));
        
        // Dispatch event to update UI
        window.dispatchEvent(new CustomEvent('watchProgressSynced'));
      } else if (dataType === 'favorites') {
        // For favorites, use a map to merge by ID
        const itemMap = new Map();
        
        // Add local items to the map
        localData.forEach(item => {
          if (item && item.id) {
            itemMap.set(item.id, item);
          }
        });
        
        // Update with Firestore items if they're newer
        firestoreData.forEach(item => {
          if (item && item.id) {
            const localItem = itemMap.get(item.id);
            if (!localItem || (item.lastWatched > localItem.lastWatched)) {
              itemMap.set(item.id, item);
            }
          }
        });
        
        // Convert map back to array
        mergedData = Array.from(itemMap.values());
        
        // Sort by lastWatched
        mergedData.sort((a, b) => ((b.lastWatched || 0) - (a.lastWatched || 0)));
        
        localStorage.setItem('vodg-favorites', JSON.stringify(mergedData));
        
        // Update UI if favorites component is initialized
        if (typeof window.updateFavoritesList === 'function') {
          window.updateFavoritesList(mergedData);
        }
      } else if (dataType === 'viewingHistory' || dataType === 'searchHistory') {
        // For history, we need to update the combined history
        const allHistory = JSON.parse(localStorage.getItem('vodg-vod-history') || '[]');
        
        // Separate viewing and search history
        const viewingHistory = allHistory.filter(item => item && item.id && !item.id.startsWith('search-'));
        const searchHistory = allHistory.filter(item => item && item.id && item.id.startsWith('search-'));
        
        // Create maps for efficient merging
        const viewingMap = new Map();
        const searchMap = new Map();
        
        // Add local items to maps
        viewingHistory.forEach(item => {
          if (item && item.id) {
            viewingMap.set(item.id, item);
          }
        });
        
        searchHistory.forEach(item => {
          if (item && item.id) {
            searchMap.set(item.id, item);
          }
        });
        
        // Update with Firestore items if they're newer
        if (dataType === 'viewingHistory') {
          firestoreData.forEach(item => {
            if (item && item.id) {
              // Skip this item if it's in our deleted items list
              if (this.isHistoryItemDeleted(item.id)) {
                console.log(`Skipping deleted item ${item.id} from Firebase sync`);
                return;
              }
              
              const localItem = viewingMap.get(item.id);
              if (!localItem || (item.timestamp > localItem.timestamp)) {
                viewingMap.set(item.id, item);
              }
            }
          });
        } else {
          firestoreData.forEach(item => {
            if (item && item.id) {
              // Skip this item if it's in our deleted items list
              if (this.isHistoryItemDeleted(item.id)) {
                console.log(`Skipping deleted item ${item.id} from Firebase sync`);
                return;
              }
              
              const localItem = searchMap.get(item.id);
              if (!localItem || (item.timestamp > localItem.timestamp)) {
                searchMap.set(item.id, item);
              }
            }
          });
        }
        
        // Convert maps back to arrays
        const updatedViewingHistory = Array.from(viewingMap.values());
        const updatedSearchHistory = Array.from(searchMap.values());
        
        // Sort by timestamp
        updatedViewingHistory.sort((a, b) => ((b.timestamp || 0) - (a.timestamp || 0)));
        updatedSearchHistory.sort((a, b) => ((b.timestamp || 0) - (a.timestamp || 0)));
        
        // Combine and limit size
        const combinedHistory = [
          ...updatedViewingHistory.slice(0, this.maxHistoryItems),
          ...updatedSearchHistory.slice(0, this.maxHistoryItems)
        ];
        
        localStorage.setItem('vodg-vod-history', JSON.stringify(combinedHistory));
        
        // Dispatch event to update UI
        window.dispatchEvent(new CustomEvent('historyUpdated', { detail: combinedHistory }));
        
        // Also try to directly update the UI if the function is available
        if (typeof window.updateHistoryList === 'function') {
          // Split history for UI updates
          const viewingHistory = dataType === 'viewingHistory' 
            ? Array.from(viewingMap.values()) 
            : updatedViewingHistory;
            
          const searchHistory = dataType === 'searchHistory'
            ? Array.from(searchMap.values())
            : updatedSearchHistory;
          
          // Update viewing history list
          window.updateHistoryList(viewingHistory, '.viewing-list', '观看');
          
          // Update search history list
          window.updateHistoryList(searchHistory, '.search-list', '搜索');
          
          console.log('History UI updated with latest data from Firestore');
        } else {
          // Fallback to dispatching an event if the direct function isn't available
          window.dispatchEvent(new CustomEvent('historyUpdated', { detail: combinedHistory }));
        }
        
        // Also try calling loadHistoryItems as an additional fallback to ensure UI is updated
        if (typeof window.loadHistoryItems === 'function') {
          window.loadHistoryItems();
          console.log('Full history reload triggered to ensure UI is updated');
        }
      }
      
      console.log(`Realtime update for ${dataType} processed successfully`);
    } catch (error) {
      console.error(`Error processing realtime update for ${dataType}:`, error);
    }
  }

  /**
   * Remove all Firestore realtime listeners
   */
  removeRealtimeListeners() {
    console.log('Removing Firestore realtime listeners');
    
    // Unsubscribe from all listeners
    Object.values(this.firestoreListeners).forEach(unsubscribe => {
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      }
    });
    
    // Clear periodic refresh timer if it exists
    if (this.periodicRefreshTimer) {
      clearInterval(this.periodicRefreshTimer);
      this.periodicRefreshTimer = null;
    }
    
    // Reset listeners object
    this.firestoreListeners = {};
  }

  /**
   * Force refresh data from Firestore on page refresh
   * Unlike syncFromFirestore, this method always fetches the latest data from Firestore
   * even if local data exists, but it merges intelligently with local changes
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async forceRefreshFromFirestore(userId) {
    if (!userId || !this.isOnline) return Promise.resolve();

    try {
      console.log(`Force refreshing data from Firestore for user ${userId}`);
      this.showSyncProgress('正在刷新数据...', 10);

      // Get Firestore references
      const db = window.db;
      if (!db) {
        console.error('Firestore not initialized');
        this.hideSyncProgress();
        return Promise.resolve();
      }

      const userDocRef = window.doc(db, 'userData', userId);
      const userDoc = await window.getDoc(userDocRef);

      if (!userDoc.exists()) {
        console.log('No user data found in Firestore to refresh');
        this.hideSyncProgress();
        return Promise.resolve();
      }

      this.showSyncProgress('正在合并数据...', 50);
      
      const userData = userDoc.data();

      // Process favorites
      if (userData.favorites && Array.isArray(userData.favorites)) {
        try {
          // Get local favorites
          const localFavorites = JSON.parse(localStorage.getItem('vodg-favorites') || '[]');
          
          // Create a map for efficient lookups
          const favoritesMap = new Map();
          
          // Add local favorites to the map
          localFavorites.forEach(item => {
            if (item && item.id) {
              favoritesMap.set(item.id, item);
            }
          });
          
          // Update with Firestore favorites if they're newer or don't exist locally
          userData.favorites.forEach(item => {
            if (item && item.id) {
              const localItem = favoritesMap.get(item.id);
              // If item doesn't exist locally or Firestore version is newer
              if (!localItem || (item.lastWatched > localItem.lastWatched)) {
                favoritesMap.set(item.id, item);
              }
            }
          });
          
          // Convert map back to array and sort by lastWatched
          const mergedFavorites = Array.from(favoritesMap.values());
          mergedFavorites.sort((a, b) => ((b.lastWatched || 0) - (a.lastWatched || 0)));
          
          // Save merged favorites to localStorage
          localStorage.setItem('vodg-favorites', JSON.stringify(mergedFavorites));
          console.log('Favorites refreshed with merged data:', mergedFavorites.length, 'items');
          
          // Update UI if favorites component is initialized
          if (typeof window.updateFavoritesList === 'function') {
            window.updateFavoritesList(mergedFavorites);
          }
        } catch (e) {
          console.error('Error processing favorites during refresh:', e);
          // Fallback to just using Firebase data
          localStorage.setItem('vodg-favorites', JSON.stringify(userData.favorites));
        }
      }

      // Process history (viewing history and search history)
      if (userData.viewingHistory || userData.searchHistory) {
        try {
          // Get local history
          const localHistory = JSON.parse(localStorage.getItem('vodg-vod-history') || '[]');
          
          // Create a map for efficient lookups
          const historyMap = new Map();
          
          // Add local history to the map
          localHistory.forEach(item => {
            if (item && item.id) {
              historyMap.set(item.id, item);
            }
          });
          
          // Update with Firestore viewing history if items are newer or don't exist locally
          const viewingHistory = userData.viewingHistory || [];
          viewingHistory.forEach(item => {
            if (item && item.id) {
              const localItem = historyMap.get(item.id);
              // If item doesn't exist locally or Firestore version is newer
              if (!localItem || (item.timestamp > localItem.timestamp)) {
                historyMap.set(item.id, item);
              }
            }
          });
          
          // Update with Firestore search history if items are newer or don't exist locally
          const searchHistory = userData.searchHistory || [];
          searchHistory.forEach(item => {
            if (item && item.id) {
              const localItem = historyMap.get(item.id);
              // If item doesn't exist locally or Firestore version is newer
              if (!localItem || (item.timestamp > localItem.timestamp)) {
                historyMap.set(item.id, item);
              }
            }
          });
          
          // Convert map back to array and sort by timestamp
          const mergedHistory = Array.from(historyMap.values());
          mergedHistory.sort((a, b) => ((b.timestamp || 0) - (a.timestamp || 0)));
          
          // Save merged history to localStorage
          localStorage.setItem('vodg-vod-history', JSON.stringify(mergedHistory));
          console.log('History refreshed with merged data:', mergedHistory.length, 'items');
          
          // Update UI if history component is initialized
          if (typeof window.updateHistoryList === 'function') {
            // Split history into viewing and search history for UI updates
            const viewingHistory = mergedHistory.filter(item => item && item.id && !item.id.startsWith('search-'));
            const searchHistory = mergedHistory.filter(item => item && item.id && item.id.startsWith('search-'));
            
            // Update viewing history list
            window.updateHistoryList(viewingHistory, '.viewing-list', '观看');
            
            // Update search history list
            window.updateHistoryList(searchHistory, '.search-list', '搜索');
            
            console.log('History UI updated with latest data from Firestore');
          } else {
            // Fallback to dispatching an event if the direct function isn't available
            window.dispatchEvent(new CustomEvent('historyUpdated', { detail: mergedHistory }));
          }
          
          // Also try calling loadHistoryItems as an additional fallback to ensure UI is updated
          if (typeof window.loadHistoryItems === 'function') {
            window.loadHistoryItems();
            console.log('Full history reload triggered to ensure UI is updated');
          }
        } catch (e) {
          console.error('Error processing history during refresh:', e);
          // Fallback to just using Firebase data
          const combinedHistory = [...(userData.viewingHistory || []), ...(userData.searchHistory || [])];
          localStorage.setItem('vodg-vod-history', JSON.stringify(combinedHistory));
        }
      }

      // Process watch progress
      if (userData.watchProgress) {
        try {
          // Get local watch progress
          const localWatchProgress = JSON.parse(localStorage.getItem('vodg-play') || '{}');
          
          // Merge watch progress, prioritizing newer items
          const mergedWatchProgress = { ...localWatchProgress };
          
          // Update with Firestore watch progress if items are newer or don't exist locally
          Object.keys(userData.watchProgress).forEach(key => {
            const firestoreItem = userData.watchProgress[key];
            const localItem = localWatchProgress[key];
            
            // If item doesn't exist locally or Firestore version is newer
            if (!localItem || (firestoreItem.timestamp > localItem.timestamp)) {
              mergedWatchProgress[key] = firestoreItem;
            }
          });
          
          // Save merged watch progress to localStorage
          localStorage.setItem('vodg-play', JSON.stringify(mergedWatchProgress));
          console.log('Watch progress refreshed with merged data');
          
          // Dispatch event to update UI
          window.dispatchEvent(new CustomEvent('watchProgressSynced'));
        } catch (e) {
          console.error('Error processing watch progress during refresh:', e);
          // Fallback to just using Firebase data
          localStorage.setItem('vodg-play', JSON.stringify(userData.watchProgress));
        }
      }

      this.showSyncProgress('数据刷新完成', 100);
      setTimeout(() => this.hideSyncProgress(), 500);

      // Return a resolved promise to indicate completion
      return Promise.resolve();
    } catch (error) {
      console.error('Error refreshing data from Firestore:', error);
      this.hideSyncProgress();
      
      // Show error message to user
      this.showErrorMessage('刷新数据错误: ' + error.message);
      
      return Promise.reject(error);
    }
  }

  /**
   * Force sync favorites to Firestore
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  async forceFavoritesSync(userId) {
    if (!userId) return Promise.resolve();

    try {
      console.log('%c Force syncing favorites to Firestore', 'background: #3498db; color: white; padding: 2px 5px; border-radius: 3px;');
      
      // Set direct sync flag to prevent infinite loops with realtime listeners
      this.isDirectSyncCall = true;
      
      // Get local data
      const favorites = JSON.parse(localStorage.getItem('vodg-favorites') || '[]');
      
      // Get Firestore references
      const db = window.db; // Assuming db is available globally
      if (!db) {
        console.error('Firestore not initialized');
        return;
      }

      // Create or update document in Firestore
      const userDocRef = window.doc(db, 'userData', userId);
      
      // Update just the favorites field
      await window.updateDoc(userDocRef, {
        favorites: favorites,
        lastUpdated: window.serverTimestamp()
      });

      console.log('Successfully force synced favorites to Firestore');
    } catch (error) {
      console.error('Error force syncing favorites to Firestore:', error);
    } finally {
      // Reset direct sync flag
      this.isDirectSyncCall = false;
    }
  }

  /**
   * Add a history item to the deleted items tracking
   * @param {string} itemId - The ID of the history item that was deleted
   */
  addDeletedHistoryItem(itemId) {
    if (!itemId) return;
    
    this.deletedHistoryItems.add(itemId);
    
    // Save to localStorage for persistence
    try {
      localStorage.setItem('vodg-deleted-history-items', JSON.stringify([...this.deletedHistoryItems]));
    } catch (error) {
      console.error('Error saving deleted history items:', error);
    }
    
    console.log(`Added item ${itemId} to deleted history tracking`);
  }

  /**
   * Check if a history item has been deleted
   * @param {string} itemId - The ID of the history item to check
   * @returns {boolean} - True if the item has been deleted
   */
  isHistoryItemDeleted(itemId) {
    return this.deletedHistoryItems.has(itemId);
  }

  /**
   * Show error message to user
   * @param {string} message - Error message
   */
  showErrorMessage(message) {
    // Skip showing error message in the UI
    console.error('Sync error:', message);
    return;
  }
}

// Initialize sync manager when the script loads, but only if Firebase auth is enabled
document.addEventListener('DOMContentLoaded', () => {
  // Check if Firebase auth is enabled in config
  const isFirebaseAuthEnabled = window.siteConfig && window.siteConfig.auth && window.siteConfig.auth.enabled !== false;

  if (isFirebaseAuthEnabled) {
    // Create sync manager instance
    window.syncManager = new FirebaseSyncManager();
    console.log('Firebase Sync Manager loaded');
  } else {
    // Create a dummy sync manager that does nothing
    window.syncManager = {
      syncToFirestore: async function() {
        console.log('Firebase auth is disabled, sync operations are no-op');
        return Promise.resolve();
      },
      syncFromFirestore: async function() {
        console.log('Firebase auth is disabled, sync operations are no-op');
        return Promise.resolve();
      },
      syncAll: async function() {
        console.log('Firebase auth is disabled, sync operations are no-op');
        return Promise.resolve();
      },
      forceWatchProgressSync: async function() {
        console.log('Firebase auth is disabled, sync operations are no-op');
        return Promise.resolve();
      },
      forceViewingHistorySync: async function() {
        console.log('Firebase auth is disabled, sync operations are no-op');
        return Promise.resolve();
      },
      forceSearchHistorySync: async function() {
        console.log('Firebase auth is disabled, sync operations are no-op');
        return Promise.resolve();
      }
    };
    console.log('Firebase auth is disabled, using dummy sync manager');
  }
});
