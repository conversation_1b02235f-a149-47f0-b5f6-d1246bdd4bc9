{[
local play_count = 0
local download_count = 0
for _, ch in ipairs(ctx.channels) do
if ctx.vod.sources[ch.code] then
if ch.title:find('下载') then
download_count = download_count + 1
else
play_count = play_count + 1
end
end
end
local ch_tips = ''
if play_count + download_count == 0 then
ch_tips = '暂无线路'
elseif play_count == 1 then
ch_tips = '1 条播放'
else
ch_tips = play_count .. ' 条播放：<span class="chan-attention neon-flash">如不能播放请切换线路，可使用"一键测速"选出最快的线路。</span>'
end
if download_count ~= 0 then
if ch_tips ~= '' then
ch_tips = ch_tips .. '<br>'
end
ch_tips = string.format('%s%s 条下载：点开"下载"线路视频后，鼠标"右键"-&gt;"另存为"可保存视频。', ch_tips, download_count)
end
]}

<div class="episodes-container">
    <div style="color: var(--text-tertiary); margin-bottom: var(--spacing-4);">{= ch_tips =}</div>

    {[ if play_count > 0 then ]}
    <div style="margin-bottom: var(--spacing-6); display: flex; align-items: center; gap: var(--spacing-3); flex-wrap: wrap;">
        <span style="color: var(--text-tertiary);">一键测速：</span>
        <button id="speed-btn-first" class="tag-button speed-btn" onclick="detectSpeed(1)" disabled>
            <span id="speed-btn-1">加载中...</span>
        </button>
        <button id="speed-btn-last" class="tag-button speed-btn" onclick="detectSpeed(-1)" disabled>
            <span id="speed-btn-2">请稍后...</span>
        </button>
        <span style="color: var(--text-tertiary); font-size: var(--font-size-sm);">
            "测速失败"表示线路不可用{[ if play_count > 1 then ]}，但全部失败则可能{[ else ]}或{[ end ]}需要升级浏览器至较新版本。
            <span id="speed-tips"></span>
        </span>
    </div>
    {[ end ]}

    <div class="episodes-title">选择线路</div>

    <div class="tab-buttons" style="margin-bottom: var(--spacing-6);">
        {[ for _, ch in ipairs(ctx.channels) do ]}
        {[ local vod_src = ctx.vod.sources[ch.code] ]}
        {[ if vod_src then ]}
        <button data-tab-id="{{ ch.code }}"
            class="tab-button tabs-head-btn{[ if ch.code == ctx.ch then ]} active tabs-curr vod-chan-curr{[ end ]}">
            <span>{{ ch.title }}</span> <sup style="color: var(--primary-light);">{= #vod_src.urls =}</sup>
            {[ if vod_src.urls[1] and vod_src.urls[1].url:find('.m3u8$') or vod_src.urls[1].url:find('.mp4$') then ]}
            <div class="vod-speed" data-first="{= vod_src.urls[1].url =}"
                data-last="{= vod_src.urls[#vod_src.urls].url =}" style="font-size: 0.75rem; color: var(--accent);">
            </div>
            {[ end ]}
        </button>
        {[ end ]}
        {[ end ]}

        {[ if ctx.include_detail_box then ]}
        <button class="tab-button tabs-head-btn" data-tab-id="detail"><span>详情</span></button>
        {[ end ]}

        {[ if ctx.include_more_vods then ]}
        <button class="tab-button tabs-head-btn" data-tab-id="more"><span>更多推荐</span></button>
        {[ end ]}
    </div>

    <div class="tab-content-container tabs-body">
        {[ if ctx.include_more_vods then ]}
        <div class="tab-content" data-tab-id="more">{| include("incl_list.html", { vod_list = ctx.more_vods }) |}</div>
        {[ end ]}

        {[ for _, ch in ipairs(ctx.channels) do ]}
        {[ local vod_src = ctx.vod.sources[ch.code] ]}
        {[ if vod_src then ]}
        {[
        local eps = {}
        if #vod_src.urls > 20 then
        for i = #vod_src.urls, 1, -1 do
        eps[#eps + 1] = i
        end
        else
        for i = 1, #vod_src.urls, 1 do
        eps[#eps + 1] = i
        end
        end
        ]}
        <div class="tab-content vod-episodes{[ if ch.code == ctx.ch then ]} active tabs-curr{[ end ]}" data-tab-id="{{ ch.code }}">
            <div class="episodes-grid">
                {[ for _, ep in ipairs(eps) do ]}
                {[ local url = vod_src.urls[ep] ]}
                <div class="episode-button vod-episode{[ if ch.code == ctx.ch and ep == ctx.ep then ]} active vod-episode-curr{[ end ]}">
                    {[ if ch.title:find('下载') then ]}
                    <a href="{= url.url =}" download="{{ ctx.vod.title }} {{ url.title }}"
                        target="_blank" rel="nofollow noopener noreferrer" style="display: block; width: 100%; height: 100%;">{{ url.title }}</a>
                    {[ else ]}
                    <a href="/vod/play/?id={= ctx.vod.id =}&ch={= ch.code =}&ep={= ep =}{[ if ctx.player and ctx.player.name ~= '' then ]}&ply={= ctx.player.name =}{[ else ]}&ply=artplayer{[ end ]}"
                        rel="nofollow"
                        class="vod-episode-play"
                        style="display: block; width: 100%; height: 100%;"
                        title="{{ ctx.vod.title }} {{ url.title }}">{{ url.title }}</a>
                    {[ end ]}
                </div>
                {[ end ]}
            </div>
        </div>
        {[ end ]}
        {[ end ]}

        {[ if ctx.include_detail_box then ]}
        <div class="tab-content" data-tab-id="detail">{| include("incl_detail_box.html") |}</div>
        {[ end ]}
    </div>
</div>

<script>
    const CLS_CURR = 'tabs-curr'
    const btns = document.querySelectorAll('.tab-buttons .tabs-head-btn')
    const bodies = document.querySelectorAll('.tab-content-container>*')
    const initTab = () => {
        let currTabId = ''
        for (let i = 0; i < btns.length; i++) {
            if (btns[i].classList.contains(CLS_CURR)) {
                currTabId = btns[i].getAttribute('data-tab-id')
                showBody(currTabId)
            }
            const select = () => {
                if (currTabId != btns[i].getAttribute('data-tab-id')) {
                    btns.forEach((x) => {
                        x.classList.remove(CLS_CURR)
                        x.classList.remove('active')
                    })
                    btns[i].classList.add(CLS_CURR)
                    btns[i].classList.add('active')
                    currTabId = btns[i].getAttribute('data-tab-id')
                    showBody(btns[i].getAttribute('data-tab-id'))
                }
            }
            btns[i].addEventListener('click', select, true)
            btns[i].querySelector('span').addEventListener('mouseover', select, true)
        }
    }
    const showBody = (tabId) => {
        for (let i = 0; i < bodies.length; i++) {
            if (bodies[i].getAttribute('data-tab-id') == tabId) {
                bodies[i].classList.add(CLS_CURR)
                bodies[i].classList.add('active')
            } else {
                bodies[i].classList.remove(CLS_CURR)
                bodies[i].classList.remove('active')
            }
        }
    }
    initTab()
</script>
<script>
    // Set default player to artplayer if not set
    if (!localStorage.getItem('vodg-player-name')) {
        localStorage.setItem('vodg-player-name', 'artplayer')
    }

    const name = localStorage.getItem('vodg-player-name')
    document.querySelectorAll('.vod-episode-play').forEach((el) => {
        if (!el.href.includes('&ply=')) {
            el.href += '&ply=' + (name || 'artplayer')
        } else if (el.href.includes('&ply=')) {
            // Replace any existing player with the preferred one
            el.href = el.href.replace(/&ply=[^&]+/, '&ply=' + (name || 'artplayer'))
        }
    })
</script>
<script type="module">
    import init, { detect_m3u8_speed, detect_read_speed } from '/statics/vod/speed_detector.js'
    const getById = (id) => document.getElementById(id)
    const enable = () => {
        getById('speed-btn-1').textContent = '测第一集'
        getById('speed-btn-2').textContent = '测最新集'
        document
            .querySelectorAll('.speed-btn')
            .forEach((x) => x.removeAttribute('disabled'))
    }
    const disable = () => {
        document
            .querySelectorAll('.speed-btn')
            .forEach((x) => x.setAttribute('disabled', 'disabled'))
        getById('speed-btn-1').innerHTML = '测速中...'
        getById('speed-btn-2').innerHTML = '请稍后...'
    }
    init().then(enable)
    window.detectSpeed = async (arg) => {
        disable()
        const promises = []
        const ATTR = arg == 1 ? 'data-first' : 'data-last'
        let fst = true
        for (const el of document.querySelectorAll('.vod-speed')) {
            const content = el.textContent
            el.textContent = '测速中'
            setTimeout(() => {
                const href = el.getAttribute(ATTR)
                let detector
                if (href.endsWith('.m3u8')) {
                    detector = detect_m3u8_speed
                } else if (href.endsWith('.mp4')) {
                    detector = detect_read_speed
                } else {
                    el.textContent = content
                    return
                }
                const prm = detector(href, arg)
                    .then((x) => el.textContent = (x / 8 / 1024).toFixed(2) + 'KB/S')
                    .catch(() => { el.textContent = '测速失败' })
                promises.push(prm)
            }, fst ? 0 : 1000)
            fst = false
        }
        const tasks = new Promise((resolve) => setTimeout(() => Promise.all(promises).then(resolve), 2000))
        const timeout = new Promise((_, reject) => setTimeout(() => reject(new Error("Timeout")), 15000))
        const tips = '<br><span class="neon-flash">有些浏览器有缓存，再次测速可能需要刷新页面才能得到准确结果。</span>'
        Promise.race([tasks, timeout]).then(enable).catch(enable).finally(() => getById('speed-tips').innerHTML = tips)
    }
</script>
<script src="/statics/vod/episode-manager.js"></script>
