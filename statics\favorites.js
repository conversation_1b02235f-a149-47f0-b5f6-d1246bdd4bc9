// Favorites Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize favorites system
    initFavorites();

    // Function to initialize favorites system
    function initFavorites() {
        // Load favorites items
        loadFavoritesItems();

        // Add scroll event listener to close favorites on desktop
        addFavoritesScrollHandler();

        // Add event listener for history updates to keep favorites in sync
        window.addEventListener('viewingHistoryUpdated', function(event) {
            // Check if this event was triggered by updateFavoritesFromHistory to prevent infinite recursion
            if (event.detail && event.detail.skipFavoritesUpdate) {
                return; // Skip updating favorites to prevent recursion
            }
            
            // Update favorites with latest episode info from history
            if (typeof window.updateFavoritesFromHistory === 'function') {
                window.updateFavoritesFromHistory();
            }
        });
    }

    // Function to add scroll handler for favorites
    function addFavoritesScrollHandler() {
        // This is a backup for the handler in base.html
        // It ensures the favorites panel closes on scroll even if the base.html handler fails
        window.addEventListener('scroll', function() {
            if (window.innerWidth > 768) {
                const favoritesContent = document.getElementById('favorites-dropdown');
                if (favoritesContent && favoritesContent.classList.contains('show')) {
                    if (typeof closeFavorites === 'function') {
                        closeFavorites();
                    }
                }
            }
        }, { passive: true }); // Use passive listener for better performance
    }
});

// Function to add an item to favorites
window.addToFavorites = function(item) {
    // Get existing favorites
    let favorites = JSON.parse(localStorage.getItem('vodg-favorites')) || [];

    // Check if item already exists in favorites
    const existingIndex = favorites.findIndex(fav => fav && fav.id === item.id);

    if (existingIndex !== -1) {
        // Item already exists, remove it (toggle behavior)
        favorites.splice(existingIndex, 1);
    } else {
        // Add new item to favorites
        // We'll sort it properly later, so just add it to the array
        favorites.push(item);
    }

    // Add timestamps for sorting and conflict resolution
    const now = Date.now();
    item.timestamp = now;

    // Add lastWatched property for sorting
    try {
        // First check if this item exists in history and has a lastWatched value
        const history = JSON.parse(localStorage.getItem('vodg-vod-history') || '[]');
        const historyItem = history.find(h => h && h.id === item.id);

        if (historyItem && historyItem.lastWatched) {
            // Use the same lastWatched value from history for consistency
            item.lastWatched = historyItem.lastWatched;
        } else {
            // If not in history or no lastWatched, check play cache
            const playCache = JSON.parse(localStorage.getItem('vodg-play') || '{}');
            if (playCache[item.id]) {
                const episodes = Object.keys(playCache[item.id]);
                if (episodes.length > 0) {
                    // Find the episode with the most recent 'updated' timestamp
                    let latestTimestamp = 0;
                    episodes.forEach(ep => {
                        const timestamp = playCache[item.id][ep].updated || 0;
                        if (timestamp > latestTimestamp) {
                            latestTimestamp = timestamp;
                        }
                    });

                    // Set the lastWatched property
                    item.lastWatched = latestTimestamp;
                } else {
                    // No episodes watched yet, use current time
                    item.lastWatched = now;
                }
            } else {
                // No watch data, use current time
                item.lastWatched = now;
            }

            // If we found this item in history but it didn't have lastWatched,
            // update the history item with the same lastWatched value for consistency
            if (historyItem) {
                historyItem.lastWatched = item.lastWatched;
                historyItem.timestamp = now; // Also update timestamp
                localStorage.setItem('vodg-vod-history', JSON.stringify(history));
            }
        }

        // Sort favorites by lastWatched (most recent first)
        favorites.sort((a, b) => {
            return ((b.lastWatched || 0) - (a.lastWatched || 0));
        });
    } catch (e) {
        console.error('Error setting lastWatched for favorite:', e);
        // Fallback to current time
        item.lastWatched = now;
    }

    // Save updated favorites
    localStorage.setItem('vodg-favorites', JSON.stringify(favorites));

    // If user is logged in, sync directly to Firebase
    if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
        const userId = window.getCurrentUserId();
        if (userId && window.syncManager) {
            if (typeof window.syncManager.forceFavoritesSync === 'function') {
                window.syncManager.forceFavoritesSync(userId);
            } else {
                // Fallback to event dispatch if direct method not available
                window.dispatchEvent(new CustomEvent('favoritesUpdated'));
            }
        }
    } else {
        // If not logged in, just dispatch the event
        window.dispatchEvent(new CustomEvent('favoritesUpdated'));
    }

    // Real-time updates for both history and favorites
    // Update favorites dropdown if it exists and is open
    if (typeof window.loadFavoritesItems === 'function') {
        const favoritesDropdown = document.getElementById('favorites-dropdown');
        if (favoritesDropdown && favoritesDropdown.classList.contains('show')) {
            loadFavoritesItems();
        }
    }

    // Update history dropdown if it exists and is open
    if (typeof window.loadHistoryItems === 'function') {
        const historyDropdown = document.getElementById('history-dropdown');
        if (historyDropdown && historyDropdown.classList.contains('show')) {
            window.loadHistoryItems();
        }
    }

    return existingIndex === -1; // Return true if added, false if removed
};

// Function to check if an item is in favorites
window.isInFavorites = function(id) {
    const favorites = JSON.parse(localStorage.getItem('vodg-favorites')) || [];
    return favorites.some(item => item && item.id === id);
};

// Function to remove an item from favorites
window.removeFavoriteItem = function(id) {
    let favorites = JSON.parse(localStorage.getItem('vodg-favorites')) || [];

    // Find and remove the item with the matching id
    favorites = favorites.filter(item => item && item.id !== id);

    // Save the updated favorites
    localStorage.setItem('vodg-favorites', JSON.stringify(favorites));

    // If user is logged in, sync directly to Firebase
    if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
        const userId = window.getCurrentUserId();
        if (userId && window.syncManager) {
            if (typeof window.syncManager.forceFavoritesSync === 'function') {
                window.syncManager.forceFavoritesSync(userId);
            } else {
                // Fallback to event dispatch if direct method not available
                window.dispatchEvent(new CustomEvent('favoritesUpdated'));
            }
        }
    } else {
        // If not logged in, just dispatch the event
        window.dispatchEvent(new CustomEvent('favoritesUpdated'));
    }

    // Real-time updates for both history and favorites
    // Update favorites dropdown if it exists and is open
    if (typeof window.loadFavoritesItems === 'function') {
        const favoritesDropdown = document.getElementById('favorites-dropdown');
        if (favoritesDropdown && favoritesDropdown.classList.contains('show')) {
            loadFavoritesItems();
        }
    }

    // Update history dropdown if it exists and is open
    if (typeof window.loadHistoryItems === 'function') {
        const historyDropdown = document.getElementById('history-dropdown');
        if (historyDropdown && historyDropdown.classList.contains('show')) {
            window.loadHistoryItems();
        }
    }

    // Show feedback message
    showFeedbackMessage('已从收藏夹中删除');
};

// Function to clear all favorites
window.clearFavorites = function() {
    // Clear favorites
    localStorage.removeItem('vodg-favorites');

    // If user is logged in, sync directly to Firebase
    if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
        const userId = window.getCurrentUserId();
        if (userId && window.syncManager) {
            if (typeof window.syncManager.forceFavoritesSync === 'function') {
                window.syncManager.forceFavoritesSync(userId);
            } else {
                // Fallback to event dispatch if direct method not available
                window.dispatchEvent(new CustomEvent('favoritesUpdated'));
            }
        }
    } else {
        // If not logged in, just dispatch the event
        window.dispatchEvent(new CustomEvent('favoritesUpdated'));
    }

    // Real-time updates for both history and favorites
    // Update favorites dropdown if it exists and is open
    if (typeof window.loadFavoritesItems === 'function') {
        loadFavoritesItems();
    }

    // Update history dropdown if it exists and is open
    if (typeof window.loadHistoryItems === 'function') {
        const historyDropdown = document.getElementById('history-dropdown');
        if (historyDropdown && historyDropdown.classList.contains('show')) {
            window.loadHistoryItems();
        }
    }

    // Show feedback message
    showFeedbackMessage('已清除收藏夹');

    // Show feedback message on all clear buttons
    const clearButtons = document.querySelectorAll('.clear-favorites-button');
    clearButtons.forEach(clearButton => {
        if (clearButton) {
            const originalHTML = clearButton.innerHTML;
            clearButton.innerHTML = '<span>✓ 已清除</span>';
            clearButton.style.backgroundColor = 'var(--secondary)';
            clearButton.disabled = true;

            // Reset button after 2 seconds
            setTimeout(() => {
                clearButton.innerHTML = originalHTML;
                clearButton.style.backgroundColor = '';
                clearButton.disabled = false;
            }, 2000);
        }
    });
};

// Function to show a feedback message
function showFeedbackMessage(message) {
    const favoritesContent = document.getElementById('favorites-dropdown');
    if (!favoritesContent) return;

    const feedbackEl = document.createElement('div');
    feedbackEl.className = 'favorites-feedback';
    feedbackEl.textContent = message;
    feedbackEl.style.position = 'absolute';
    feedbackEl.style.bottom = '10px';
    feedbackEl.style.left = '50%';
    feedbackEl.style.transform = 'translateX(-50%)';
    feedbackEl.style.backgroundColor = 'var(--secondary)';
    feedbackEl.style.color = 'white';
    feedbackEl.style.padding = '5px 10px';
    feedbackEl.style.borderRadius = '4px';
    feedbackEl.style.fontSize = 'var(--font-size-sm)';
    feedbackEl.style.opacity = '0';
    feedbackEl.style.transition = 'opacity 0.3s ease';
    feedbackEl.style.zIndex = '1000';

    favoritesContent.appendChild(feedbackEl);

    // Show and then hide the feedback
    setTimeout(() => {
        feedbackEl.style.opacity = '1';
        setTimeout(() => {
            feedbackEl.style.opacity = '0';
            setTimeout(() => {
                if (favoritesContent.contains(feedbackEl)) {
                    favoritesContent.removeChild(feedbackEl);
                }
            }, 300);
        }, 1500);
    }, 10);
}

// Function to update favorites with latest episode information from history
window.updateFavoritesFromHistory = function() {
    try {
        const favorites = JSON.parse(localStorage.getItem('vodg-favorites')) || [];
        const history = JSON.parse(localStorage.getItem('vodg-vod-history')) || [];
        const playCache = JSON.parse(localStorage.getItem('vodg-play') || '{}');
        let updated = false;
        let historyUpdated = false;

        // For each favorite item, check if there's a newer episode in history
        for (let i = 0; i < favorites.length; i++) {
            if (favorites[i] && favorites[i].id) {
                // Find this item in history
                const historyItem = history.find(h => h && h.id === favorites[i].id);

                if (historyItem) {
                    // Get episode info from history item
                    const historyUrlParams = new URLSearchParams(historyItem.url.split('?')[1] || '');
                    const historyEp = historyUrlParams.get('ep') || historyItem.episode;

                    // Get episode info from favorite item
                    const favoriteUrlParams = new URLSearchParams(favorites[i].url.split('?')[1] || '');
                    const favoriteEp = favoriteUrlParams.get('ep') || favorites[i].episode;

                    // If history has a newer episode, update the favorite
                    if (historyEp && (!favoriteEp || parseInt(historyEp) > parseInt(favoriteEp))) {
                        favorites[i].url = historyItem.url;
                        favorites[i].episode = historyEp;
                        updated = true;
                    }

                    // Update the lastWatched timestamp for sorting
                    const now = Date.now();
                    let newLastWatched = 0;

                    // First check if history item has a lastWatched value
                    if (historyItem && historyItem.lastWatched) {
                        newLastWatched = historyItem.lastWatched;
                    }
                    // Then check play cache for the most recent timestamp
                    else if (playCache[favorites[i].id]) {
                        const episodes = Object.keys(playCache[favorites[i].id]);
                        if (episodes.length > 0) {
                            // Find the episode with the most recent 'updated' timestamp
                            let latestTimestamp = 0;
                            episodes.forEach(ep => {
                                const timestamp = playCache[favorites[i].id][ep].updated || 0;
                                if (timestamp > latestTimestamp) {
                                    latestTimestamp = timestamp;
                                }
                            });

                            newLastWatched = latestTimestamp;
                        }
                    }
                    // Fallback to history timestamp if available
                    else if (historyItem && historyItem.timestamp) {
                        newLastWatched = historyItem.timestamp;
                    }

                    // Only update if we found a valid timestamp
                    if (newLastWatched > 0) {
                        // Set the lastWatched property
                        favorites[i].lastWatched = newLastWatched;

                        // Also update the timestamp for Firebase sync
                        favorites[i].timestamp = now;

                        // Ensure history item has the same lastWatched value for consistency
                        if (historyItem) {
                            historyItem.lastWatched = newLastWatched;
                            historyItem.timestamp = now;

                            // Flag that we need to update history
                            historyUpdated = true;
                        }

                        updated = true;
                    }
                }
            }
        }

        // If any favorites were updated, sort and save the changes
        if (updated) {
            // Sort favorites by lastWatched (most recent first)
            favorites.sort((a, b) => {
                return ((b.lastWatched || 0) - (a.lastWatched || 0));
            });

            localStorage.setItem('vodg-favorites', JSON.stringify(favorites));

            // Update the UI if needed
            if (typeof window.updateFavoritesList === 'function') {
                window.updateFavoritesList(favorites);
            }

            // If user is logged in, sync directly to Firebase
            if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
                const userId = window.getCurrentUserId();
                if (userId && window.syncManager) {
                    if (typeof window.syncManager.forceFavoritesSync === 'function') {
                        window.syncManager.forceFavoritesSync(userId);
                    } else {
                        // Fallback to event dispatch if direct method not available
                        window.dispatchEvent(new CustomEvent('favoritesUpdated'));
                    }
                }
            } else {
                // If not logged in, just dispatch the event
                window.dispatchEvent(new CustomEvent('favoritesUpdated'));
            }
        }

        // If any history items were updated, sort and save the changes
        if (historyUpdated) {
            // Sort history by lastWatched (most recent first)
            history.sort((a, b) => {
                return ((b.lastWatched || 0) - (a.lastWatched || 0));
            });

            localStorage.setItem('vodg-vod-history', JSON.stringify(history));

            // Dispatch event for Firebase sync with flag to prevent recursion
            window.dispatchEvent(new CustomEvent('viewingHistoryUpdated', {
                detail: { skipFavoritesUpdate: true }
            }));

            // Update history UI if needed
            if (typeof window.loadHistoryItems === 'function') {
                const historyDropdown = document.getElementById('history-dropdown');
                if (historyDropdown && historyDropdown.classList.contains('show')) {
                    window.loadHistoryItems();
                }
            }
        }
    } catch (e) {
        console.error('Error updating favorites from history:', e);
    }
};

// Function to load and display favorites items
window.loadFavoritesItems = function() {
    // First update favorites with latest episode info from history
    window.updateFavoritesFromHistory();

    let favorites = JSON.parse(localStorage.getItem('vodg-favorites')) || [];

    // Filter out null items
    favorites = favorites.filter(x => x != null);

    // Sort favorites by most recent playback progress
    try {
        const playCache = JSON.parse(localStorage.getItem('vodg-play') || '{}');

        // Add a lastWatched property to each favorite item
        favorites.forEach(item => {
            if (item && item.id) {
                // Default to 0 (oldest) if no watch data is found
                item.lastWatched = 0;

                // Check if this item has any watch progress
                if (playCache[item.id]) {
                    // Find the most recent episode watched
                    const episodes = Object.keys(playCache[item.id]);
                    if (episodes.length > 0) {
                        // Find the episode with the most recent 'updated' timestamp
                        let latestTimestamp = 0;
                        episodes.forEach(ep => {
                            const timestamp = playCache[item.id][ep].updated || 0;
                            if (timestamp > latestTimestamp) {
                                latestTimestamp = timestamp;
                            }
                        });

                        // Set the lastWatched property
                        item.lastWatched = latestTimestamp;
                    }
                }
            }
        });

        // Sort favorites by lastWatched (most recent first)
        favorites.sort((a, b) => {
            return (b.lastWatched || 0) - (a.lastWatched || 0);
        });
    } catch (e) {
        console.error('Error sorting favorites by recent playback:', e);
    }

    // Update favorites list
    updateFavoritesList(favorites);
};

// Helper function to update the favorites list
window.updateFavoritesList = function(items) {
    const listElement = document.querySelector('.favorites-list');
    if (!listElement) return;

    // If items is undefined or null, load from localStorage
    if (!items) {
        items = JSON.parse(localStorage.getItem('vodg-favorites') || '[]');
        console.log('No items provided to updateFavoritesList, loaded from localStorage:', items);
    }

    // Ensure items is an array
    if (!Array.isArray(items)) {
        console.error('updateFavoritesList received non-array items:', items);
        items = [];
    }

    if (items.length === 0) {
        listElement.innerHTML = `
            <li class="history-empty">
                <div class="history-empty-icon">⭐</div>
                <div class="history-empty-text">暂无收藏内容</div>
            </li>
        `;
        return;
    }

    let links = '';
    for (const item of items) {
        // Ensure the item has all required properties
        if (item && item.url && item.title) {
            // Format the title (remove episode info if it's in the title)
            let cleanTitle = item.title;
            if (cleanTitle.includes('第') && cleanTitle.includes('集')) {
                cleanTitle = cleanTitle.replace(/第\s*\d+\s*集/, '').trim();
            }

            // Create list item with poster if available
            if (item.poster) {
                const vodId = item.id;
                let episodeInfo = '';
                let progressBar = '';
                let progressPercent = 0;
                let episodeText = '';

                // Try to get the latest episode and progress from history
                try {
                    // Check for episode info in the URL or item
                    const urlParams = new URLSearchParams(item.url.split('?')[1] || '');
                    let epParam = urlParams.get('ep');

                    // We don't need to check history here anymore
                    // The updateFavoritesFromHistory function now handles this logic

                    // Prioritize episode from URL (which is updated by updateFavoritesFromHistory)
                    const episode = epParam || item.episode;

                    if (episode) {
                        episodeText = `第 ${episode} 集`;

                        // Get progress for this episode
                        const playCache = JSON.parse(localStorage.getItem('vodg-play') || '{}');
                        if (playCache[vodId] && playCache[vodId][episode]) {
                            progressPercent = Math.round(playCache[vodId][episode].playProgressRate * 100);

                            // Create progress bar
                            progressBar = `
                                <div class="viewing-history-item-progress-container">
                                    <div class="viewing-history-item-progress-bar" style="width: ${progressPercent}%"></div>
                                </div>
                                <span class="viewing-history-item-progress-text">${progressPercent}%</span>
                            `;
                        }
                    } else {
                        // Check if there's any watch progress for this VOD
                        const playCache = JSON.parse(localStorage.getItem('vodg-play') || '{}');
                        if (playCache[vodId]) {
                            // Find the latest episode with progress
                            const episodes = Object.keys(playCache[vodId]);
                            if (episodes.length > 0) {
                                // Sort episodes by last updated time
                                episodes.sort((a, b) => {
                                    return (playCache[vodId][b].updated || 0) - (playCache[vodId][a].updated || 0);
                                });

                                const latestEp = episodes[0];
                                episodeText = `第 ${latestEp} 集`;
                                progressPercent = Math.round(playCache[vodId][latestEp].playProgressRate * 100);

                                // Create progress bar
                                progressBar = `
                                    <div class="viewing-history-item-progress-container">
                                        <div class="viewing-history-item-progress-bar" style="width: ${progressPercent}%"></div>
                                    </div>
                                    <span class="viewing-history-item-progress-text">${progressPercent}%</span>
                                `;

                                // Update URL to point to the latest episode
                                const urlObj = new URL(item.url, window.location.origin);
                                urlObj.searchParams.set('ep', latestEp);

                                // Use the channel from the play cache if available
                                if (playCache[vodId][latestEp].ch) {
                                    urlObj.searchParams.set('ch', playCache[vodId][latestEp].ch);
                                }

                                if (urlObj.pathname.includes('/detail/')) {
                                    urlObj.pathname = urlObj.pathname.replace('/detail/', '/play/');
                                }
                                item.url = urlObj.pathname + urlObj.search;
                            }
                        }
                    }
                } catch (e) {
                    console.error('Error getting episode/progress info:', e);
                }

                // Add episode info if available
                if (episodeText) {
                    episodeInfo = `<div class="viewing-history-item-episode">${episodeText}</div>`;
                }

                links += `
                    <li class="viewing-history-item is-favorite">
                        <div class="viewing-history-item-container" onclick="window.location.href='${item.url}'">
                            <div class="viewing-history-item-poster">
                                <img src="${item.poster}" alt="${cleanTitle}" loading="lazy" onerror="this.parentNode.innerHTML='<div class=\\'viewing-history-item-poster-fallback\\'>🎬</div>'">
                                <div class="viewing-history-item-favorite-badge">★</div>
                            </div>
                            <div class="viewing-history-item-content">
                                <div class="viewing-history-item-title">${cleanTitle}</div>
                                ${episodeInfo}
                                ${progressBar}
                            </div>
                            <div class="viewing-history-item-actions">
                                <button class="viewing-history-item-delete"
                                      onclick="event.preventDefault(); event.stopPropagation(); removeFavoriteItem('${item.id}');"
                                      title="从收藏夹中删除" aria-label="从收藏夹中删除">&times;</button>
                            </div>
                        </div>
                    </li>
                `;
            } else {
                // Simple list item for items without poster (like search history)
                links += `
                    <li class="history-item">
                        <div class="history-item-container">
                            <a href="${item.url}" rel="nofollow" title="${item.title}">
                                <span class="history-item-icon">⭐</span>
                                <span class="history-item-title">${item.title}</span>
                            </a>
                            <div class="history-item-actions">
                                <button class="history-item-delete"
                                      onclick="event.preventDefault(); event.stopPropagation(); removeFavoriteItem('${item.id}');"
                                      title="从收藏夹中删除" aria-label="从收藏夹中删除">&times;</button>
                            </div>
                        </div>
                    </li>
                `;
            }
        }
    }
    listElement.innerHTML = links;
};

// Function to toggle favorite status from history list
window.toggleFavoriteFromHistory = function(vodId, url, title, poster, episode, channel) {
    // Prevent the click from propagating to the container
    event.preventDefault();
    event.stopPropagation();

    // Create the item object
    const item = {
        id: vodId,
        url: url,
        title: title,
        poster: poster
    };

    // Add episode info if available
    if (episode) {
        item.episode = episode;
    }

    // Add channel info if available
    if (channel) {
        item.channel = channel;
    }

    // Toggle favorite status
    const added = addToFavorites(item);

    // Real-time updates for both history and favorites
    // Update history dropdown if it exists and is open
    if (typeof window.loadHistoryItems === 'function') {
        const historyDropdown = document.getElementById('history-dropdown');
        if (historyDropdown && historyDropdown.classList.contains('show')) {
            window.loadHistoryItems();
        }
    }

    // Update favorites dropdown if it exists and is open
    if (typeof window.loadFavoritesItems === 'function') {
        const favoritesDropdown = document.getElementById('favorites-dropdown');
        if (favoritesDropdown && favoritesDropdown.classList.contains('show')) {
            loadFavoritesItems();
        }
    }

    // Show feedback
    const historyContent = document.getElementById('history-dropdown');
    if (historyContent) {
        const feedbackEl = document.createElement('div');
        feedbackEl.className = 'history-feedback';
        feedbackEl.textContent = added ? '已添加到收藏夹' : '已从收藏夹中删除';
        feedbackEl.style.position = 'absolute';
        feedbackEl.style.bottom = '10px';
        feedbackEl.style.left = '50%';
        feedbackEl.style.transform = 'translateX(-50%)';
        feedbackEl.style.backgroundColor = 'var(--secondary)';
        feedbackEl.style.color = 'white';
        feedbackEl.style.padding = '5px 10px';
        feedbackEl.style.borderRadius = '4px';
        feedbackEl.style.fontSize = 'var(--font-size-sm)';
        feedbackEl.style.opacity = '0';
        feedbackEl.style.transition = 'opacity 0.3s ease';
        feedbackEl.style.zIndex = '1000';

        historyContent.appendChild(feedbackEl);

        // Show and then hide the feedback
        setTimeout(() => {
            feedbackEl.style.opacity = '1';
            setTimeout(() => {
                feedbackEl.style.opacity = '0';
                setTimeout(() => {
                    if (historyContent.contains(feedbackEl)) {
                        historyContent.removeChild(feedbackEl);
                    }
                }, 300);
            }, 1500);
        }, 10);
    }

    return added;
};

// Add favorites button to VOD detail pages
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a VOD detail page
    const vodIdElement = document.querySelector('[data-vod-id]');
    if (vodIdElement) {
        const vodId = vodIdElement.getAttribute('data-vod-id');
        if (vodId) {
            // Get VOD details - prioritize detail-title class (new design) over vod-title (old design)
            const vodTitle = document.querySelector('.detail-title')?.textContent ||
                             document.querySelector('.vod-title')?.textContent ||
                             'Unknown Title';
            const vodPoster = document.querySelector('.detail-poster img')?.src ||
                              document.querySelector('.vod-poster img')?.src ||
                              '';
            const currentUrl = window.location.href;

            // Create favorites button
            const favoriteButton = document.createElement('button');
            favoriteButton.className = 'favorite-button';
            favoriteButton.setAttribute('aria-label', '收藏');
            favoriteButton.innerHTML = isInFavorites(vodId) ?
                '<span class="favorite-icon active">★</span> 已收藏' :
                '<span class="favorite-icon">☆</span> 收藏';

            // Add click event
            favoriteButton.addEventListener('click', function() {
                const item = {
                    id: vodId,
                    url: currentUrl,
                    title: vodTitle,
                    poster: vodPoster
                };

                const added = addToFavorites(item);

                // Update button appearance
                if (added) {
                    favoriteButton.innerHTML = '<span class="favorite-icon active">★</span> 已收藏';
                } else {
                    favoriteButton.innerHTML = '<span class="favorite-icon">☆</span> 收藏';
                }

                // Real-time updates for both history and favorites
                // Update history dropdown if it exists and is open
                if (typeof window.loadHistoryItems === 'function') {
                    const historyDropdown = document.getElementById('history-dropdown');
                    if (historyDropdown && historyDropdown.classList.contains('show')) {
                        window.loadHistoryItems();
                    }
                }

                // Update favorites dropdown if it exists and is open
                if (typeof window.loadFavoritesItems === 'function') {
                    const favoritesDropdown = document.getElementById('favorites-dropdown');
                    if (favoritesDropdown && favoritesDropdown.classList.contains('show')) {
                        loadFavoritesItems();
                    }
                }

                // Show feedback
                const feedbackEl = document.createElement('div');
                feedbackEl.className = 'detail-feedback';
                feedbackEl.textContent = added ? '已添加到收藏夹' : '已从收藏夹中删除';
                feedbackEl.style.position = 'fixed';
                feedbackEl.style.bottom = '20px';
                feedbackEl.style.left = '50%';
                feedbackEl.style.transform = 'translateX(-50%)';
                feedbackEl.style.backgroundColor = 'var(--secondary)';
                feedbackEl.style.color = 'white';
                feedbackEl.style.padding = '8px 16px';
                feedbackEl.style.borderRadius = '4px';
                feedbackEl.style.fontSize = 'var(--font-size-sm)';
                feedbackEl.style.opacity = '0';
                feedbackEl.style.transition = 'opacity 0.3s ease';
                feedbackEl.style.zIndex = '1000';

                document.body.appendChild(feedbackEl);

                // Show and then hide the feedback
                setTimeout(() => {
                    feedbackEl.style.opacity = '1';
                    setTimeout(() => {
                        feedbackEl.style.opacity = '0';
                        setTimeout(() => {
                            if (document.body.contains(feedbackEl)) {
                                document.body.removeChild(feedbackEl);
                            }
                        }, 300);
                    }, 1500);
                }, 10);
            });

            // Find a good place to insert the button
            const actionButtons = document.querySelector('.vod-actions');
            if (actionButtons) {
                actionButtons.appendChild(favoriteButton);
            } else {
                // If no action buttons container, create one
                const vodInfo = document.querySelector('.detail-info') || document.querySelector('.vod-info');
                if (vodInfo) {
                    const actionsDiv = document.createElement('div');
                    actionsDiv.className = 'vod-actions';
                    actionsDiv.appendChild(favoriteButton);

                    // Insert after the title for better placement
                    const titleElement = vodInfo.querySelector('.detail-title') || vodInfo.querySelector('.vod-title');
                    if (titleElement && titleElement.nextSibling) {
                        vodInfo.insertBefore(actionsDiv, titleElement.nextSibling);
                    } else {
                        vodInfo.appendChild(actionsDiv);
                    }
                }
            }
        }
    }
});
