/* Direct Navigation CSS - Simple, direct styling for mobile navigation */

/* Ensure bottom navigation is hidden on desktop */
@media (min-width: 769px) {
  .bottom-nav {
    display: none !important;
    visibility: hidden !important;
  }
}

/* Ensure bottom navigation is always visible and clickable on mobile only */
@media (max-width: 768px) {
  .bottom-nav {
    display: flex !important;
    visibility: visible !important;
    z-index: 9999 !important;
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
  }

  /* Ensure bottom navigation buttons are always clickable */
  .bottom-nav-btn {
    z-index: 10000 !important;
    position: relative !important;
    pointer-events: auto !important;
  }
}

/* Ensure categories dropdown is properly styled */
.categories-content {
  display: none;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: -1 !important;
  transform: translateY(100%) !important;
  transition: transform 0.3s ease !important;
  overflow-y: auto !important; /* Allow scrolling within the dropdown */
  -webkit-overflow-scrolling: touch !important; /* Smooth scrolling on iOS */
  overscroll-behavior: contain !important; /* Prevent scroll chaining */
  touch-action: pan-y !important; /* Allow only vertical touch actions */
}

.categories-content.show {
  display: block !important;
  z-index: 1000 !important;
  transform: translateY(0) !important;
}

/* Ensure favorites dropdown is properly styled */
.favorites-content {
  display: none;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: -1 !important;
  transform: translateY(100%) !important;
  transition: transform 0.3s ease !important;
  overflow-y: auto !important; /* Allow scrolling within the dropdown */
  -webkit-overflow-scrolling: touch !important; /* Smooth scrolling on iOS */
  overscroll-behavior: contain !important; /* Prevent scroll chaining */
  touch-action: pan-y !important; /* Allow only vertical touch actions */
}

.favorites-content.show {
  display: block !important;
  z-index: 1000 !important;
  transform: translateY(0) !important;
}

/* Ensure history dropdown is properly styled */
.history-content {
  display: none;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: -1 !important;
  transform: translateY(100%) !important;
  transition: transform 0.3s ease !important;
  overflow-y: auto !important; /* Allow scrolling within the dropdown */
  -webkit-overflow-scrolling: touch !important; /* Smooth scrolling on iOS */
  overscroll-behavior: contain !important; /* Prevent scroll chaining */
  touch-action: pan-y !important; /* Allow only vertical touch actions */
}

.history-content.show {
  display: block !important;
  z-index: 1000 !important;
  transform: translateY(0) !important;
}

/* Ensure mobile search overlay is properly styled */
.mobile-search-overlay {
  display: flex !important; /* Use flex instead of none to maintain layout */
  visibility: hidden; /* Hide it initially but keep the layout */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: -1 !important;
  background-color: var(--bg-dark) !important;
  overflow-y: auto !important; /* Allow scrolling within the overlay */
  -webkit-overflow-scrolling: touch !important; /* Smooth scrolling on iOS */
  overscroll-behavior: contain !important; /* Prevent scroll chaining */
  touch-action: pan-y !important; /* Allow only vertical touch actions */
  align-items: center !important;
  justify-content: center !important;
}

.mobile-search-overlay.active {
  visibility: visible !important;
  z-index: 1000 !important;
  opacity: 1 !important;
}

/* Hide all backdrops - they are not needed */
.categories-backdrop,
.favorites-backdrop,
.history-backdrop {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Ensure close buttons are visible and clickable */
.categories-close,
.favorites-close,
.history-close,
.mobile-search-close {
  z-index: 1001 !important;
  position: relative !important;
  pointer-events: auto !important;
  cursor: pointer !important;
  font-size: 24px !important;
  font-weight: bold !important;
  color: var(--text-primary) !important;
}

/* Add padding to main content to prevent bottom nav from covering content - mobile only */
@media (max-width: 768px) {
  main.main-content {
    padding-bottom: 70px !important;
  }
}

/* Body styles when a popup is open */
body[style*="position: fixed"] {
  width: 100% !important;
  height: 100% !important;
  overflow: hidden !important;
  touch-action: none !important;
  -webkit-overflow-scrolling: none !important;
  overscroll-behavior: none !important;
}

/* Prevent scrolling on html when body is fixed */
html:has(body[style*="position: fixed"]) {
  overflow: hidden !important;
}

/* Ensure content inside popups can scroll */
.categories-content .categories-body,
.favorites-content .favorites-body,
.history-content .history-body,
.mobile-search-overlay .mobile-search-content {
  overflow-y: auto !important;
  -webkit-overflow-scrolling: touch !important;
  max-height: calc(100% - 60px) !important; /* Account for header */
  overscroll-behavior: contain !important;
}
