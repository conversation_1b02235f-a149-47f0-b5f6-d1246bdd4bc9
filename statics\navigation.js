/**
 * Navigation System - Modular and Optimized
 * Version: 2.0 - Extracted from base.html for better maintainability
 * 
 * Handles all navigation-related functionality:
 * - Categories dropdown
 * - History management
 * - Favorites management
 * - Mobile search overlay
 * - Bottom navigation
 */

'use strict';

class NavigationManager {
  constructor() {
    this.activeDropdown = null;
    this.clickOutsideHandler = null;
    this.isInitialized = false;
    
    // Bind methods to preserve context
    this.handleClickOutside = this.handleClickOutside.bind(this);
    this.handleEscapeKey = this.handleEscapeKey.bind(this);
    this.handleResize = this.handleResize.bind(this);
  }
  
  /**
   * Initialize the navigation system
   */
  init() {
    if (this.isInitialized) return;
    
    Core.Logger.log('Initializing Navigation Manager');
    
    this.setupEventListeners();
    this.setupCategories();
    this.setupMobileNavigation();
    this.setupAccessibility();
    
    this.isInitialized = true;
    Core.Logger.log('Navigation Manager initialized');
  }
  
  /**
   * Setup global event listeners
   */
  setupEventListeners() {
    // Global escape key handler
    Core.DOM.on(document, 'keydown', this.handleEscapeKey);
    
    // Window resize handler
    Core.DOM.on(window, 'resize', Core.Performance.debounce(this.handleResize, 250));
    
    // Scroll handler for desktop dropdowns
    Core.DOM.on(window, 'scroll', Core.Performance.throttle(() => {
      if (!Core.Environment.isMobile() && this.activeDropdown) {
        this.closeAllDropdowns();
      }
    }, 100));
  }
  
  /**
   * Setup categories functionality
   */
  setupCategories() {
    const categoriesButton = Core.DOM.$('.category-button');
    const categoriesList = Core.DOM.$('#categories-list');
    
    if (categoriesButton) {
      Core.DOM.on(categoriesButton, 'click', (e) => {
        e.preventDefault();
        this.toggleCategories();
      });
    }
    
    // Populate categories from config
    if (categoriesList && window.siteConfig?.categories) {
      this.populateCategories(categoriesList, window.siteConfig.categories);
    }
  }
  
  /**
   * Populate categories list from configuration
   */
  populateCategories(container, categories) {
    container.innerHTML = '';
    
    categories.forEach(category => {
      const listItem = Core.DOM.createElement('li');
      const link = Core.DOM.createElement('a', {
        href: category.url,
        textContent: category.name,
        'aria-label': `Browse ${category.name} category`
      });
      
      listItem.appendChild(link);
      container.appendChild(listItem);
    });
  }
  
  /**
   * Setup mobile navigation
   */
  setupMobileNavigation() {
    // Bottom navigation buttons
    const bottomNavButtons = Core.DOM.$$('.bottom-nav-btn[data-action]');
    
    bottomNavButtons.forEach(button => {
      const action = button.dataset.action;
      
      Core.DOM.on(button, 'click', (e) => {
        e.preventDefault();
        this.handleBottomNavAction(action);
      });
    });
    
    // Mobile search close button
    const searchCloseBtn = Core.DOM.$('.mobile-search-close');
    if (searchCloseBtn) {
      Core.DOM.on(searchCloseBtn, 'click', () => this.closeMobileSearch());
    }
    
    // Mobile search overlay click-to-close
    const searchOverlay = Core.DOM.$('.mobile-search-overlay');
    if (searchOverlay) {
      Core.DOM.on(searchOverlay, 'click', (e) => {
        if (e.target === searchOverlay) {
          this.closeMobileSearch();
        }
      });
    }
  }
  
  /**
   * Handle bottom navigation actions
   */
  handleBottomNavAction(action) {
    // Close any open dropdowns first
    this.closeAllDropdowns();
    
    switch (action) {
      case 'categories':
        this.toggleCategories();
        break;
      case 'search':
        this.openMobileSearch();
        break;
      case 'history':
        this.toggleHistory();
        break;
      case 'favorites':
        this.toggleFavorites();
        break;
      default:
        Core.Logger.warn('Unknown navigation action:', action);
    }
    
    // Update active state
    this.updateBottomNavActiveState(action);
  }
  
  /**
   * Update bottom navigation active state
   */
  updateBottomNavActiveState(activeAction) {
    const buttons = Core.DOM.$$('.bottom-nav-btn');
    
    buttons.forEach(button => {
      button.classList.remove('active');
      
      if (button.dataset.action === activeAction) {
        button.classList.add('active');
      }
    });
  }
  
  /**
   * Toggle categories dropdown
   */
  toggleCategories() {
    const dropdown = Core.DOM.$('#categories-dropdown');
    if (!dropdown) return;
    
    if (this.isDropdownOpen(dropdown)) {
      this.closeDropdown(dropdown);
    } else {
      this.openDropdown(dropdown, 'categories');
    }
  }
  
  /**
   * Toggle history dropdown
   */
  toggleHistory() {
    const dropdown = Core.DOM.$('#history-dropdown');
    if (!dropdown) return;
    
    if (this.isDropdownOpen(dropdown)) {
      this.closeDropdown(dropdown);
    } else {
      this.openDropdown(dropdown, 'history');
      // Load history items if function exists
      if (typeof window.loadHistoryItems === 'function') {
        window.loadHistoryItems();
      }
    }
  }
  
  /**
   * Toggle favorites dropdown
   */
  toggleFavorites() {
    const dropdown = Core.DOM.$('#favorites-dropdown');
    if (!dropdown) return;
    
    if (this.isDropdownOpen(dropdown)) {
      this.closeDropdown(dropdown);
    } else {
      this.openDropdown(dropdown, 'favorites');
      // Load favorites items if function exists
      if (typeof window.loadFavoritesItems === 'function') {
        window.loadFavoritesItems();
      }
    }
  }
  
  /**
   * Open mobile search overlay
   */
  openMobileSearch() {
    const overlay = Core.DOM.$('.mobile-search-overlay');
    if (!overlay) return;
    
    // Show overlay
    overlay.classList.add('active');
    overlay.style.display = 'flex';
    overlay.style.visibility = 'visible';
    overlay.style.zIndex = '1000';
    
    // Disable body scroll
    document.body.style.overflow = 'hidden';
    
    // Focus search input
    setTimeout(() => {
      const searchInput = Core.DOM.$('.mobile-search-input');
      if (searchInput) {
        searchInput.focus();
      }
    }, 300);
    
    this.activeDropdown = 'search';
    Core.Logger.log('Mobile search opened');
  }
  
  /**
   * Close mobile search overlay
   */
  closeMobileSearch() {
    const overlay = Core.DOM.$('.mobile-search-overlay');
    if (!overlay) return;
    
    // Hide overlay
    overlay.classList.remove('active');
    
    // Re-enable body scroll
    document.body.style.overflow = '';
    
    // Reset styles after animation
    setTimeout(() => {
      overlay.style.display = 'none';
      overlay.style.visibility = 'hidden';
      overlay.style.zIndex = '-1';
    }, 300);
    
    this.activeDropdown = null;
    this.resetBottomNavActiveState();
    Core.Logger.log('Mobile search closed');
  }
  
  /**
   * Generic dropdown open method
   */
  openDropdown(dropdown, type) {
    if (!dropdown) return;
    
    // Close any other open dropdowns
    this.closeAllDropdowns();
    
    // Position dropdown appropriately
    this.positionDropdown(dropdown, type);
    
    // Show dropdown
    dropdown.classList.add('show');
    dropdown.style.visibility = 'visible';
    dropdown.style.zIndex = '100';
    dropdown.style.pointerEvents = 'auto';
    
    // Handle mobile full-screen
    if (Core.Environment.isMobile()) {
      dropdown.style.transform = 'translateY(0)';
      document.body.style.overflow = 'hidden';
    }
    
    // Setup click outside handler
    this.setupClickOutsideHandler(dropdown);
    
    this.activeDropdown = type;
    Core.Logger.log(`${type} dropdown opened`);
  }
  
  /**
   * Generic dropdown close method
   */
  closeDropdown(dropdown) {
    if (!dropdown) return;
    
    dropdown.classList.remove('show');
    dropdown.style.visibility = 'hidden';
    dropdown.style.zIndex = '-1';
    dropdown.style.pointerEvents = 'none';
    
    // Handle mobile cleanup
    if (Core.Environment.isMobile()) {
      dropdown.style.transform = '';
      document.body.style.overflow = '';
    }
    
    this.removeClickOutsideHandler();
    this.activeDropdown = null;
    this.resetBottomNavActiveState();
  }
  
  /**
   * Close all open dropdowns
   */
  closeAllDropdowns() {
    const dropdowns = Core.DOM.$$('.categories-content, .history-content, .favorites-content');
    
    dropdowns.forEach(dropdown => {
      if (this.isDropdownOpen(dropdown)) {
        this.closeDropdown(dropdown);
      }
    });
    
    this.closeMobileSearch();
  }
  
  /**
   * Check if dropdown is open
   */
  isDropdownOpen(dropdown) {
    return dropdown && dropdown.classList.contains('show');
  }
  
  /**
   * Position dropdown relative to trigger button
   */
  positionDropdown(dropdown, type) {
    if (Core.Environment.isMobile()) {
      // Mobile: full screen positioning handled by CSS
      return;
    }
    
    // Desktop: position near trigger button
    const button = Core.DOM.$(`.${type}-button`);
    if (!button) return;
    
    const buttonRect = button.getBoundingClientRect();
    const dropdownRect = dropdown.getBoundingClientRect();
    
    // Position below button with some spacing
    dropdown.style.top = `${buttonRect.bottom + 10}px`;
    dropdown.style.right = `${window.innerWidth - buttonRect.right}px`;
    dropdown.style.left = 'auto';
    dropdown.style.transform = 'none';
  }
  
  /**
   * Setup click outside handler
   */
  setupClickOutsideHandler(dropdown) {
    this.removeClickOutsideHandler();
    
    this.clickOutsideHandler = (e) => {
      if (!dropdown.contains(e.target) && !this.isClickOnTrigger(e.target)) {
        this.closeDropdown(dropdown);
      }
    };
    
    // Delay to prevent immediate closure
    setTimeout(() => {
      Core.DOM.on(document, 'click', this.clickOutsideHandler);
    }, 10);
  }
  
  /**
   * Remove click outside handler
   */
  removeClickOutsideHandler() {
    if (this.clickOutsideHandler) {
      document.removeEventListener('click', this.clickOutsideHandler);
      this.clickOutsideHandler = null;
    }
  }
  
  /**
   * Check if click is on a trigger button
   */
  isClickOnTrigger(target) {
    return target.closest('.category-button, .history-button, .favorites-button, .bottom-nav-btn');
  }
  
  /**
   * Handle escape key press
   */
  handleEscapeKey(e) {
    if (e.key === 'Escape' && this.activeDropdown) {
      this.closeAllDropdowns();
    }
  }
  
  /**
   * Handle window resize
   */
  handleResize() {
    if (this.activeDropdown) {
      // Reposition dropdowns on resize
      const dropdown = Core.DOM.$(`#${this.activeDropdown}-dropdown`);
      if (dropdown && this.isDropdownOpen(dropdown)) {
        this.positionDropdown(dropdown, this.activeDropdown);
      }
    }
  }
  
  /**
   * Reset bottom navigation active state to home
   */
  resetBottomNavActiveState() {
    const buttons = Core.DOM.$$('.bottom-nav-btn');
    const homeButton = Core.DOM.$('.bottom-nav-btn[href="/"]');
    
    buttons.forEach(button => button.classList.remove('active'));
    
    if (homeButton) {
      homeButton.classList.add('active');
    }
  }
  
  /**
   * Setup accessibility features
   */
  setupAccessibility() {
    // Add ARIA attributes
    const dropdownTriggers = Core.DOM.$$('.category-button, .history-button, .favorites-button');
    
    dropdownTriggers.forEach(trigger => {
      trigger.setAttribute('aria-haspopup', 'true');
      trigger.setAttribute('aria-expanded', 'false');
    });
    
    // Update ARIA states when dropdowns open/close
    const originalOpenDropdown = this.openDropdown;
    const originalCloseDropdown = this.closeDropdown;
    
    this.openDropdown = (dropdown, type) => {
      originalOpenDropdown.call(this, dropdown, type);
      const trigger = Core.DOM.$(`.${type}-button`);
      if (trigger) trigger.setAttribute('aria-expanded', 'true');
    };
    
    this.closeDropdown = (dropdown) => {
      const type = this.activeDropdown;
      originalCloseDropdown.call(this, dropdown);
      if (type) {
        const trigger = Core.DOM.$(`.${type}-button`);
        if (trigger) trigger.setAttribute('aria-expanded', 'false');
      }
    };
  }
  
  /**
   * Destroy the navigation manager
   */
  destroy() {
    this.closeAllDropdowns();
    this.removeClickOutsideHandler();
    this.isInitialized = false;
    Core.Logger.log('Navigation Manager destroyed');
  }
}

// Create global instance
const Navigation = new NavigationManager();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => Navigation.init());
} else {
  Navigation.init();
}

// Make available globally for backward compatibility
window.Navigation = Navigation;
window.toggleCategories = () => Navigation.toggleCategories();
window.toggleHistory = () => Navigation.toggleHistory();
window.toggleFavorites = () => Navigation.toggleFavorites();
window.directToggleCategories = () => Navigation.toggleCategories();
window.directToggleHistory = () => Navigation.toggleHistory();
window.directToggleFavorites = () => Navigation.toggleFavorites();
window.directOpenMobileSearch = () => Navigation.openMobileSearch();
window.directCloseAll = () => Navigation.closeAllDropdowns();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = Navigation;
}
