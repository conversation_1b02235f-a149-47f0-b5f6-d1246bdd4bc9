<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #111827;
            color: #f9fafb;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #374151;
            border-radius: 8px;
            background: #1f2937;
        }
        .test-button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #1d4ed8;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background: #065f46; color: #d1fae5; }
        .error { background: #7f1d1d; color: #fecaca; }
        .info { background: #1e3a8a; color: #dbeafe; }
    </style>
</head>
<body>
    <h1>🧪 Refactoring Functionality Test</h1>
    
    <div class="test-section">
        <h2>📁 File Structure Test</h2>
        <div id="file-test-results"></div>
        <button class="test-button" onclick="testFileStructure()">Test File Structure</button>
    </div>
    
    <div class="test-section">
        <h2>🎨 CSS Variables Test</h2>
        <div id="css-test-results"></div>
        <button class="test-button" onclick="testCSSVariables()">Test CSS Variables</button>
    </div>
    
    <div class="test-section">
        <h2>⚙️ JavaScript Core Test</h2>
        <div id="js-test-results"></div>
        <button class="test-button" onclick="testJavaScript()">Test JavaScript Core</button>
    </div>
    
    <div class="test-section">
        <h2>🧭 Navigation Test</h2>
        <div id="nav-test-results"></div>
        <button class="test-button" onclick="testNavigation()">Test Navigation Functions</button>
    </div>

    <script>
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function testFileStructure() {
            clearResults('file-test-results');
            
            const criticalFiles = [
                '/statics/main.css',
                '/statics/core.js',
                '/statics/navigation.js',
                '/statics/css-variables.css',
                '/statics/direct-nav.js',
                '/statics/category-config.js'
            ];

            let passed = 0;
            let total = criticalFiles.length;

            criticalFiles.forEach(file => {
                fetch(file, { method: 'HEAD' })
                    .then(response => {
                        if (response.ok) {
                            addResult('file-test-results', `✅ ${file} - Found`, 'success');
                            passed++;
                        } else {
                            addResult('file-test-results', `❌ ${file} - Missing (${response.status})`, 'error');
                        }
                        
                        if (passed + (total - passed) === total) {
                            addResult('file-test-results', 
                                `📊 File Structure Test: ${passed}/${total} files found`, 
                                passed === total ? 'success' : 'error');
                        }
                    })
                    .catch(error => {
                        addResult('file-test-results', `❌ ${file} - Error: ${error.message}`, 'error');
                    });
            });
        }

        function testCSSVariables() {
            clearResults('css-test-results');
            
            const testElement = document.createElement('div');
            document.body.appendChild(testElement);
            
            const computedStyle = getComputedStyle(testElement);
            
            const variables = [
                '--primary',
                '--bg-dark',
                '--text-primary',
                '--spacing-4',
                '--radius',
                '--transition'
            ];

            let passed = 0;
            
            variables.forEach(variable => {
                const value = computedStyle.getPropertyValue(variable);
                if (value && value.trim() !== '') {
                    addResult('css-test-results', `✅ ${variable}: ${value.trim()}`, 'success');
                    passed++;
                } else {
                    addResult('css-test-results', `❌ ${variable} - Not defined`, 'error');
                }
            });
            
            document.body.removeChild(testElement);
            
            addResult('css-test-results', 
                `📊 CSS Variables Test: ${passed}/${variables.length} variables found`, 
                passed === variables.length ? 'success' : 'error');
        }

        function testJavaScript() {
            clearResults('js-test-results');
            
            const tests = [
                { name: 'Core Library', check: () => typeof window.Core !== 'undefined' },
                { name: 'Core.Logger', check: () => typeof window.Core?.Logger?.log === 'function' },
                { name: 'Core.DOM', check: () => typeof window.Core?.DOM?.$ === 'function' },
                { name: 'Core.Storage', check: () => typeof window.Core?.Storage?.get === 'function' },
                { name: 'Core.Performance', check: () => typeof window.Core?.Performance?.debounce === 'function' }
            ];

            let passed = 0;
            
            tests.forEach(test => {
                try {
                    if (test.check()) {
                        addResult('js-test-results', `✅ ${test.name} - Available`, 'success');
                        passed++;
                    } else {
                        addResult('js-test-results', `❌ ${test.name} - Not available`, 'error');
                    }
                } catch (error) {
                    addResult('js-test-results', `❌ ${test.name} - Error: ${error.message}`, 'error');
                }
            });
            
            addResult('js-test-results', 
                `📊 JavaScript Test: ${passed}/${tests.length} components working`, 
                passed === tests.length ? 'success' : 'error');
        }

        function testNavigation() {
            clearResults('nav-test-results');
            
            const tests = [
                { name: 'Navigation Object', check: () => typeof window.Navigation !== 'undefined' },
                { name: 'toggleCategories', check: () => typeof window.toggleCategories === 'function' },
                { name: 'toggleHistory', check: () => typeof window.toggleHistory === 'function' },
                { name: 'toggleFavorites', check: () => typeof window.toggleFavorites === 'function' },
                { name: 'directToggleCategories', check: () => typeof window.directToggleCategories === 'function' },
                { name: 'directToggleHistory', check: () => typeof window.directToggleHistory === 'function' },
                { name: 'directToggleFavorites', check: () => typeof window.directToggleFavorites === 'function' },
                { name: 'directCloseAll', check: () => typeof window.directCloseAll === 'function' }
            ];

            let passed = 0;
            
            tests.forEach(test => {
                try {
                    if (test.check()) {
                        addResult('nav-test-results', `✅ ${test.name} - Available`, 'success');
                        passed++;
                    } else {
                        addResult('nav-test-results', `❌ ${test.name} - Not available`, 'error');
                    }
                } catch (error) {
                    addResult('nav-test-results', `❌ ${test.name} - Error: ${error.message}`, 'error');
                }
            });
            
            addResult('nav-test-results', 
                `📊 Navigation Test: ${passed}/${tests.length} functions working`, 
                passed === tests.length ? 'success' : 'error');
        }

        // Auto-run tests when page loads
        window.addEventListener('load', function() {
            setTimeout(() => {
                testFileStructure();
                testCSSVariables();
                testJavaScript();
                testNavigation();
            }, 1000);
        });
    </script>
</body>
</html>
