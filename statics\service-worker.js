/**
 * Service Worker for Caching and Offline Support
 */

// Cache name with version
const CACHE_NAME = 'vodg-cache-v1';

// Resources to cache
const STATIC_RESOURCES = [
  '/',
  '/statics/console-production.js',
  '/statics/critical.css',
  '/statics/modern-ui.css',
  '/statics/style.css',
  '/statics/utils.js',
  '/statics/performance-optimizations.js',
  '/statics/optimized-history.js',
  '/statics/collapsible-header.js',
  '/statics/favorites.js',
  '/statics/direct-nav.js',
  '/statics/history-item.css',
  '/statics/history-tabs.css',
  '/statics/skeleton-loader.css',
  '/statics/favorites.css',
  '/statics/header-custom.css'
];

// Install event - cache static resources
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        // Caching static resources
        return cache.addAll(STATIC_RESOURCES);
      })
      .then(() => self.skipWaiting())
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            // Deleting old cache
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', event => {
  // Skip non-GET requests
  if (event.request.method !== 'GET') return;

  // Skip cross-origin requests
  if (!event.request.url.startsWith(self.location.origin)) return;

  // Skip API requests
  if (event.request.url.includes('/api/')) return;

  // Handle requests
  event.respondWith(
    caches.match(event.request)
      .then(cachedResponse => {
        // Return cached response if available
        if (cachedResponse) {
          return cachedResponse;
        }

        // Otherwise fetch from network
        return fetch(event.request)
          .then(response => {
            // Don't cache non-successful responses
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Clone the response
            const responseToCache = response.clone();

            // Cache the response
            caches.open(CACHE_NAME)
              .then(cache => {
                cache.put(event.request, responseToCache);
              });

            return response;
          })
          .catch(error => {
            // If both cache and network fail, return a fallback
            // Fetch failed

            // For HTML requests, return the offline page
            if (event.request.headers.get('accept').includes('text/html')) {
              return caches.match('/');
            }

            // For image requests, return a placeholder
            if (event.request.url.match(/\.(jpg|jpeg|png|gif|webp)$/)) {
              return new Response(
                '<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200"><rect width="200" height="200" fill="#1f2937"/><text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" font-family="sans-serif" font-size="24" fill="#9ca3af">Image</text></svg>',
                { headers: { 'Content-Type': 'image/svg+xml' } }
              );
            }

            // For other requests, return an empty response
            return new Response('');
          });
      })
  );
});

// Handle messages from clients
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
