/**
 * Site Configuration
 * This file contains configurable settings for the site
 */

// Site configuration
const siteConfig = {
    // Site title
    siteTitle: "",

    // Authentication configuration
    auth: {
        // Enable or disable Firebase authentication features (login/register)
        enabled: true,

        // Firebase configuration
        firebase: {
           apiKey: "AIzaSyCWn8425dHrPDDfb1yFSqsNQkHOIsajYW8",
            authDomain: "vod-sync.firebaseapp.com",
            projectId: "vod-sync",
            storageBucket: "vod-sync.firebasestorage.app",
            messagingSenderId: "155908265004",
            appId: "1:155908265004:web:9252173003ca106cfded89"
        }
    },

    // Categories configuration
    categories: [
        {
            title: "动漫",
            path: "/vod/list/?kw=动漫",
            subcategories: [
                { title: "国产动漫", path: "/vod/list/?kw=国产动漫" },
                { title: "日本动漫", path: "/vod/list/?kw=日本动漫" },
                { title: "韩国动漫", path: "/vod/list/?kw=韩国动漫" },
                { title: "欧美动漫", path: "/vod/list/?kw=欧美动漫" },
                { title: "港台动漫", path: "/vod/list/?kw=港台动漫" },
                { title: "海外动漫", path: "/vod/list/?kw=海外动漫" }
            ]
        },
        {
            title: "电影",
            path: "/vod/list/?kw=电影",
            subcategories: [
                { title: "动作片", path: "/vod/list/?kw=动作片" },
                { title: "喜剧片", path: "/vod/list/?kw=喜剧片" },
                { title: "爱情片", path: "/vod/list/?kw=爱情片" },
                { title: "科幻片", path: "/vod/list/?kw=科幻片" },
                { title: "恐怖片", path: "/vod/list/?kw=恐怖片" },
                { title: "剧情片", path: "/vod/list/?kw=剧情片" }
            ]
        },
        {
            title: "连续剧",
            path: "/vod/list/?kw=连续剧,电视剧",
            subcategories: [
                { title: "国产剧", path: "/vod/list/?kw=国产剧,大陆剧" },
                { title: "香港剧", path: "/vod/list/?kw=香港剧" },
                { title: "台湾剧", path: "/vod/list/?kw=台湾剧" },
                { title: "日本剧", path: "/vod/list/?kw=日本剧" },
                { title: "韩国剧", path: "/vod/list/?kw=韩国剧" },
                { title: "欧美剧", path: "/vod/list/?kw=欧美剧" },
                { title: "Netflix自制剧", path: "/vod/list/?kw=Netflix自制剧" }
            ]
        },
        {
            title: "综艺",
            path: "/vod/list/?kw=综艺",
            subcategories: [
                { title: "大陆综艺", path: "/vod/list/?kw=大陆综艺" },
                { title: "日韩综艺", path: "/vod/list/?kw=日韩综艺" },
                { title: "港台综艺", path: "/vod/list/?kw=港台综艺" },
                { title: "欧美综艺", path: "/vod/list/?kw=欧美综艺" }
            ]
        },
        {
            title: "短剧",
            path: "/vod/list/?kw=短剧,短剧大全,爽文短剧",
            subcategories: [
                { title: "女频恋爱", path: "/vod/list/?kw=女频恋爱" },
                { title: "反转爽剧", path: "/vod/list/?kw=反转爽剧" },
                { title: "脑洞悬疑", path: "/vod/list/?kw=脑洞悬疑" },
                { title: "年代穿越", path: "/vod/list/?kw=年代穿越" },
                { title: "古装仙侠", path: "/vod/list/?kw=古装仙侠" },
                { title: "现代都市", path: "/vod/list/?kw=现代都市" }
            ]
        }
    ]
};

// Make configuration available globally
window.siteConfig = siteConfig;
