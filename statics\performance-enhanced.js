/**
 * Enhanced Performance Optimizations
 * Consolidated performance improvements for the application
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize all performance optimizations
  PerformanceOptimizer.init();
});

/**
 * Performance Optimizer
 * Handles various performance optimizations throughout the application
 */
const PerformanceOptimizer = {
  /**
   * Initialize all performance optimizations
   */
  init: function() {
    // First check if Utils is available, if not use inline fallbacks
    const hasUtils = typeof window.Utils !== 'undefined';
    
    // Apply optimizations in order of importance
    this.optimizeScrollListeners();
    this.optimizeDOMUpdates();
    this.optimizeNetworkRequests();
    this.deferNonCriticalOperations();
    
    // Log optimization status in development
    if (hasUtils) {
      Utils.logger.info('Performance optimizations applied');
    } else if (this.isDev()) {
      console.info('Performance optimizations applied (Utils not available)');
    }
  },
  
  /**
   * Optimize scroll event listeners
   * Use passive listeners and throttling for better performance
   */
  optimizeScrollListeners: function() {
    // Optimize existing scroll listeners
    const scrollHandler = typeof Utils !== 'undefined' 
      ? Utils.perf.throttle(this.handleScroll.bind(this), 100)
      : this.throttle(this.handleScroll.bind(this), 100);
    
    // Remove any existing listeners to prevent duplicates
    window.removeEventListener('scroll', scrollHandler);
    
    // Add optimized scroll listener with passive option
    window.addEventListener('scroll', scrollHandler, { passive: true });
    
    // Save the handler for potential cleanup
    this.scrollHandler = scrollHandler;
  },
  
  /**
   * Handle scroll events
   * Consolidate all scroll-related updates
   */
  handleScroll: function() {
    // Handle header visibility
    this.handleHeaderVisibility();
    
    // Perform lazy loading for visible elements
    this.lazyLoadVisibleElements();
    
    // Collapse relevant dropdowns on scroll (desktop only)
    if (window.innerWidth > 768) {
      // History dropdown
      const historyContent = document.getElementById('history-dropdown');
      if (historyContent && historyContent.classList.contains('show')) {
        const closeHistory = window.closeHistory || function() {
          historyContent.classList.remove('show');
        };
        closeHistory();
      }
      
      // Favorites dropdown
      const favoritesContent = document.getElementById('favorites-dropdown');
      if (favoritesContent && favoritesContent.classList.contains('show')) {
        const closeFavorites = window.closeFavorites || function() {
          favoritesContent.classList.remove('show');
        };
        closeFavorites();
      }
      
      // Categories dropdown
      const categoriesContent = document.getElementById('categories-dropdown');
      if (categoriesContent && categoriesContent.classList.contains('show')) {
        const closeCategories = window.closeCategories || function() {
          categoriesContent.classList.remove('show');
        };
        closeCategories();
      }
    }
  },
  
  /**
   * Handle header visibility on scroll
   * Implements a dynamic header that hides/shows based on scroll direction
   */
  handleHeaderVisibility: function() {
    const header = document.querySelector('.site-header');
    if (!header) return;
    
    // Get scroll position
    const currentScroll = window.scrollY;
    
    // Initialize lastScroll if it's the first time
    if (typeof this.lastScroll === 'undefined') {
      this.lastScroll = currentScroll;
      return;
    }
    
    // Determine scroll direction and amount
    const scrollDelta = currentScroll - this.lastScroll;
    const scrollThreshold = 5; // Minimum pixels to consider as a direction change
    
    // Apply hide/show logic based on scroll direction
    if (currentScroll > 100) { // Only apply when scrolled down a bit
      if (scrollDelta > scrollThreshold) {
        // Scrolling down - hide header
        header.classList.add('header-hidden');
      } else if (scrollDelta < -scrollThreshold) {
        // Scrolling up - show header
        header.classList.remove('header-hidden');
      }
    } else {
      // At the top - always show header
      header.classList.remove('header-hidden');
    }
    
    // Update last scroll position
    this.lastScroll = currentScroll;
  },
  
  /**
   * Lazy load elements as they become visible
   * Load images, iframes and other heavy content only when needed
   */
  lazyLoadVisibleElements: function() {
    // Use IntersectionObserver if not already set up
    if (!this.lazyLoadObserver) {
      // Skip if already using native lazy loading
      if ('loading' in HTMLImageElement.prototype) return;
      
      // Create observer
      this.lazyLoadObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const element = entry.target;
            
            // Handle different element types
            if (element.tagName === 'IMG') {
              // Handle lazy images
              const dataSrc = element.getAttribute('data-src');
              if (dataSrc) {
                element.src = dataSrc;
                element.removeAttribute('data-src');
              }
            } else if (element.classList.contains('progressive-image-container')) {
              // Handle progressive images
              const img = element.querySelector('img[data-src]');
              if (img) {
                const dataSrc = img.getAttribute('data-src');
                if (dataSrc) {
                  img.src = dataSrc;
                  img.removeAttribute('data-src');
                  img.classList.add('loaded');
                }
              }
            } else if (element.tagName === 'IFRAME') {
              // Handle iframes
              const dataSrc = element.getAttribute('data-src');
              if (dataSrc) {
                element.src = dataSrc;
                element.removeAttribute('data-src');
              }
            }
            
            // Stop observing once loaded
            this.lazyLoadObserver.unobserve(element);
          }
        });
      }, {
        rootMargin: '200px', // Load items 200px before they become visible
        threshold: 0.01
      });
      
      // Start observing all lazy elements
      document.querySelectorAll('img[data-src], .progressive-image-container, iframe[data-src]')
        .forEach(element => this.lazyLoadObserver.observe(element));
    }
  },
  
  /**
   * Optimize DOM updates for better performance
   * Use requestAnimationFrame and document fragments for DOM manipulations
   */
  optimizeDOMUpdates: function() {
    // Store original methods for potential restoration
    if (!window._originalAppendChild) {
      window._originalAppendChild = Element.prototype.appendChild;
    }
    
    // For batch DOM updates, use document fragments
    window.performBatchUpdate = function(container, elements) {
      if (!container) return;
      
      // Create a document fragment for batch insertion
      const fragment = document.createDocumentFragment();
      
      // Add all elements to the fragment
      elements.forEach(el => fragment.appendChild(el));
      
      // Append the fragment in one operation
      requestAnimationFrame(() => {
        container.appendChild(fragment);
      });
    };
  },
  
  /**
   * Optimize network requests
   * Implement request batching, caching, and prefetching
   */
  optimizeNetworkRequests: function() {
    // Set up request cache if supported
    if ('caches' in window) {
      // Cache API is available - implement custom caching strategy
      window.fetchWithCache = async function(url, options = {}, cacheName = 'app-cache') {
        try {
          // Try to get from cache first
          const cache = await caches.open(cacheName);
          const cachedResponse = await cache.match(url);
          
          if (cachedResponse) {
            // Return cached response but update cache in background
            const updateCachePromise = fetch(url, options).then(response => {
              if (response.ok) cache.put(url, response.clone());
              return response;
            }).catch(() => {});
            
            // Don't wait for the update, return cached version immediately
            return cachedResponse;
          }
          
          // If not in cache, fetch and cache
          const response = await fetch(url, options);
          if (response.ok) {
            cache.put(url, response.clone());
          }
          return response;
        } catch (error) {
          // Fall back to regular fetch if caching fails
          return fetch(url, options);
        }
      };
    }
    
    // Prefetch critical resources
    this.prefetchCriticalResources();
  },
  
  /**
   * Prefetch critical resources when network is idle
   */
  prefetchCriticalResources: function() {
    // Use requestIdleCallback if available, otherwise use a light timeout
    const schedulePrefetch = window.requestIdleCallback || 
      function(cb) { setTimeout(() => cb({ timeRemaining: () => 1000 }), 1000); };
    
    // Schedule prefetching when browser is idle
    schedulePrefetch(() => {
      // Prefetch resources common to most pages
      const resourcesToPreload = [
        '/statics/modern-ui.css',
        '/statics/deferred-firebase.js'
      ];
      
      resourcesToPreload.forEach(url => {
        // Create a prefetch link
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = url;
        document.head.appendChild(link);
      });
    });
  },
  
  /**
   * Defer non-critical operations to improve initial load performance
   */
  deferNonCriticalOperations: function() {
    // Use requestIdleCallback when available
    const scheduleTask = window.requestIdleCallback || 
      function(cb) { setTimeout(() => cb({ timeRemaining: () => 1000 }), 1000); };
    
    // Queue of deferred operations
    const deferredTasks = [
      // Analytics initialization
      () => {
        if (window.initializeAnalytics) {
          window.initializeAnalytics();
        }
      },
      
      // Register service worker
      () => {
        if ('serviceWorker' in navigator) {
          navigator.serviceWorker.register('/statics/service-worker.js')
            .catch(error => {
              if (typeof Utils !== 'undefined') {
                Utils.logger.error('ServiceWorker registration failed:', error);
              }
            });
        }
      },
      
      // Load deferred scripts
      () => {
        const deferredScripts = [
          '/statics/firebase-sync.js',
          '/statics/category-config.js'
        ];
        
        deferredScripts.forEach(src => {
          const script = document.createElement('script');
          script.src = src;
          script.async = true;
          document.body.appendChild(script);
        });
      }
    ];
    
    // Process tasks when browser is idle
    let taskIndex = 0;
    const processTasks = (deadline) => {
      while (taskIndex < deferredTasks.length && deadline.timeRemaining() > 0) {
        try {
          deferredTasks[taskIndex]();
        } catch (error) {
          if (typeof Utils !== 'undefined') {
            Utils.logger.error('Deferred task error:', error);
          }
        }
        taskIndex++;
      }
      
      // Schedule more tasks if needed
      if (taskIndex < deferredTasks.length) {
        scheduleTask(processTasks);
      }
    };
    
    // Start processing tasks
    scheduleTask(processTasks);
  },
  
  /* Fallback utility methods if Utils is not available */
  
  /**
   * Check if in development environment (fallback)
   * @returns {boolean}
   */
  isDev: function() {
    return window.location.hostname === 'localhost' || 
           window.location.hostname === '127.0.0.1' ||
           window.location.hostname.includes('.local');
  },
  
  /**
   * Throttle function (fallback)
   * @param {Function} func - Function to throttle
   * @param {number} limit - Throttle limit in ms
   * @returns {Function} Throttled function
   */
  throttle: function(func, limit) {
    let inThrottle = false;
    return function() {
      const context = this;
      const args = arguments;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => {
          inThrottle = false;
        }, limit);
      }
    };
  }
};

// Add header styles if not already present
if (!document.querySelector('style#performance-styles')) {
  const style = document.createElement('style');
  style.id = 'performance-styles';
  style.textContent = `
    /* Header transition for hiding/showing on scroll */
    .site-header {
      position: sticky;
      top: 0;
      transition: transform 0.3s ease;
      z-index: var(--z-header, 100);
    }
    
    .site-header.header-hidden {
      transform: translateY(-100%);
    }
    
    /* Lazy loading styles */
    .progressive-image-container img {
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    .progressive-image-container img.loaded {
      opacity: 1;
    }
  `;
  document.head.appendChild(style);
}
