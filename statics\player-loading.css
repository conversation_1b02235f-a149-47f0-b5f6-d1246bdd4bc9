/* 
 * Optimized Player Loading Animations
 * Provides smooth, performant loading animations for video players
 */

/* Base loading container - Added !important to ensure styles are applied */
.player-loading-container {
  width: 100% !important;
  height: 0 !important;
  padding-bottom: 56.25% !important; /* 16:9 aspect ratio */
  position: relative !important;
  background-color: var(--bg-card, #1f2937) !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  transform: translateZ(0) !important; /* Force GPU acceleration */
  will-change: opacity !important; /* Hint for browser optimization */
  display: block !important; /* Ensure it's displayed as a block */
  margin: 0 auto !important; /* Center it */
  max-width: 100% !important;
}

/* Loading overlay */
.player-loading-overlay {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: rgba(17, 24, 39, 0.7) !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
  z-index: 10 !important;
}

/* Poster image */
.player-loading-poster {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  opacity: 0.7 !important;
  transform: scale(1.05) !important;
  filter: blur(2px) !important;
  transition: opacity 0.3s ease !important;
}

/* Spinner animation */
.player-loading-spinner {
  width: 48px !important;
  height: 48px !important;
  border-radius: 50% !important;
  position: relative !important;
  margin-bottom: 16px !important;
}

.player-loading-spinner::before,
.player-loading-spinner::after {
  content: "" !important;
  position: absolute !important;
  border-radius: 50% !important;
}

.player-loading-spinner::before {
  width: 100% !important;
  height: 100% !important;
  background-image: linear-gradient(0deg, var(--accent, #f97316) 33%, var(--bg-card, #1f2937) 100%) !important;
  animation: player-spinner-rotate 1s infinite linear !important;
}

.player-loading-spinner::after {
  width: 85% !important;
  height: 85% !important;
  background-color: var(--bg-card, #1f2937) !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

@keyframes player-spinner-rotate {
  to {
    transform: rotate(360deg);
  }
}

/* Loading text */
.player-loading-text {
  font-size: 16px !important;
  font-weight: 500 !important;
  color: var(--text-primary, #f9fafb) !important;
  margin-bottom: 8px !important;
  text-align: center !important;
  font-family: system-ui, -apple-system, sans-serif !important;
  line-height: 1.4 !important;
  display: block !important;
  width: auto !important;
  max-width: 80% !important;
  white-space: normal !important;
  overflow: visible !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* Loading progress */
.player-loading-progress {
  width: 80% !important;
  max-width: 240px !important;
  height: 4px !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-radius: 2px !important;
  overflow: hidden !important;
  margin-bottom: 16px !important;
}

.player-loading-progress-bar {
  height: 100% !important;
  width: 30% !important;
  background-color: var(--accent, #f97316) !important;
  border-radius: 2px !important;
  position: relative !important;
  animation: player-progress-indeterminate 1.5s infinite ease-in-out !important;
  transform: translateZ(0) !important; /* Force GPU acceleration */
}

@keyframes player-progress-indeterminate {
  0% {
    left: -30%;
  }
  100% {
    left: 100%;
  }
}

/* Loading message */
.player-loading-message {
  font-size: 14px !important;
  color: var(--text-secondary, #e5e7eb) !important;
  text-align: center !important;
  max-width: 80% !important;
  line-height: 1.4 !important;
}

/* Error state */
.player-loading-error {
  color: #ef4444 !important;
  font-size: 14px !important;
  margin-top: 8px !important;
  text-align: center !important;
  max-width: 80% !important;
}

/* Retry button */
.player-loading-retry {
  margin-top: 16px !important;
  padding: 8px 16px !important;
  background-color: var(--accent, #f97316) !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: background-color 0.2s ease !important;
}

.player-loading-retry:hover {
  background-color: var(--accent-dark, #ea580c) !important;
}

.player-loading-retry:focus {
  outline: 2px solid var(--accent-light, #fb923c) !important;
  outline-offset: 2px !important;
}

/* Shimmer effect for skeleton elements */
.player-loading-shimmer {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.08),
    transparent
  ) !important;
  animation: player-shimmer 2s infinite !important;
  transform: translateX(-100%) !important;
}

@keyframes player-shimmer {
  100% {
    transform: translateX(100%);
  }
}

/* Controls skeleton */
.player-loading-controls {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 48px !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  display: flex !important;
  align-items: center !important;
  padding: 0 16px !important;
}

.player-loading-control {
  width: 32px !important;
  height: 32px !important;
  border-radius: 50% !important;
  background-color: rgba(255, 255, 255, 0.2) !important;
  margin-right: 12px !important;
  position: relative !important;
  overflow: hidden !important;
}

.player-loading-progress-bar-skeleton {
  flex: 1 !important;
  height: 4px !important;
  background-color: rgba(255, 255, 255, 0.2) !important;
  border-radius: 2px !important;
  margin: 0 12px !important;
  position: relative !important;
  overflow: hidden !important;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .player-loading-spinner {
    width: 40px !important;
    height: 40px !important;
    margin-bottom: 12px !important;
  }
  
  .player-loading-text {
    font-size: 14px !important;
    margin-bottom: 6px !important;
  }
  
  .player-loading-progress {
    width: 70% !important;
    max-width: 200px !important;
    margin-bottom: 12px !important;
  }
  
  .player-loading-message {
    font-size: 12px !important;
    max-width: 90% !important;
  }
  
  .player-loading-controls {
    height: 40px !important;
  }
}

/* Lower quality for low-end devices */
.low-end-device .player-loading-overlay {
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
}

.low-end-device .player-loading-spinner::before {
  animation-duration: 1.5s !important;
}

.low-end-device .player-loading-progress-bar {
  animation-duration: 2s !important;
}

.low-end-device .player-loading-shimmer {
  display: none !important;
}

/* Reduce motion if user prefers */
@media (prefers-reduced-motion: reduce) {
  .player-loading-spinner::before {
    animation: none !important;
    transform: rotate(45deg) !important;
  }
  
  .player-loading-progress-bar {
    animation: none !important;
    width: 50% !important;
    margin: 0 auto !important;
    left: 0 !important;
  }
  
  .player-loading-shimmer {
    display: none !important;
  }
  
  .player-loading-poster {
    transform: none !important;
    filter: none !important;
  }
}

/* Focus styles for accessibility */
.player-loading-container:focus-visible {
  outline: 2px solid var(--accent, #f97316) !important;
  outline-offset: 2px !important;
}

/* Screen reader only class */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border-width: 0 !important;
}

/* Fix for player placeholder specifically */
.player-placeholder {
  width: 100% !important;
  height: auto !important;
  aspect-ratio: 16/9 !important;
  display: block !important;
}

/* Avoid loading UI being hidden */
#player-wrapper .player-placeholder,
#player-wrapper .player-loading-container {
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Fix for player wrapper */
#player-wrapper {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 auto !important;
  position: relative !important;
}

/* Animation fix */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

#player-wrapper .player-loading-container {
  animation: fadeIn 0.3s ease-in-out !important;
}

/* Make sure loading text is visible in all environments */
#player-wrapper .player-loading-text,
.player-placeholder .player-loading-text {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  color: #f9fafb !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  margin-bottom: 8px !important;
  text-align: center !important;
  font-family: system-ui, -apple-system, sans-serif !important;
  line-height: 1.4 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  z-index: 100 !important;
}

/* Target the loading text specifically */
.player-loading-overlay div:nth-child(2) {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  color: #f9fafb !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  margin-bottom: 8px !important;
  text-align: center !important;
  font-family: system-ui, -apple-system, sans-serif !important;
  line-height: 1.4 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  z-index: 100 !important;
}
