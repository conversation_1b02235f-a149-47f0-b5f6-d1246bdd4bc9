/**
 * Unified Utility Library
 * This file contains common utility functions used across the application
 */

// Device and Environment Detection
const Utils = {
  // Logging utility - only logs in development environments
  logger: (function() {
    // Check if we're in a development environment
    const isDev = window.location.hostname === 'localhost' || 
                  window.location.hostname === '127.0.0.1' ||
                  window.location.hostname.includes('.local');
    
    // Return logging utility functions
    return {
      log: isDev ? console.log.bind(console) : function() {},
      error: isDev ? console.error.bind(console) : function() {},
      warn: isDev ? console.warn.bind(console) : function() {},
      info: isDev ? console.info.bind(console) : function() {}
    };
  })(),

  /**
   * Check if the device is a mobile device
   * @returns {boolean} True if the device is mobile
   */
  isMobileDevice: function() {
    return window.innerWidth <= 768 ||
           /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  },

  /**
   * Check if the device is a low-end device
   * @returns {boolean} True if the device is low-end
   */
  isLowEndDevice: function() {
    // Check for hardware concurrency (CPU cores)
    if (navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2) {
      return true;
    }

    // Check for older Android devices
    if (/Android [4-6]/.test(navigator.userAgent)) {
      return true;
    }

    // Check for mobile Firefox (often has performance issues)
    if (/Mobile.*Firefox/.test(navigator.userAgent)) {
      return true;
    }

    // Check for memory constraints
    if (navigator.deviceMemory && navigator.deviceMemory <= 2) {
      return true;
    }

    return false;
  },

  /**
   * Check if reduced motion is preferred
   * @returns {boolean} True if reduced motion is preferred
   */
  prefersReducedMotion: function() {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },

  /**
   * Check if dark mode is preferred
   * @returns {boolean} True if dark mode is preferred
   */
  prefersDarkMode: function() {
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  },

  // DOM Manipulation Utilities

  /**
   * Create an element with attributes and children
   * @param {string} tag - The tag name
   * @param {Object} attrs - The attributes
   * @param {Array|string} children - The children
   * @returns {HTMLElement} The created element
   */
  createElement: function(tag, attrs = {}, children = []) {
    const element = document.createElement(tag);

    // Add attributes
    Object.entries(attrs).forEach(([key, value]) => {
      if (key === 'className') {
        element.className = value;
      } else if (key === 'style' && typeof value === 'object') {
        Object.entries(value).forEach(([prop, val]) => {
          element.style[prop] = val;
        });
      } else if (key.startsWith('on') && typeof value === 'function') {
        element.addEventListener(key.substring(2).toLowerCase(), value);
      } else {
        element.setAttribute(key, value);
      }
    });

    // Add children
    if (typeof children === 'string') {
      element.textContent = children;
    } else if (Array.isArray(children)) {
      children.forEach(child => {
        if (typeof child === 'string') {
          element.appendChild(document.createTextNode(child));
        } else if (child instanceof Node) {
          element.appendChild(child);
        }
      });
    }

    return element;
  },

  /**
   * Create HTML from a template string (safer than innerHTML)
   * @param {string} html - The HTML string
   * @returns {DocumentFragment} The document fragment
   */
  createHTML: function(html) {
    const template = document.createElement('template');
    template.innerHTML = html.trim();
    return template.content.cloneNode(true);
  },

  /**
   * Empty an element
   * @param {HTMLElement} element - The element to empty
   */
  emptyElement: function(element) {
    while (element.firstChild) {
      element.removeChild(element.firstChild);
    }
  },

  // Event Handling Utilities

  /**
   * Debounce a function
   * @param {Function} func - The function to debounce
   * @param {number} wait - The debounce wait time in milliseconds
   * @returns {Function} The debounced function
   */
  debounce: function(func, wait) {
    let timeout;
    return function() {
      const context = this;
      const args = arguments;
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        func.apply(context, args);
      }, wait);
    };
  },

  /**
   * Throttle a function
   * @param {Function} func - The function to throttle
   * @param {number} limit - The throttle limit in milliseconds
   * @returns {Function} The throttled function
   */
  throttle: function(func, limit) {
    let inThrottle;
    return function() {
      const context = this;
      const args = arguments;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => {
          inThrottle = false;
        }, limit);
      }
    };
  },

  /**
   * Add event listener with automatic cleanup
   * @param {HTMLElement} element - The element to add the listener to
   * @param {string} type - The event type
   * @param {Function} listener - The event listener
   * @param {Object} options - The event listener options
   * @returns {Function} A function to remove the listener
   */
  addEventListenerWithCleanup: function(element, type, listener, options = {}) {
    element.addEventListener(type, listener, options);
    return function() {
      element.removeEventListener(type, listener, options);
    };
  },

  // UI Component Utilities

  /**
   * Show a dropdown
   * @param {HTMLElement} dropdown - The dropdown element
   * @param {HTMLElement} button - The button that triggered the dropdown
   * @param {Object} options - The options
   */
  showDropdown: function(dropdown, button, options = {}) {
    const isMobile = this.isMobileDevice();
    const defaults = {
      mobileFullscreen: true,
      closeOnClickOutside: true,
      closeOnEscape: true,
      closeOnScroll: !isMobile,
      showBackdrop: isMobile,
      onShow: null,
      onHide: null,
      zIndex: isMobile ? 1000 : 100
    };

    const settings = { ...defaults, ...options };

    // Position the dropdown
    if (!isMobile && button) {
      const buttonRect = button.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

      // Use fixed positioning to follow the viewport
      dropdown.style.position = 'fixed';
      dropdown.style.top = (buttonRect.bottom + 10) + 'px';
      dropdown.style.right = (window.innerWidth - buttonRect.right) + 'px';
      dropdown.style.left = '';
      dropdown.style.margin = '';

      // Make sure the dropdown is fully visible in the viewport
      setTimeout(() => {
        const dropdownRect = dropdown.getBoundingClientRect();
        if (dropdownRect.bottom > window.innerHeight) {
          // If dropdown extends beyond bottom of viewport, position it above the button
          dropdown.style.top = 'auto';
          dropdown.style.bottom = (window.innerHeight - buttonRect.top + 10) + 'px';
        }
      }, 0);
    } else if (isMobile && settings.mobileFullscreen) {
      dropdown.style.position = 'fixed';
      dropdown.style.top = '0';
      dropdown.style.right = '0';
      dropdown.style.bottom = '0';
      dropdown.style.left = '0';
      dropdown.style.width = '100%';
      dropdown.style.height = '100%';
      dropdown.style.margin = '0';
      dropdown.style.maxHeight = '100%';
      dropdown.style.borderRadius = '0';

      // Disable scrolling on mobile
      document.body.style.overflow = 'hidden';
    }

    // Show the dropdown
    dropdown.style.visibility = 'visible';
    dropdown.style.zIndex = settings.zIndex;
    dropdown.style.pointerEvents = 'auto';
    dropdown.classList.add('show');

    // Show backdrop if needed
    if (settings.showBackdrop) {
      const backdropId = dropdown.id + '-backdrop';
      let backdrop = document.getElementById(backdropId);

      if (!backdrop) {
        backdrop = document.createElement('div');
        backdrop.id = backdropId;
        backdrop.className = 'dropdown-backdrop';
        document.body.appendChild(backdrop);
      }

      backdrop.classList.add('show');
    }

    // Call onShow callback
    if (typeof settings.onShow === 'function') {
      settings.onShow(dropdown);
    }

    // Close when clicking outside
    if (settings.closeOnClickOutside && !isMobile) {
      const clickOutsideHandler = (e) => {
        if (!dropdown.contains(e.target) && !button.contains(e.target)) {
          this.hideDropdown(dropdown, settings);
          document.removeEventListener('click', clickOutsideHandler);
        }
      };

      // Add the handler after a short delay
      setTimeout(() => {
        document.addEventListener('click', clickOutsideHandler);
      }, 10);
    }

    // Close on escape key
    if (settings.closeOnEscape) {
      const escapeHandler = (e) => {
        if (e.key === 'Escape') {
          this.hideDropdown(dropdown, settings);
          document.removeEventListener('keydown', escapeHandler);
        }
      };

      document.addEventListener('keydown', escapeHandler);
    }

    // Close on scroll
    if (settings.closeOnScroll) {
      const scrollHandler = () => {
        this.hideDropdown(dropdown, settings);
        window.removeEventListener('scroll', scrollHandler);
      };

      window.addEventListener('scroll', scrollHandler, { passive: true });
    }
  },

  /**
   * Hide a dropdown
   * @param {HTMLElement} dropdown - The dropdown element
   * @param {Object} options - The options
   */
  hideDropdown: function(dropdown, options = {}) {
    const defaults = {
      onHide: null
    };

    const settings = { ...defaults, ...options };

    // Hide the dropdown
    dropdown.classList.remove('show');

    // Hide backdrop if it exists
    const backdropId = dropdown.id + '-backdrop';
    const backdrop = document.getElementById(backdropId);
    if (backdrop) {
      backdrop.classList.remove('show');
    }

    // Re-enable scrolling
    document.body.style.overflow = '';

    // Make sure the dropdown is not capturing any events
    dropdown.style.visibility = 'hidden';
    dropdown.style.zIndex = '-1';

    // Call onHide callback
    if (typeof settings.onHide === 'function') {
      settings.onHide(dropdown);
    }

    // After a short delay, reset the visibility to the default state
    setTimeout(() => {
      dropdown.style.visibility = '';
      dropdown.style.zIndex = '';
    }, 100);
  },

  /**
   * Toggle a dropdown
   * @param {HTMLElement} dropdown - The dropdown element
   * @param {HTMLElement} button - The button that triggered the dropdown
   * @param {Object} options - The options
   */
  toggleDropdown: function(dropdown, button, options = {}) {
    if (dropdown.classList.contains('show')) {
      this.hideDropdown(dropdown, options);
    } else {
      this.showDropdown(dropdown, button, options);
    }
  },

  // Storage Utilities

  /**
   * Get an item from localStorage with error handling
   * @param {string} key - The key
   * @param {*} defaultValue - The default value
   * @returns {*} The value
   */
  getStorageItem: function(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (e) {
      // Error getting item from localStorage
      return defaultValue;
    }
  },

  /**
   * Set an item in localStorage with error handling
   * @param {string} key - The key
   * @param {*} value - The value
   * @returns {boolean} True if successful
   */
  setStorageItem: function(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (e) {
      // Error setting item in localStorage
      return false;
    }
  },

  /**
   * Remove an item from localStorage with error handling
   * @param {string} key - The key
   * @returns {boolean} True if successful
   */
  removeStorageItem: function(key) {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (e) {
      // Error removing item from localStorage
      return false;
    }
  },

  // Image Utilities

  /**
   * Preload an image
   * @param {string} src - The image source
   * @returns {Promise} A promise that resolves when the image is loaded
   */
  preloadImage: function(src) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;
      img.src = src;
    });
  },

  /**
   * Get image dimensions
   * @param {string} src - The image source
   * @returns {Promise} A promise that resolves with the image dimensions
   */
  getImageDimensions: function(src) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve({
        width: img.width,
        height: img.height,
        aspectRatio: img.width / img.height
      });
      img.onerror = reject;
      img.src = src;
    });
  },

  /**
   * Auto sync data on app start
   * This function checks if the user is logged in and triggers a data sync for all data types
   * It's meant to be called on application startup
   */
  autoSyncOnAppStart: function() {
    // Check if user is logged in
    if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
      this.logger.log('User is logged in, triggering auto-sync on app start');
      
      // Get the user ID
      const userId = window.getCurrentUserId();
      if (!userId) {
        this.logger.log('Unable to get user ID for auto-sync');
        return;
      }
      
      // Check if sync manager is available
      if (!window.syncManager) {
        this.logger.log('Sync manager not available for auto-sync, will try again in 2 seconds');
        // If sync manager isn't available yet, try again in 2 seconds
        setTimeout(() => {
          if (window.syncManager) {
            this.logger.log('Sync manager now available, proceeding with auto-sync');
            Utils.performAutoSync(userId);
          } else {
            this.logger.log('Sync manager still not available, loading it now');
            // Try to load the sync manager
            if (typeof window.loadFirebaseSyncManager === 'function') {
              window.loadFirebaseSyncManager();
              // Try one more time after sync manager is loaded
              setTimeout(() => {
                if (window.syncManager) {
                  Utils.performAutoSync(userId);
                } else {
                  this.logger.log('Unable to load sync manager for auto-sync');
                }
              }, 1000);
            }
          }
        }, 2000);
      } else {
        // Sync manager is available, proceed with sync
        Utils.performAutoSync(userId);
      }
    } else {
      this.logger.log('User is not logged in, skipping auto-sync');
    }
  },

  /**
   * Perform auto sync operations
   * @param {string} userId - The user ID
   */
  performAutoSync: function(userId) {
    if (!window.syncManager) return;
    
    this.logger.log('Performing auto-sync for user:', userId);
    
    // Show sync indicator
    Utils.showSyncIndicator();
    
    // IMPORTANT: Ensure all data has proper timestamps before syncing
    // This prevents issues with conflict resolution during sync
    if (typeof Utils.ensureDataTimestamps === 'function') {
      Utils.ensureDataTimestamps();
    }
    
    // First, refresh data from Firestore to ensure we have the latest data
    if (typeof window.syncManager.forceRefreshFromFirestore === 'function') {
      this.logger.log('Refreshing data from Firestore...');
      window.syncManager.forceRefreshFromFirestore(userId)
        .then(() => {
          this.logger.log('Data refreshed from Firestore successfully');
          
          // After refreshing, sync all data types to ensure consistency
          if (typeof window.syncManager.syncAll === 'function') {
            this.logger.log('Syncing all data types...');
            window.syncManager.syncAll(userId)
              .then(() => {
                this.logger.log('All data synced successfully');
                // Hide sync indicator
                Utils.hideSyncIndicator();
                // Fire event to notify that auto-sync is complete
                window.dispatchEvent(new CustomEvent('autoSyncComplete'));
              })
              .catch(error => {
                this.logger.error('Error syncing all data:', error);
                // Hide sync indicator on error
                Utils.hideSyncIndicator();
              });
          } else {
            // Hide sync indicator if syncAll is not available
            Utils.hideSyncIndicator();
          }
        })
        .catch(error => {
          this.logger.error('Error refreshing data from Firestore:', error);
          
          // Hide sync indicator on error
          Utils.hideSyncIndicator();
          
          // Even if refresh fails, try syncing anyway
          if (typeof window.syncManager.syncAll === 'function') {
            Utils.showSyncIndicator('正在尝试同步数据...');
            window.syncManager.syncAll(userId)
              .then(() => {
                this.logger.log('All data synced successfully despite refresh failure');
                // Hide sync indicator
                Utils.hideSyncIndicator();
                // Fire event to notify that auto-sync is complete
                window.dispatchEvent(new CustomEvent('autoSyncComplete'));
              })
              .catch(error => {
                this.logger.error('Error syncing all data:', error);
                // Hide sync indicator on error
                Utils.hideSyncIndicator();
              });
          }
        });
    } else if (typeof window.syncManager.syncAll === 'function') {
      // Fallback to syncAll if forceRefreshFromFirestore is not available
      this.logger.log('Syncing all data types...');
      window.syncManager.syncAll(userId)
        .then(() => {
          this.logger.log('All data synced successfully');
          // Hide sync indicator
          Utils.hideSyncIndicator();
          // Fire event to notify that auto-sync is complete
          window.dispatchEvent(new CustomEvent('autoSyncComplete'));
        })
        .catch(error => {
          this.logger.error('Error syncing all data:', error);
          // Hide sync indicator on error
          Utils.hideSyncIndicator();
        });
    } else {
      // Hide sync indicator if no sync methods are available
      Utils.hideSyncIndicator();
    }
  },

  /**
   * Setup auto-sync on app resume or visibility change
   * This function sets up event listeners to sync data when the app resumes from background or
   * when a mobile device wakes up from sleep
   */
  setupAppResumeSync: function() {
    // Get the current time for later comparison
    let lastVisibilityTime = Date.now();
    
    // Flag to track if we've been hidden for a significant amount of time
    let significantTimeHidden = false;
    
    // Handle page visibility change (app going to background or resuming)
    document.addEventListener('visibilitychange', () => {
      const isHidden = document.hidden;
      const now = Date.now();
      
      if (isHidden) {
        // App is going to background, record the time
        lastVisibilityTime = now;
        significantTimeHidden = false;
      } else {
        // App is resuming from background
        const timeHidden = now - lastVisibilityTime;
        
        // Only trigger sync if app was hidden for more than 5 minutes
        // This prevents unnecessary syncs for brief visibility changes
        if (timeHidden > 5 * 60 * 1000) { // 5 minutes in milliseconds
          significantTimeHidden = true;
          this.logger.log(`App was hidden for ${Math.round(timeHidden/1000)} seconds, triggering sync`);
          
          // Check if user is logged in before syncing
          if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
            const userId = window.getCurrentUserId();
            if (userId && window.syncManager) {
              this.logger.log('Syncing data on app resume');
              // Ensure all data has proper timestamps before syncing
              if (typeof Utils.ensureDataTimestamps === 'function') {
                Utils.ensureDataTimestamps();
              }
              // Use the forceRefreshFromFirestore method to get the latest data from Firestore
              if (typeof window.syncManager.forceRefreshFromFirestore === 'function') {
                // Show sync indicator
                Utils.showSyncIndicator('正在恢复同步...');
                window.syncManager.forceRefreshFromFirestore(userId)
                  .then(() => {
                    this.logger.log('Data refreshed on app resume successfully');
                    // Hide sync indicator
                    Utils.hideSyncIndicator();
                  })
                  .catch(error => {
                    this.logger.error('Error refreshing data on app resume:', error);
                    // Hide sync indicator on error
                    Utils.hideSyncIndicator();
                  });
              }
            }
          }
        } else {
          this.logger.log(`App was hidden for only ${Math.round(timeHidden/1000)} seconds, skipping sync`);
        }
      }
    });
    
    // Also listen for online/offline events to sync when connection is restored
    window.addEventListener('online', () => {
      this.logger.log('Connection restored, checking if sync is needed');
      
      // Only sync if we've been offline for a significant amount of time
      if (significantTimeHidden) {
        significantTimeHidden = false;
        
        // Check if user is logged in before syncing
        if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
          const userId = window.getCurrentUserId();
          if (userId && window.syncManager) {
            this.logger.log('Syncing data on connection restore');
            // Ensure all data has proper timestamps before syncing
            if (typeof Utils.ensureDataTimestamps === 'function') {
              Utils.ensureDataTimestamps();
            }
            // Use the forceRefreshFromFirestore method to get the latest data from Firestore
            if (typeof window.syncManager.forceRefreshFromFirestore === 'function') {
              // Show sync indicator
              Utils.showSyncIndicator('正在恢复连接同步...');
              window.syncManager.forceRefreshFromFirestore(userId)
                .then(() => {
                  this.logger.log('Data refreshed on connection restore successfully');
                  // Hide sync indicator
                  Utils.hideSyncIndicator();
                })
                .catch(error => {
                  this.logger.error('Error refreshing data on connection restore:', error);
                  // Hide sync indicator on error
                  Utils.hideSyncIndicator();
                });
            }
          }
        }
      }
    });
  },

  /**
   * Show a small sync indicator in the corner of the screen
   * @param {string} message - Optional message to display
   */
  showSyncIndicator: function(message = '正在同步数据...') {
    // Check if the indicator already exists
    let indicator = document.getElementById('auto-sync-indicator');
    
    if (!indicator) {
      // Create the indicator
      indicator = document.createElement('div');
      indicator.id = 'auto-sync-indicator';
      
      // Style the indicator
      Object.assign(indicator.style, {
        position: 'fixed',
        bottom: '10px',
        right: '10px',
        backgroundColor: 'rgba(33, 150, 243, 0.85)',
        color: 'white',
        padding: '6px 12px',
        borderRadius: '4px',
        fontSize: '12px',
        zIndex: '9999',
        display: 'flex',
        alignItems: 'center',
        boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
        transition: 'opacity 0.3s ease',
        opacity: '0'
      });
      
      // Add a loading spinner
      const spinner = document.createElement('div');
      spinner.className = 'sync-spinner';
      Object.assign(spinner.style, {
        width: '12px',
        height: '12px',
        borderRadius: '50%',
        border: '2px solid rgba(255,255,255,0.3)',
        borderTopColor: 'white',
        marginRight: '8px',
        animation: 'auto-sync-spin 1s linear infinite'
      });
      
      // Add the animation style
      const style = document.createElement('style');
      style.textContent = `
        @keyframes auto-sync-spin {
          to { transform: rotate(360deg); }
        }
      `;
      document.head.appendChild(style);
      
      // Add the message
      const messageElement = document.createElement('span');
      messageElement.textContent = message;
      
      // Assemble the indicator
      indicator.appendChild(spinner);
      indicator.appendChild(messageElement);
      
      // Add to the DOM
      document.body.appendChild(indicator);
      
      // Fade in the indicator
      setTimeout(() => {
        indicator.style.opacity = '1';
      }, 10);
      
      // Set a timeout to hide the indicator after 10 seconds
      // in case something goes wrong and it doesn't get hidden
      setTimeout(() => {
        Utils.hideSyncIndicator();
      }, 10000);
    }
    
    return indicator;
  },

  /**
   * Hide the sync indicator
   */
  hideSyncIndicator: function() {
    const indicator = document.getElementById('auto-sync-indicator');
    
    if (indicator) {
      // Fade out
      indicator.style.opacity = '0';
      
      // Remove after animation completes
      setTimeout(() => {
        if (indicator.parentNode) {
          indicator.parentNode.removeChild(indicator);
        }
      }, 300);
    }
  },

  /**
   * Ensure all user data has proper timestamps for conflict resolution
   * This function ensures that all data in localStorage has the necessary
   * timestamp fields for proper merging during sync operations
   */
  ensureDataTimestamps: function() {
    this.logger.log('Ensuring all data has timestamps for conflict resolution');
    
    // Process favorites
    try {
      const favorites = JSON.parse(localStorage.getItem('vodg-favorites') || '[]');
      let favoritesUpdated = false;
      
      favorites.forEach(item => {
        if (item) {
          // Ensure lastWatched timestamp exists
          if (!item.lastWatched) {
            item.lastWatched = Date.now();
            favoritesUpdated = true;
          }
          
          // Ensure timestamp exists (as backup field)
          if (!item.timestamp) {
            item.timestamp = item.lastWatched || Date.now();
            favoritesUpdated = true;
          }
        }
      });
      
      if (favoritesUpdated) {
        localStorage.setItem('vodg-favorites', JSON.stringify(favorites));
        this.logger.log('Updated timestamps for favorites items');
      }
    } catch (e) {
      this.logger.error('Error ensuring favorites timestamps:', e);
    }
    
    // Process history (viewing and search)
    try {
      const history = JSON.parse(localStorage.getItem('vodg-vod-history') || '[]');
      let historyUpdated = false;
      
      history.forEach(item => {
        if (item) {
          // Ensure timestamp exists
          if (!item.timestamp) {
            item.timestamp = Date.now();
            historyUpdated = true;
          }
          
          // Ensure lastWatched timestamp exists for viewing history
          if (!item.id.startsWith('search-') && !item.lastWatched) {
            item.lastWatched = item.timestamp || Date.now();
            historyUpdated = true;
          }
        }
      });
      
      if (historyUpdated) {
        localStorage.setItem('vodg-vod-history', JSON.stringify(history));
        this.logger.log('Updated timestamps for history items');
      }
    } catch (e) {
      this.logger.error('Error ensuring history timestamps:', e);
    }
    
    // Process watch progress
    try {
      const watchProgress = JSON.parse(localStorage.getItem('vodg-play') || '{}');
      let watchProgressUpdated = false;
      
      Object.keys(watchProgress).forEach(videoId => {
        const videoData = watchProgress[videoId];
        
        if (typeof videoData === 'object') {
          Object.keys(videoData).forEach(episodeId => {
            const episodeData = videoData[episodeId];
            
            if (episodeData && typeof episodeData === 'object') {
              // Ensure timestamp exists
              if (!episodeData.timestamp) {
                episodeData.timestamp = episodeData.updated || Date.now();
                watchProgressUpdated = true;
              }
              
              // Ensure updated field exists (as backup)
              if (!episodeData.updated) {
                episodeData.updated = episodeData.timestamp || Date.now();
                watchProgressUpdated = true;
              }
            }
          });
        }
      });
      
      if (watchProgressUpdated) {
        localStorage.setItem('vodg-play', JSON.stringify(watchProgress));
        this.logger.log('Updated timestamps for watch progress items');
      }
    } catch (e) {
      this.logger.error('Error ensuring watch progress timestamps:', e);
    }
    
    return true;
  }
};

// Export the Utils object
window.Utils = Utils;
