// Adds skeletons to the VOD list while images are loading, then removes them when all images are loaded.
(function() {
  function createSkeletonItem() {
    const item = document.createElement('div');
    item.className = 'vod-item skeleton';
    item.innerHTML = `
      <div class="vod-poster skeleton-box"></div>
      <div class="vod-title skeleton-bar"></div>
    `;
    return item;
  }

  function showSkeletons() {
    const vodList = document.querySelector('.vod-list');
    if (!vodList) return;
    // Only add skeletons if not already present
    if (vodList.querySelector('.skeleton')) return;
    for (let i = 0; i < 8; ++i) {
      vodList.appendChild(createSkeletonItem());
    }
  }

  function removeSkeletons() {
    document.querySelectorAll('.vod-item.skeleton').forEach(el => el.remove());
  }

  // Wait for DOM
  document.addEventListener('DOMContentLoaded', function() {
    showSkeletons();
    // Wait for all images in .vod-list to load
    const vodList = document.querySelector('.vod-list');
    if (!vodList) return;
    const images = vodList.querySelectorAll('img');
    if (images.length === 0) {
      removeSkeletons();
      return;
    }
    let loaded = 0;
    images.forEach(img => {
      if (img.complete) {
        loaded++;
      } else {
        img.addEventListener('load', () => {
          loaded++;
          if (loaded === images.length) removeSkeletons();
        });
        img.addEventListener('error', () => {
          loaded++;
          if (loaded === images.length) removeSkeletons();
        });
      }
    });
    // In case all are already loaded
    if (loaded === images.length) removeSkeletons();
  });
})();
