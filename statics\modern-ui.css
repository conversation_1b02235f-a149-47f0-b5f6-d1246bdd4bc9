/* Modern UI Design System for Site Revamp */

/* Import CSS variables */
@import url('/statics/css-variables.css');

/* Base Styles */
html, body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-dark);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
  max-width: 100%;
  overflow-x: hidden;
}

/* Focus styles for accessibility */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: var(--spacing-4);
  font-weight: 600;
  line-height: 1.2;
}

h1 {
  font-size: var(--font-size-4xl);
}

h2 {
  font-size: var(--font-size-3xl);
}

h3 {
  font-size: var(--font-size-2xl);
}

h4 {
  font-size: var(--font-size-xl);
}

h5 {
  font-size: var(--font-size-lg);
}

h6 {
  font-size: var(--font-size-base);
}

p {
  margin-top: 0;
  margin-bottom: var(--spacing-4);
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: color var(--transition) var(--transition-ease);
}

a:hover {
  color: var(--primary-light);
}

/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius);
  font-size: var(--font-size-sm);
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition) var(--transition-ease);
  border: none;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--secondary-dark);
}

.btn-accent {
  background-color: var(--accent);
  color: white;
}

.btn-accent:hover {
  background-color: var(--accent-dark);
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--border);
  color: var(--text-primary);
}

.btn-outline:hover {
  background-color: var(--bg-card-hover);
}

/* Forms */
.form-group {
  margin-bottom: var(--spacing-4);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-secondary);
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  transition: border-color var(--transition) var(--transition-ease);
}

.form-control:focus {
  border-color: var(--primary);
  outline: none;
}

/* Cards */
.card {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: transform var(--transition) var(--transition-ease),
              box-shadow var(--transition) var(--transition-ease);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border);
}

.card-body {
  padding: var(--spacing-4);
}

.card-footer {
  padding: var(--spacing-4);
  border-top: 1px solid var(--border);
}

/* Utilities */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.d-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.align-items-center {
  align-items: center;
}

.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-3 { margin-top: var(--spacing-3); }
.mt-4 { margin-top: var(--spacing-4); }

.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }

.ml-1 { margin-left: var(--spacing-1); }
.ml-2 { margin-left: var(--spacing-2); }
.ml-3 { margin-left: var(--spacing-3); }
.ml-4 { margin-left: var(--spacing-4); }

.mr-1 { margin-right: var(--spacing-1); }
.mr-2 { margin-right: var(--spacing-2); }
.mr-3 { margin-right: var(--spacing-3); }
.mr-4 { margin-right: var(--spacing-4); }

.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }

/* Responsive utilities */
@media (max-width: 768px) {
  h1 { font-size: var(--font-size-3xl); }
  h2 { font-size: var(--font-size-2xl); }
  h3 { font-size: var(--font-size-xl); }
  
  .d-none-mobile { display: none !important; }
}

@media (min-width: 769px) {
  .d-none-desktop { display: none !important; }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Layout */
@media (max-width: 768px) {
  .container {
    padding-right: var(--spacing-4) !important; /* Increased padding */
  }
}

@media (max-width: 480px) {
  .container {
    padding-right: var(--spacing-3) !important; /* Increased padding */
  }
}

/* Header */
.site-header {
  background-color: var(--bg-card);
  padding: var(--spacing-4) 0;
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 10; /* Reduced z-index to prevent overlapping with video controls */
  transition: transform 0.3s ease, padding 0.3s ease, box-shadow 0.3s ease;
}

.site-header.header-collapsed {
  padding: var(--spacing-2) 0;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-4);
  transition: gap 0.3s ease;
}

.site-header.header-collapsed .header-container {
  gap: var(--spacing-2);
}

.site-logo {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-primary);
  transition: font-size 0.3s ease, transform 0.3s ease;
}

.site-header.header-collapsed .site-logo {
  transform: scale(0.9);
  transform-origin: left center;
}

.site-logo a {
  color: var(--text-primary);
  text-decoration: none;
}

/* Navigation */
.main-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
}

.nav-link {
  color: var(--text-secondary);
  font-weight: 500;
  transition: color var(--transition) var(--transition-ease);
}

.nav-link:hover {
  color: var(--primary-light);
}

.nav-link.active {
  color: var(--primary);
  font-weight: 600;
}

/* Search Form */
.search-form {
  display: flex;
  gap: var(--spacing-2);
  width: 100%;
  max-width: 500px;
}

/* Mobile Search Styles */
/* Mobile Search Button */
.mobile-search-button {
  display: none;
  position: fixed;
  bottom: 20px;
  left: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary);
  color: white;
  border: none;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  z-index: 40;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mobile-search-button:hover,
.mobile-search-button:focus {
  background-color: var(--primary-dark);
  transform: scale(1.05);
}

.mobile-search-button:active {
  transform: scale(0.95);
}

/* Mobile Search Overlay */
.mobile-search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-search-overlay.active {
  opacity: 1;
  visibility: visible;
}

.mobile-search-container {
  width: 90%;
  max-width: 500px;
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.mobile-search-overlay.active .mobile-search-container {
  transform: translateY(0);
}

.mobile-search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border);
}

.mobile-search-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.mobile-search-close {
  background: none;
  border: none;
  color: var(--text-tertiary);
  font-size: 1.8rem;
  cursor: pointer;
  padding: var(--spacing-1) var(--spacing-2);
  line-height: 1;
  transition: color 0.2s ease;
}

.mobile-search-close:hover {
  color: var(--text-primary);
}

.mobile-search-form {
  padding: var(--spacing-4);
}

.mobile-search-input-group {
  display: flex;
  position: relative;
  margin-bottom: var(--spacing-4);
}

.mobile-search-input {
  flex: 1;
  background-color: var(--bg-dark);
  color: var(--text-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius-full);
  padding: var(--spacing-3) var(--spacing-4);
  padding-right: 50px;
  font-size: var(--font-size-base);
  transition: border-color 0.2s ease;
  width: 100%;
}

.mobile-search-input:focus {
  outline: none;
  border-color: var(--primary);
}

.mobile-search-submit {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.mobile-search-submit:hover {
  background-color: var(--primary-dark);
}

.mobile-search-options {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-3);
}

.mobile-search-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
}

.mobile-search-option input[type="radio"] {
  margin: 0;
}

.mobile-search-option span {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

@media (max-width: 480px) {
  .mobile-search-container {
    width: 95%;
  }

  .mobile-search-header {
    padding: var(--spacing-3);
  }

  .mobile-search-form {
    padding: var(--spacing-3);
  }

  .mobile-search-input {
    padding: var(--spacing-2) var(--spacing-3);
    padding-right: 45px;
  }

  .mobile-search-submit {
    width: 36px;
    height: 36px;
  }
}

.search-select {
  background-color: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius-full);
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-base);
  transition: border-color var(--transition) var(--transition-ease);
}

.search-select:focus {
  border-color: var(--primary);
  outline: none;
}

.search-input {
  flex-grow: 1;
  background-color: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius-full);
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-base);
  transition: border-color var(--transition) var(--transition-ease);
}

.search-input:focus {
  border-color: var(--primary);
  outline: none;
}

.search-button {
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius-full);
  padding: var(--spacing-2) var(--spacing-6);
  font-weight: 600;
  cursor: pointer;
  transition: background-color var(--transition) var(--transition-ease);
}

.search-button:hover {
  background-color: var(--primary-dark);
}

/* Hero Section */
.hero {
  padding: var(--spacing-12) 0;
  text-align: center;
  background: linear-gradient(to bottom, var(--bg-dark), var(--bg-card));
}

.hero-title {
  font-size: var(--font-size-4xl);
  font-weight: 800;
  margin-bottom: var(--spacing-4);
  background: linear-gradient(to right, var(--primary-light), var(--accent-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto var(--spacing-8);
}

/* Tabs */
.tabs {
  margin-bottom: var(--spacing-8);
}

.tab-buttons-wrapper {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  scrollbar-width: none; /* Hide scrollbar in Firefox */
  position: relative;
}

.tab-buttons-wrapper::-webkit-scrollbar {
  display: none; /* Hide scrollbar in Chrome/Safari */
}

.tab-buttons {
  display: inline-flex; /* Changed from flex to inline-flex */
  gap: var(--spacing-4);
  border-bottom: 1px solid var(--border);
  padding-bottom: var(--spacing-2);
  margin-bottom: var(--spacing-6);
  white-space: nowrap; /* Prevent wrapping */
  flex-wrap: nowrap; /* Ensure no wrapping */
  min-width: min-content; /* Ensure it takes at least the width of all children */
}

.tab-buttons::-webkit-scrollbar {
  display: none;
}

.tab-button {
  background: none;
  border: none;
  color: var(--text-tertiary);
  font-size: var(--font-size-base);
  font-weight: 500;
  padding: var(--spacing-2) var(--spacing-4);
  cursor: pointer;
  position: relative;
  transition: color var(--transition) var(--transition-ease);
  flex: 0 0 auto; /* Don't shrink, don't grow, use auto width */
  white-space: nowrap; /* Prevent text wrapping */
}

.tab-button:hover {
  color: var(--text-primary);
}

.tab-button.active {
  color: var(--primary);
  font-weight: 600;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary);
  border-radius: var(--radius-full);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
  animation: fadeIn var(--transition-slow) var(--transition-ease);
}

/* Toggle Switch */
.toggle-container {
  display: flex;
  background-color: var(--bg-card);
  border-radius: var(--radius-full);
  padding: var(--spacing-1);
  position: relative;
  width: fit-content;
  margin-bottom: var(--spacing-6);
}

.toggle-button {
  padding: var(--spacing-2) var(--spacing-5);
  font-weight: 500;
  transition: color var(--transition) var(--transition-ease);
  border: none;
  outline: none;
  cursor: pointer;
  position: relative;
  z-index: 1;
  background-color: transparent;
  border-radius: var(--radius-full);
  color: var(--text-tertiary);
}

.toggle-button.active {
  color: var(--text-primary);
}

.toggle-slider {
  position: absolute;
  top: var(--spacing-1);
  left: var(--spacing-1);
  height: calc(100% - var(--spacing-2));
  width: calc(50% - var(--spacing-1));
  background-color: var(--primary);
  border-radius: var(--radius-full);
  transition: transform var(--transition-slow) var(--transition-bounce);
}

.toggle-slider.right {
  transform: translateX(100%);
}

/* Tags */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
}

.tag-button {
  background-color: var(--bg-card);
  color: var(--text-tertiary);
  border: none;
  border-radius: var(--radius-full);
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition) var(--transition-ease);
}

.tag-button:hover {
  background-color: var(--bg-card-hover);
  color: var(--text-secondary);
  transform: translateY(-2px);
}

.tag-button.active {
  background-color: var(--primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* Card Grid */
.card-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr)) !important;
  gap: var(--spacing-4) !important;
}

/* Responsive grid adjustments */
@media (min-width: 400px) {
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)) !important;
  }
}

@media (min-width: 600px) {
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr)) !important;
  }
}

@media (min-width: 800px) {
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(170px, 1fr)) !important;
  }
}

/* Movie/TV Card */
.media-card {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: transform var(--transition) var(--transition-ease),
              box-shadow var(--transition) var(--transition-ease);
}

.media-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.media-poster {
  position: relative;
  aspect-ratio: 2/3;
  overflow: hidden;
}

.media-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow) var(--transition-ease);
}

.media-card:hover .media-poster img {
  transform: scale(1.05);
}

.media-badge {
  position: absolute;
  top: var(--spacing-2);
  right: var(--spacing-2);
  background-color: var(--accent);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: 700;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius);
  z-index: 1;
}

.media-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
  padding: var(--spacing-4) var(--spacing-2) var(--spacing-2);
  color: white;
  font-size: var(--font-size-xs);
}

.media-title {
  padding: var(--spacing-3);
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Detail Page */
.detail-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 768px) {
  .detail-container {
    grid-template-columns: 250px 1fr;
  }
}

.detail-poster {
  width: 100%;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.detail-poster img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.detail-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.detail-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-2);
}

.detail-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-3);
  color: var(--text-tertiary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-4);
}

.detail-description {
  line-height: 1.7;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-6);
}

/* Episodes */
.episodes-container {
  margin-top: var(--spacing-8);
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
}

.episodes-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--border);
}

.episodes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: var(--spacing-3);
  max-height: 400px;
  overflow-y: auto;
  padding-right: var(--spacing-2);
  scrollbar-width: thin;
  scrollbar-color: var(--border) transparent;
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

.episodes-grid::-webkit-scrollbar {
  width: 6px;
}

.episodes-grid::-webkit-scrollbar-track {
  background: transparent;
}

.episodes-grid::-webkit-scrollbar-thumb {
  background-color: var(--border);
  border-radius: var(--radius-full);
}

.episodes-grid::-webkit-scrollbar-thumb:hover {
  background-color: var(--border-light);
}

.episode-button {
  background-color: var(--bg-dark);
  color: var(--text-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: var(--spacing-2);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition) var(--transition-ease);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.episode-button:hover {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.episode-button.active {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
  font-weight: 600;
}

/* Episode play link */
.vod-episode-play {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  padding: 0 4px;
  color: inherit;
  text-decoration: none;
}

/* Recommendations */
.recommendations {
  margin-top: var(--spacing-12);
}

.recommendations-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--border);
}

/* History Feature */
.history-dropdown,
.favorites-dropdown {
  position: relative;
  display: inline-flex;
  align-items: center;
  margin: 0 10px;
}

/* Desktop History Button */
.history-button,
.favorites-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  background: none;
  border: none;
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius);
  transition: all var(--transition) var(--transition-ease);
  height: 40px; /* Fixed height for consistent alignment */
  white-space: nowrap; /* Prevent text wrapping */
}

@media (max-width: 768px) {
  .history-button,
  .favorites-button {
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--font-size-sm);
    gap: var(--spacing-1);
    height: 36px; /* Slightly smaller height on mobile */
  }
}

@media (max-width: 480px) {
  .history-button,
  .favorites-button {
    padding: var(--spacing-1);
    font-size: var(--font-size-xs);
    height: 32px; /* Even smaller height on very small screens */
  }
}

.history-button:hover,
.favorites-button:hover {
  background-color: var(--bg-card-hover);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.history-button:active,
.favorites-button:active {
  transform: translateY(0);
}

.history-button-icon,
.favorites-button-icon {
  font-size: 1.2em;
  opacity: 0.8;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px; /* Fixed width for icons */
  height: 20px; /* Fixed height for icons */
}

.history-button:hover .history-button-icon,
.favorites-button:hover .favorites-button-icon {
  opacity: 1;
}

/* History Dropdown Content */
.history-content,
.favorites-content {
  position: fixed;
  top: 60px; /* Adjust based on your header height */
  right: 20px;
  width: 320px;
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border);
  z-index: -1; /* Default to below content */
  overflow: hidden;
  visibility: hidden;
  opacity: 0;
  transform: translateY(10px);
  transition: all var(--transition) var(--transition-ease);
  pointer-events: none; /* Prevent capturing clicks when hidden */
}

.history-content.show,
.favorites-content.show {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
  z-index: 50; /* Above content when shown */
  pointer-events: auto; /* Allow interaction when visible */
}

.history-header,
.favorites-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-5) var(--spacing-6);
  border-bottom: 1px solid var(--border);
  background-color: var(--bg-card);
}

/* History Tabs */
.history-tabs,
.favorites-tabs {
  display: flex;
  width: 100%;
  border-bottom: 1px solid var(--border);
}

@media (max-width: 768px) {
  .history-tabs,
  .favorites-tabs {
    padding: var(--spacing-1) 0;
  }
}

@media (max-width: 480px) {
  .history-tabs,
  .favorites-tabs {
    padding: var(--spacing-2) 0;
  }
}

.history-tab,
.favorites-tab {
  flex: 1;
  padding: var(--spacing-2) 0;
  background: none;
  border: none;
  color: var(--text-tertiary);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition) var(--transition-ease);
  position: relative;
}

.history-tab:hover,
.favorites-tab:hover {
  color: var(--text-secondary);
}

.history-tab.active,
.favorites-tab.active {
  color: var(--primary);
}

.history-tab.active::after,
.favorites-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 25%;
  width: 50%;
  height: 2px;
  background-color: var(--primary);
  border-radius: var(--radius-full);
}

@media (max-width: 768px) {
  .history-tab.active::after,
  .favorites-tab.active::after {
    height: 3px;
  }
}

@media (max-width: 480px) {
  .history-tab.active::after,
  .favorites-tab.active::after {
    height: 4px;
    width: 60%;
    left: 20%;
  }
}

.history-tab-content,
.favorites-tab-content {
  display: none;
}

.history-tab-content.active,
.favorites-tab-content.active {
  display: block;
}

.history-title,
.favorites-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.history-close,
.favorites-close {
  font-size: 1.8rem;
  color: var(--text-tertiary);
  cursor: pointer;
  line-height: 1;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: transparent;
  transition: all var(--transition) var(--transition-ease);
}

.history-close:hover,
.favorites-close:hover {
  color: var(--text-primary);
  background-color: rgba(0, 0, 0, 0.05);
}

.history-list {
  max-height: 350px;
  overflow-y: auto;
  margin: 0;
  padding: var(--spacing-2) 0;
  scrollbar-width: thin;
  scrollbar-color: var(--border) transparent;
}

.history-list::-webkit-scrollbar {
  width: 4px;
}

.history-list::-webkit-scrollbar-track {
  background: transparent;
}

.history-list::-webkit-scrollbar-thumb {
  background-color: var(--border);
  border-radius: var(--radius-full);
}

.history-item {
  list-style: none;
  border-bottom: 1px solid var(--border-light);
}

.history-item:last-child {
  border-bottom: none;
}

.history-item-container {
  display: flex;
  align-items: center;
  width: 100%;
  padding: var(--spacing-2) var(--spacing-5) var(--spacing-2) var(--spacing-3);
  transition: background-color var(--transition) var(--transition-ease);
  position: relative;
}

.history-item-container:hover {
  background-color: var(--bg-card-hover);
}

.history-item a {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex: 1;
  padding: var(--spacing-1) 0;
  color: var(--text-secondary);
  transition: color var(--transition) var(--transition-ease);
  text-decoration: none;
}

.history-item a:hover {
  color: var(--text-primary);
}

.history-item-icon {
  flex-shrink: 0;
  font-size: 1.2em;
  color: var(--text-tertiary);
  opacity: 0.7;
}

.history-item-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-item-delete {
  flex-shrink: 0;
  font-size: 1.2rem;
  color: var(--text-tertiary);
  opacity: 0.7;
  cursor: pointer;
  padding: 0 0.4em;
  margin-left: var(--spacing-2);
  margin-right: var(--spacing-4);
  border-radius: var(--radius);
  transition: all var(--transition) var(--transition-ease);
  position: relative;
  right: 0;
  background: none;
  border: none;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  min-height: 24px;
}

.history-item-delete:hover {
  color: var(--accent);
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
}

.history-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-6);
  color: var(--text-tertiary);
  text-align: center;
}

.history-empty-icon {
  font-size: 2rem;
  margin-bottom: var(--spacing-3);
  opacity: 0.5;
}

.history-empty-text {
  font-size: var(--font-size-sm);
}

.history-footer {
  display: flex;
  justify-content: flex-end;
  padding: var(--spacing-3) var(--spacing-4);
  border-top: 1px solid var(--border);
  background-color: var(--bg-dark);
}

.clear-history-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  background-color: var(--accent);
  color: white;
  border: none;
  border-radius: var(--radius);
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition) var(--transition-ease);
}

.clear-history-button:hover {
  background-color: var(--accent-dark);
  transform: translateY(-1px);
}

.clear-history-button:active {
  transform: translateY(0);
}

.clear-history-button-icon {
  font-size: 0.9em;
}

/* History Backdrop for Mobile */
.history-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: -2; /* Below everything when hidden */
  visibility: hidden;
  opacity: 0;
  transition: all var(--transition) var(--transition-ease);
  pointer-events: none; /* Prevent capturing clicks when hidden */
}

.history-backdrop.show {
  visibility: visible;
  opacity: 1;
  z-index: 90; /* Above content but below history content */
  pointer-events: auto; /* Allow interaction when visible */
}

/* Responsive Adjustments for History */
@media (max-width: 768px) {
  /* Show buttons in mobile nav */
  .main-nav .history-dropdown,
  .main-nav .favorites-dropdown {
    display: inline-flex !important;
    margin: 0 5px;
  }

  .main-nav {
    justify-content: flex-end;
    gap: var(--spacing-2);
    position: absolute !important;
    right: 0 !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
  }

  .history-button,
  .favorites-button {
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--font-size-sm);
    gap: var(--spacing-1);
    height: 36px;
    min-width: auto;
  }

  /* Position dropdown from bottom on mobile */
  .history-content,
  .favorites-content {
    position: fixed;
    top: auto !important;
    right: 0 !important;
    bottom: 20px;
    left: 0;
    width: 90%;
    max-width: 400px;
    max-height: 80vh;
    margin: 0 auto;
    z-index: 100;
    transform: translateY(100%);
    pointer-events: auto;
    overflow: visible !important;
    transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    transform-style: preserve-3d;
    -webkit-transform-style: preserve-3d;
    transform: translateY(100%) translateZ(0);
    -webkit-transform: translateY(100%) translateZ(0);
  }

  .history-content.show,
  .favorites-content.show {
    transform: translateY(0) translateZ(0) !important;
    -webkit-transform: translateY(0) translateZ(0) !important;
  }
}

@media (max-width: 480px) {
  .main-nav .history-dropdown,
  .main-nav .favorites-dropdown {
    margin: 0 3px;
  }

  .history-button,
  .favorites-button {
    padding: var(--spacing-1);
    font-size: var(--font-size-xs);
    height: 32px;
  }

  .history-content,
  .favorites-content {
    bottom: 0;
    right: 0 !important;
    left: 0;
    width: 100%;
    max-width: none;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  }
}

/* Loading Skeleton */
.skeleton {
  background: linear-gradient(
    90deg,
    var(--bg-card) 25%,
    var(--bg-card-hover) 37%,
    var(--bg-card) 63%
  );
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

.skeleton-poster {
  aspect-ratio: 2/3;
  border-radius: var(--radius-lg);
}

.skeleton-text {
  height: 1em;
  margin-top: var(--spacing-3);
  border-radius: var(--radius);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Video Player Adjustments */
#player-wrapper {
  position: relative;
  z-index: 1; /* Ensure it's below the header */
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .header-container {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-right: 0 !important;
    position: relative !important;
    width: 100% !important;
  }

  .site-header {
    padding: var(--spacing-8) var(--spacing-4) var(--spacing-8) var(--spacing-3); /* Further increased vertical padding */
  }

  .site-header.header-collapsed {
    padding: var(--spacing-5) var(--spacing-4) var(--spacing-5) var(--spacing-2); /* Further increased vertical padding */
  }

  .site-logo {
    text-align: left;
    margin-bottom: 0;
    font-size: var(--font-size-lg);
    transition: font-size 0.3s ease;
  }

  .site-header.header-collapsed .site-logo {
    font-size: var(--font-size-base);
  }

  .search-form {
    display: none; /* Hide the original search form on mobile */
  }

  .main-nav {
    justify-content: flex-end;
    gap: var(--spacing-3);
    position: absolute !important;
    right: 0 !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .nav-link {
    font-size: var(--font-size-sm);
  }

  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
  }

  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: var(--spacing-4);
  }

  /* Responsive adjustments for episodes */
  .episodes-grid {
    max-height: 350px; /* Smaller height on tablets */
  }

  .episode-button {
    padding: var(--spacing-2);
    min-height: 44px;
  }
}

@media (max-width: 480px) {
  .site-header {
    padding: var(--spacing-6) var(--spacing-3) var(--spacing-6) var(--spacing-2); /* Further increased vertical padding */
  }

  .site-header.header-collapsed {
    padding: var(--spacing-4) var(--spacing-3) var(--spacing-4) var(--spacing-1); /* Further increased vertical padding */
  }

  .site-logo {
    font-size: var(--font-size-base);
    margin-bottom: 0;
  }

  .site-header.header-collapsed .site-logo {
    font-size: var(--font-size-sm);
  }

  /* Original search form is hidden on mobile */

  .main-nav {
    gap: var(--spacing-2);
    position: absolute !important;
    right: 0 !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .nav-link {
    font-size: var(--font-size-xs);
  }

  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--spacing-3);
  }

  .tab-buttons-wrapper {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0;
    margin: 0;
  }

  .tab-buttons {
    gap: var(--spacing-2);
    padding-bottom: var(--spacing-3);
    margin-bottom: var(--spacing-4);
    padding-left: var(--spacing-1);
    padding-right: var(--spacing-1);
    display: inline-flex;
    min-width: min-content;
  }

  .tab-button {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
    min-width: auto;
    flex: 0 0 auto;
    white-space: nowrap;
  }

  .tags-container {
    gap: var(--spacing-2);
  }

  .tag-button {
    padding: var(--spacing-1) var(--spacing-3);
    font-size: var(--font-size-xs);
  }

  /* Make history tabs even more prominent on very small screens */
  .history-tab {
    padding: var(--spacing-2) 0;
    font-size: var(--font-size-sm);
    font-weight: 700;
    letter-spacing: 0.02em;
  }

  /* Responsive adjustments for episodes */
  .episodes-grid {
    max-height: 300px; /* Smaller height on phones */
  }

  .episode-button {
    padding: var(--spacing-1);
    font-size: var(--font-size-sm);
    min-height: 36px;
    line-height: 1.2;
  }

  .episode-page-button {
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--font-size-xs);
  }

  /* Mobile header buttons have been removed - using bottom navigation instead */
}

/* Larger screens get more height for episodes */
@media (min-width: 1200px) {
  .episodes-grid {
    max-height: 500px;
  }
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .main-nav {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important;
    gap: var(--spacing-2) !important;
    position: absolute !important;
    right: 0 !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    padding: 0 !important;
    margin: 0 !important;
    width: auto !important;
  }

  .nav-link,
  .history-button,
  .favorites-button {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: var(--spacing-1) var(--spacing-2) !important;
    font-size: var(--font-size-sm) !important;
    height: 36px !important;
    min-width: auto !important;
    white-space: nowrap !important;
    margin: 0 !important;
    background: none !important;
    border: none !important;
    color: var(--text-secondary) !important;
    cursor: pointer !important;
    transition: all var(--transition) var(--transition-ease) !important;
  }

  .nav-link:hover,
  .history-button:hover,
  .favorites-button:hover {
    color: var(--text-primary) !important;
    background-color: var(--bg-card-hover) !important;
  }

  .nav-link.active,
  .history-button.active,
  .favorites-button.active {
    color: var(--primary) !important;
    font-weight: 600 !important;
  }

  /* Dropdown containers */
  .history-dropdown,
  .favorites-dropdown {
    display: inline-flex !important;
    align-items: center !important;
    margin: 0 !important;
    position: relative !important;
  }

  /* Dropdown content */
  .history-content,
  .favorites-content {
    position: fixed !important;
    top: auto !important;
    right: 0 !important;
    bottom: 20px !important;
    left: 0 !important;
    width: 90% !important;
    max-width: 400px !important;
    max-height: 80vh !important;
    margin: 0 auto !important;
    z-index: 100 !important;
    transform: translateY(100%) !important;
    pointer-events: auto !important;
    overflow: visible !important;
    transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
    will-change: transform !important;
    backface-visibility: hidden !important;
    -webkit-backface-visibility: hidden !important;
    transform-style: preserve-3d !important;
    -webkit-transform-style: preserve-3d !important;
  }

  .history-content.show,
  .favorites-content.show {
    transform: translateY(0) !important;
    -webkit-transform: translateY(0) !important;
  }
}

@media (max-width: 480px) {
  .main-nav {
    gap: var(--spacing-1) !important;
  }

  .nav-link,
  .history-button,
  .favorites-button {
    padding: var(--spacing-1) !important;
    font-size: var(--font-size-xs) !important;
    height: 32px !important;
  }

  .history-content,
  .favorites-content {
    bottom: 0 !important;
    width: 100% !important;
    max-width: none !important;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0 !important;
  }
}

/* Ensure dropdowns are above other content */
.history-content,
.favorites-content {
  z-index: 1000 !important;
}

/* Add backdrop for mobile dropdowns */
.history-backdrop,
.favorites-backdrop,
.categories-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease;
  display: none;
}

.history-backdrop.show,
.favorites-backdrop.show,
.categories-backdrop.show {
  opacity: 1;
  visibility: visible;
  display: block;
}

/* Fix z-index for mobile dropdowns */
.history-content,
.favorites-content,
.categories-content {
  z-index: 1000;
}

/* Fix for header dropdowns on mobile */
@media (max-width: 768px) {
  .main-nav {
    z-index: 11 !important;
  }
  
  /* Force content IDs */
  #history-dropdown,
  #favorites-dropdown,
  #categories-dropdown {
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    height: 100% !important;
    max-height: 100vh !important;
    margin: 0 !important;
    overflow: hidden !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    border: none !important;
    transform: translateY(100%) !important;
    visibility: hidden !important;
    opacity: 0 !important;
    transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1), opacity 0.3s ease, visibility 0.3s !important;
  }
  
  /* Content headers and bodies for full screen */
  .history-header,
  .favorites-header,
  .categories-header {
    padding: var(--spacing-6) var(--spacing-4) !important;
    border-bottom: 1px solid var(--border) !important;
    position: sticky !important;
    top: 0 !important;
    background-color: var(--bg-card) !important;
    z-index: 10 !important;
  }
  
  .history-body,
  .favorites-body,
  .categories-body {
    height: calc(100% - 70px) !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }
  
  .history-tabs,
  .favorites-tabs {
    position: sticky !important;
    top: 70px !important;
    background-color: var(--bg-card) !important;
    z-index: 9 !important;
  }
  
  .history-list,
  .favorites-list,
  .categories-list {
    max-height: none !important;
    height: auto !important;
    padding-bottom: 80px !important; /* Space for bottom nav */
  }
  
  .history-close,
  .favorites-close,
  .categories-close {
    width: 44px !important;
    height: 44px !important;
    font-size: 1.8rem !important;
  }
  
  #history-dropdown.show,
  #history-dropdown.active,
  #favorites-dropdown.show,
  #favorites-dropdown.active,
  #categories-dropdown.show,
  #categories-dropdown.active {
    transform: translateY(0) !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}
