/**
 * JavaScript for configurable categories
 */

// Fix for categories dropdown toggle issue
function fixCategoriesDropdown() {
    // Override the directCloseAll function to ensure it doesn't break the categories dropdown
    if (typeof directCloseAll === 'function') {
        const originalDirectCloseAll = window.directCloseAll;

        window.directCloseAll = function() {
            // Call the original function
            originalDirectCloseAll();

            // Fix the categories dropdown after a short delay
            setTimeout(function() {
                const categoriesDropdown = document.getElementById('categories-dropdown');
                if (categoriesDropdown) {
                    // Reset the display property to allow the dropdown to be shown again
                    categoriesDropdown.style.display = '';

                    // Make sure the transform is reset for desktop
                    if (window.innerWidth > 768) {
                        categoriesDropdown.style.transform = '';
                    }

                    // Reset other properties that might prevent reopening
                    categoriesDropdown.style.pointerEvents = '';
                }

                // Also fix history and favorites dropdowns
                const historyDropdown = document.getElementById('history-dropdown');
                if (historyDropdown) {
                    // Reset properties to allow reopening
                    setTimeout(() => {
                        historyDropdown.style.display = '';
                        historyDropdown.style.visibility = '';
                        historyDropdown.style.opacity = '';
                        historyDropdown.style.zIndex = '';
                        historyDropdown.style.pointerEvents = '';

                        // Reset transform for desktop
                        if (window.innerWidth > 768) {
                            historyDropdown.style.transform = '';
                        }
                    }, 100);
                }

                const favoritesDropdown = document.getElementById('favorites-dropdown');
                if (favoritesDropdown) {
                    // Reset properties to allow reopening
                    setTimeout(() => {
                        favoritesDropdown.style.display = '';
                        favoritesDropdown.style.visibility = '';
                        favoritesDropdown.style.opacity = '';
                        favoritesDropdown.style.zIndex = '';
                        favoritesDropdown.style.pointerEvents = '';

                        // Reset transform for desktop
                        if (window.innerWidth > 768) {
                            favoritesDropdown.style.transform = '';
                        }
                    }, 100);
                }
            }, 300);
        };
    }

    // Override the toggleCategories function to ensure it works properly
    if (typeof toggleCategories === 'function') {
        const originalToggleCategories = window.toggleCategories;

        window.toggleCategories = function() {
            const categoriesDropdown = document.getElementById('categories-dropdown');
            if (!categoriesDropdown) return;

            // Reset any properties that might prevent the dropdown from showing
            categoriesDropdown.style.display = '';
            categoriesDropdown.style.visibility = '';
            categoriesDropdown.style.opacity = '';
            categoriesDropdown.style.zIndex = '';
            categoriesDropdown.style.pointerEvents = '';

            // For desktop, ensure transform is reset
            if (window.innerWidth > 768) {
                categoriesDropdown.style.transform = '';
            }

            // Call the original function
            originalToggleCategories();
        };
    }
}

// Function to ensure the categories dropdown has proper spacing on mobile
function fixCategoriesDropdownSpacing() {
    const categoriesContent = document.getElementById('categories-dropdown');
    const categoriesBody = categoriesContent ? categoriesContent.querySelector('.categories-body') : null;
    const categoriesList = document.getElementById('categories-list');

    if (categoriesContent && categoriesBody && categoriesList && window.innerWidth <= 768) {
        // Get the bottom nav height
        const bottomNav = document.querySelector('.bottom-nav');
        const bottomNavHeight = bottomNav ? bottomNav.offsetHeight : 60;

        // Add padding to the categories content
        categoriesContent.style.paddingBottom = bottomNavHeight + 'px';

        // Ensure the categories body has proper max-height
        categoriesBody.style.maxHeight = `calc(100vh - ${bottomNavHeight + 70}px)`;
        categoriesBody.style.overflowY = 'auto';
        categoriesBody.style.webkitOverflowScrolling = 'touch';

        // Add a spacer element at the bottom of the list if it doesn't exist
        let spacer = categoriesList.querySelector('.categories-spacer');
        if (!spacer) {
            spacer = document.createElement('li');
            spacer.className = 'categories-spacer';
            spacer.style.height = bottomNavHeight + 'px';
            spacer.style.minHeight = bottomNavHeight + 'px';
            spacer.style.background = 'transparent';
            spacer.style.border = 'none';
            spacer.style.margin = '0';
            spacer.style.padding = '0';
            categoriesList.appendChild(spacer);
        } else {
            spacer.style.height = bottomNavHeight + 'px';
            spacer.style.minHeight = bottomNavHeight + 'px';
        }
    }
}

// Function to populate categories from configuration
function populateCategoriesFromConfig() {
    // Get the categories list element
    const categoriesList = document.getElementById('categories-list');
    if (!categoriesList) return;

    // Clear existing categories
    categoriesList.innerHTML = '';

    // Get categories from config
    const categories = window.siteConfig && window.siteConfig.categories ? window.siteConfig.categories : [];

    // If no categories in config, use default categories
    if (!categories || categories.length === 0) {
      const defaultCategories = [
        { title: '动漫', path: '/vod/list/?kw=动漫' },
        { title: '电影', path: '/vod/list/?kw=电影' },
        { title: '剧集', path: '/vod/list/?kw=剧集' },
        { title: '综艺', path: '/vod/list/?kw=综艺' },
        { title: '纪录片', path: '/vod/list/?kw=纪录片' }
      ];

      defaultCategories.forEach(category => {
        const li = document.createElement('li');
        const a = document.createElement('a');
        a.href = category.path;
        a.textContent = category.title;
        a.onclick = () => directCloseAll();
        li.appendChild(a);
        categoriesList.appendChild(li);
      });
      return;
    }

    // Add categories from config
    categories.forEach(category => {
      // Create list item
      const li = document.createElement('li');

      // Create link
      const a = document.createElement('a');
      a.href = category.path || `/vod/list/?kw=${encodeURIComponent(category.title)}`;
      a.textContent = category.title;
      a.onclick = () => directCloseAll();

      // Add link to list item
      li.appendChild(a);

      // Add list item to categories list
      categoriesList.appendChild(li);

      // If this category has subcategories, add them as nested items
      if (category.subcategories && category.subcategories.length > 0) {
        const subUl = document.createElement('ul');
        subUl.className = 'subcategories-list';
        
        category.subcategories.forEach(subcategory => {
          const subLi = document.createElement('li');
          const subA = document.createElement('a');
          subA.href = subcategory.path;
          subA.textContent = subcategory.title;
          subA.onclick = () => directCloseAll();
          subLi.appendChild(subA);
          subUl.appendChild(subLi);
        });
        
        li.appendChild(subUl);
      }
    });
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Fix categories dropdown toggle issue
    fixCategoriesDropdown();

    // Fix categories dropdown spacing on mobile
    fixCategoriesDropdownSpacing();

    // Populate categories
    populateCategoriesFromConfig();

    // Fix categories dropdown spacing again after populating
    setTimeout(fixCategoriesDropdownSpacing, 100);

    // Update site title if configured
    if (window.siteConfig && window.siteConfig.siteTitle) {
        // Update document title
        const titleElement = document.querySelector('title');
        if (titleElement) {
            if (titleElement.textContent.includes(' - ')) {
                // If title has a separator, replace the part after it
                const parts = titleElement.textContent.split(' - ');
                titleElement.textContent = parts[0] + ' - ' + window.siteConfig.siteTitle;
            } else {
                // Otherwise replace the entire title
                titleElement.textContent = window.siteConfig.siteTitle;
            }
        }

        // Update site logo
        const siteLogoLink = document.getElementById('site-logo-link');
        if (siteLogoLink) {
            siteLogoLink.textContent = window.siteConfig.siteTitle;
        }
    }

    // Add event listeners for categories dropdown
    const categoryButton = document.querySelector('.category-button');
    const categoriesDropdown = document.getElementById('categories-dropdown');

    if (categoryButton && categoriesDropdown) {
        if (window.innerWidth > 768) {
            // Desktop-specific event listeners

            // Ensure the dropdown is properly positioned when opened
            categoryButton.addEventListener('click', function() {
                // Reset any properties that might prevent the dropdown from showing
                categoriesDropdown.style.display = '';
                categoriesDropdown.style.visibility = '';
                categoriesDropdown.style.opacity = '';
                categoriesDropdown.style.zIndex = '';
                categoriesDropdown.style.pointerEvents = '';
                categoriesDropdown.style.transform = '';
            });

            // Add click outside handler for desktop
            document.addEventListener('click', function(e) {
                // If the dropdown is open and the click is outside the dropdown and not on the category button
                if (categoriesDropdown.classList.contains('show') &&
                    !categoriesDropdown.contains(e.target) &&
                    !categoryButton.contains(e.target)) {

                    // Close the dropdown
                    if (typeof closeCategories === 'function') {
                        closeCategories();
                    } else if (typeof directCloseAll === 'function') {
                        directCloseAll();
                    }
                }
            });
        } else {
            // Mobile-specific event listeners

            // Fix spacing when the categories dropdown is opened
            const categoryMobileBtn = document.querySelector('.bottom-nav-btn[data-action="categories"]');
            if (categoryMobileBtn) {
                categoryMobileBtn.addEventListener('click', function() {
                    // Fix spacing after a short delay to ensure the dropdown is open
                    setTimeout(fixCategoriesDropdownSpacing, 100);
                });
            }

            // Also handle direct navigation
            if (typeof directToggleCategories === 'function') {
                const originalDirectToggleCategories = window.directToggleCategories;

                window.directToggleCategories = function() {
                    // Call the original function
                    originalDirectToggleCategories();

                    // Fix spacing after a short delay
                    setTimeout(fixCategoriesDropdownSpacing, 100);
                };
            }
        }
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        // Fix spacing on resize
        fixCategoriesDropdownSpacing();
    });
});
