<!-- 
    Dropdown Templates - Modular dropdown components
    Extracted from base.html for better maintainability
-->

<!-- Categories Dropdown -->
<div class="categories-content" id="categories-dropdown" role="dialog" aria-labelledby="categories-title" aria-hidden="true">
    <div class="categories-header">
        <h3 class="categories-title" id="categories-title">分类导航</h3>
        <button class="categories-close" onclick="Navigation.closeAllDropdowns()" aria-label="关闭分类菜单">
            &times;
        </button>
    </div>
    <div class="categories-body">
        <ul class="categories-list" id="categories-list" role="list">
            <!-- Categories will be populated from config.js -->
        </ul>
    </div>
</div>

<!-- Categories Backdrop -->
<div id="categories-backdrop" class="categories-backdrop" aria-hidden="true"></div>

<!-- History Dropdown -->
<div class="history-content" id="history-dropdown" role="dialog" aria-labelledby="history-title" aria-hidden="true">
    <div class="history-header">
        <h3 class="history-title" id="history-title">历史记录</h3>
        <div class="header-actions">
            <button class="refresh-data-button" onclick="refreshHistoryFromFirebase()" title="从云端刷新数据" aria-label="刷新历史记录">
                🔄
            </button>
            <button class="clear-history-button" onclick="confirmClearHistory('all')" aria-label="清除所有历史记录">
                清除
            </button>
            <button class="history-close" onclick="Navigation.closeAllDropdowns()" aria-label="关闭历史记录">
                &times;
            </button>
        </div>
    </div>

    <!-- History Tabs -->
    <div class="history-tabs" role="tablist" aria-label="历史记录类型">
        <button class="history-tab active" 
                data-tab="viewing" 
                role="tab" 
                aria-selected="true" 
                aria-controls="viewing-history" 
                id="viewing-tab">
            观看历史
        </button>
        <button class="history-tab" 
                data-tab="search" 
                role="tab" 
                aria-selected="false" 
                aria-controls="search-history" 
                id="search-tab">
            搜索历史
        </button>
    </div>

    <!-- Viewing History Tab Content -->
    <div class="history-tab-content active" 
         id="viewing-history" 
         role="tabpanel" 
         aria-labelledby="viewing-tab">
        <div class="history-body">
            <ul class="history-list viewing-list" role="list"></ul>
        </div>
    </div>

    <!-- Search History Tab Content -->
    <div class="history-tab-content" 
         id="search-history" 
         role="tabpanel" 
         aria-labelledby="search-tab">
        <div class="history-body">
            <ul class="history-list search-list" role="list"></ul>
        </div>
    </div>
</div>

<!-- History Backdrop -->
<div id="history-backdrop" class="history-backdrop" aria-hidden="true"></div>

<!-- Favorites Dropdown -->
<div class="favorites-content" id="favorites-dropdown" role="dialog" aria-labelledby="favorites-title" aria-hidden="true">
    <div class="favorites-header">
        <h3 class="favorites-title" id="favorites-title">收藏夹</h3>
        <div class="header-actions">
            <button class="refresh-data-button" onclick="refreshHistoryFromFirebase()" title="从云端刷新数据" aria-label="刷新收藏夹">
                🔄
            </button>
            <button class="clear-favorites-button" onclick="confirmClearFavorites()" aria-label="清除所有收藏">
                清除
            </button>
            <button class="favorites-close" onclick="Navigation.closeAllDropdowns()" aria-label="关闭收藏夹">
                &times;
            </button>
        </div>
    </div>
    <div class="favorites-body">
        <ul class="favorites-list" role="list"></ul>
    </div>
</div>

<!-- Favorites Backdrop -->
<div id="favorites-backdrop" class="favorites-backdrop" aria-hidden="true"></div>

<!-- Mobile Search Overlay -->
<div class="mobile-search-overlay" role="dialog" aria-labelledby="mobile-search-title" aria-hidden="true">
    <div class="mobile-search-container">
        <div class="mobile-search-header">
            <h3 id="mobile-search-title">搜索</h3>
            <button class="mobile-search-close" onclick="Navigation.closeMobileSearch()" aria-label="关闭搜索">
                &times;
            </button>
        </div>
        <div class="mobile-search-form">
            <form action="/vod/search/" method="GET">
                <div class="mobile-search-input-group">
                    <input type="text" 
                           name="kw" 
                           class="mobile-search-input" 
                           placeholder="输入关键词" 
                           autocomplete="off" 
                           autocorrect="off" 
                           autocapitalize="off" 
                           spellcheck="false"
                           aria-label="搜索关键词">
                    <button type="submit" class="mobile-search-submit" aria-label="执行搜索">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="11" cy="11" r="8"></circle>
                            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                        </svg>
                    </button>
                </div>
                <div class="mobile-search-options" role="radiogroup" aria-label="搜索类型">
                    <label class="mobile-search-option">
                        <input type="radio" name="field" value="title" checked>
                        <span>搜标题</span>
                    </label>
                    <label class="mobile-search-option">
                        <input type="radio" name="field" value="tag">
                        <span>搜标签</span>
                    </label>
                    <label class="mobile-search-option">
                        <input type="radio" name="field" value="staff">
                        <span>搜人员</span>
                    </label>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Loading Indicator for Firebase -->
<div id="firebase-loading-indicator" 
     style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999; background-color: rgba(255, 255, 255, 0.95); border-radius: 8px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); padding: 20px; width: auto; max-width: 90%; text-align: center;"
     role="status"
     aria-live="polite"
     aria-label="正在加载">
    <div style="display: flex; align-items: center; justify-content: center; gap: 12px;">
        <div style="width: 24px; height: 24px; border: 2px solid #f3f3f3; border-top: 2px solid #3498db; border-radius: 50%; animation: spin 0.8s linear infinite;" aria-hidden="true"></div>
        <div style="font-size: 14px; color: #333;">正在加载...</div>
    </div>
</div>

<style>
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* Ensure dropdowns are properly hidden by default */
    .categories-content,
    .history-content,
    .favorites-content,
    .mobile-search-overlay {
        visibility: hidden;
        opacity: 0;
        pointer-events: none;
        z-index: -1;
    }
    
    /* Show state */
    .categories-content.show,
    .history-content.show,
    .favorites-content.show,
    .mobile-search-overlay.active {
        visibility: visible;
        opacity: 1;
        pointer-events: auto;
        z-index: 100;
    }
    
    /* Backdrop styles */
    .categories-backdrop,
    .history-backdrop,
    .favorites-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: -1;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }
    
    .categories-backdrop.show,
    .history-backdrop.show,
    .favorites-backdrop.show {
        z-index: 50;
        opacity: 1;
        visibility: visible;
    }
    
    /* Mobile-specific styles */
    @media (max-width: 768px) {
        .categories-content,
        .history-content,
        .favorites-content {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100vh;
            border-radius: 0;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }
        
        .categories-content.show,
        .history-content.show,
        .favorites-content.show {
            transform: translateY(0);
        }
    }
    
    /* Desktop positioning */
    @media (min-width: 769px) {
        .categories-content,
        .history-content,
        .favorites-content {
            position: fixed;
            top: 4rem;
            right: auto;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            max-width: 90%;
            border-radius: 0.5rem;
            background-color: var(--bg-dark, #111827);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2), 0 0 0 1px var(--border, #374151);
        }
    }
</style>
