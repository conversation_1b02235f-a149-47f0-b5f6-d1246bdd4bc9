/**
 * Configuration Index
 * This file serves as an entry point for all configuration files
 */

// Import all configuration files
try {
  import('./config.js');
  
  // You can add more configuration imports here in the future
  // For example:
  // import './theme-config.js';
  // import './api-config.js';
  // etc.
} catch (error) {
  // Use the _tempLog function if available (from console-production.js)
  if (window._tempLog) {
    window._tempLog('Error loading configuration:', error);
  } else {
    // Fallback to console.error if _tempLog is not available
    console.error('Error loading configuration:', error);
  }
}

console.log('Configuration loaded successfully');
