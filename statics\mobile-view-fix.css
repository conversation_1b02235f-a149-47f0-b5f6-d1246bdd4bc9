/* Mobile View Fix - Comprehensive fixes for mobile display issues */

/* Disable horizontal scrolling for all devices */
html, body {
  max-width: 100% !important;
  overflow-x: hidden !important;
}

/* Global mobile adjustments */
@media (max-width: 768px) {
  /* Ensure proper content width */
  .wrapper, .container, .main-container {
    width: 100% !important;
    max-width: 100% !important;
    padding-left: var(--spacing-2) !important;
    padding-right: var(--spacing-2) !important;
    overflow-x: hidden !important;
  }
  
  /* Fix scrolling issues for search results page */
  .search-results-page .main-content {
    width: 100% !important;
    padding: 0 0 70px 0 !important; /* Add padding at bottom for the bottom nav */
    margin: 0 !important;
    overflow-x: hidden !important;
    overflow-y: auto !important; /* Ensure vertical scrolling works */
    -webkit-overflow-scrolling: touch !important; /* Smooth scrolling on iOS */
  }

  /* Fix for main content area */
  .main-content {
    width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow-x: hidden !important;
  }

  /* Ensure all content containers prevent horizontal overflow */
  main, .wrapper, .container, section, div {
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  /* Fix for any potentially overflowing elements */
  img, video, iframe, table {
    max-width: 100% !important;
    height: auto !important;
  }

  /* Fix for code blocks and preformatted text */
  pre, code {
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    overflow-x: auto !important;
    max-width: 100% !important;
  }

  /* Ensure text is readable and doesn't cause horizontal overflow */
  body {
    font-size: var(--font-size-sm) !important;
    line-height: 1.4 !important;
  }

  /* Prevent text from causing horizontal overflow */
  p, h1, h2, h3, h4, h5, h6, span, a, li {
    overflow-wrap: break-word !important;
    word-wrap: break-word !important;
    -ms-word-break: break-all !important;
    word-break: break-word !important;
    -ms-hyphens: auto !important;
    -moz-hyphens: auto !important;
    -webkit-hyphens: auto !important;
    hyphens: auto !important;
  }

  /* Fix for card grids */
  .card-grid, .vod-list {
    display: grid !important;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr)) !important;
    gap: 0.75rem !important;
    width: 100% !important;
  }

  /* Specific fix for search results page grid */
  .search-results-page .vod-list {
    padding-bottom: 10px !important; /* Add some space at the bottom */
    width: 100% !important;
    margin-bottom: 0 !important;
  }

  /* Fix for media cards */
  .media-card, .vod-item {
    width: 100% !important;
    margin: 0 !important;
  }

  /* Fix for media titles */
  .media-title, .vod-title {
    font-size: var(--font-size-sm) !important;
    line-height: 1.3 !important;
    max-height: 2.6em !important;
    overflow: hidden !important;
  }

  /* Fix for pagination */
  .pagination {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    gap: var(--spacing-2) !important;
    margin: var(--spacing-4) 0 !important;
  }
  
  /* Fix for search results pagination to ensure it's above the bottom nav */
  .search-results-page .pagination, 
  .search-results-page .flex.flex-wrap.items-center.justify-center.gap-2.mt-6 {
    margin-bottom: 20px !important;
  }

  /* Fix for header */
  .site-header {
    padding: var(--spacing-4) var(--spacing-2) !important;
    position: sticky !important;
    top: 0 !important;
    z-index: 100 !important;
    background-color: var(--bg-dark) !important;
  }

  /* Fixed header buttons have been removed - using bottom navigation instead */

  /* Hide desktop elements on mobile */
  .desktop-only {
    display: none !important;
  }

  /* Ensure mobile elements are visible */
  .mobile-only {
    display: block !important;
  }

  /* Fix for search form */
  .search-form {
    display: none !important; /* Hide desktop search on mobile */
  }

  /* Fixed header buttons have been removed - using bottom navigation instead */

  /* Fix for user links in header - only apply when user is logged in */
  .user-links {
    justify-content: flex-end !important;
    margin-left: auto !important;
    position: relative !important;
    z-index: 10 !important;
    align-items: center !important;
    /* Note: We removed display: flex !important to allow proper hiding when not logged in */
  }

  /* Style for username display */
  .user-display-name {
    color: var(--text-secondary) !important;
    font-weight: 500 !important;
    padding: 0.25rem 0 !important;
  }

  /* Style the logout button to reduce accidental touches */
  .user-links .logout-btn {
    padding: 0.25rem 0.5rem !important;
    background-color: var(--bg-card) !important; /* Less prominent color */
    color: var(--text-secondary) !important; /* Less prominent text color */
    border: 1px solid var(--border) !important;
    border-radius: 4px !important;
    font-weight: 500 !important; /* Slightly less bold */
    font-size: 0.9rem !important;
    white-space: nowrap !important;
    position: relative !important;
    top: 2px !important; /* Move slightly away from the top edge */
    right: 2px !important; /* Move slightly away from the right edge */
    margin-left: 8px !important; /* Add some space to separate from other elements */
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  /* Smaller grid for very small screens */
  .card-grid, .vod-list {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)) !important;
    gap: 0.5rem !important;
  }
  
  /* Fix for search results page on small mobile */
  .search-results-page .main-content {
    padding-bottom: 66px !important; /* Adjust padding to match smaller bottom nav height */
  }
  
  /* Ensure the search results page has proper spacing at the bottom */
  .search-results-page .pagination, 
  .search-results-page .flex.flex-wrap.items-center.justify-center.gap-2.mt-6 {
    margin-bottom: 16px !important;
  }

  /* Adjust header for small screens */
  .site-header {
    padding: var(--spacing-3) var(--spacing-2) !important;
  }

  /* Fixed header buttons have been removed - using bottom navigation instead */

  /* Smaller text for very small screens */
  .media-title, .vod-title {
    font-size: var(--font-size-xs) !important;
  }

  /* Adjust badges and info text */
  .media-badge, .media-info, .vod-badge, .vod-memo {
    font-size: var(--font-size-xs) !important;
    padding: 0.1rem 0.3rem !important;
  }

  /* Adjust username display for very small screens */
  .user-display-name {
    font-size: 0.75rem !important;
    max-width: 70px !important;
    margin-right: 0.3rem !important;
  }

  /* Make logout button more compact and harder to accidentally touch on very small screens */
  .user-links .logout-btn {
    padding: 0.2rem 0.4rem !important;
    font-size: 0.8rem !important;
    margin-right: 0.5rem !important;
    margin-top: 0 !important; /* Reset margin-top */
    opacity: 0.8 !important; /* Make it slightly less prominent */
  }

  /* Add a small touch delay to prevent accidental touches */
  .user-links .logout-btn:active {
    transition-delay: 0.1s !important;
  }

  /* Ensure the logout button doesn't get pushed off screen */
  .main-nav {
    padding-right: var(--spacing-5) !important;
  }
}

/* Fix for dark mode */
@media (prefers-color-scheme: dark) {
  .site-header {
    background-color: var(--bg-dark, #111827) !important;
  }

  /* Fixed header buttons have been removed - using bottom navigation instead */
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}
