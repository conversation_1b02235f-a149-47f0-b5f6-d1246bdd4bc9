{| extend("../base.html") |}

{| block head |}
<script>
    // Add search-results-page class to body
    document.addEventListener('DOMContentLoaded', function() {
        document.body.classList.add('search-results-page');
    });
</script>
{| end block head |}

{| block style |}
<style>
    /* Responsive styles for list view */
    .pagination {
        display: flex;
        align-items: center;
        gap: var(--spacing-2);
        margin-bottom: var(--spacing-4);
        flex-wrap: wrap;
    }

    .pagination a, .pagination span {
        padding: var(--spacing-2) var(--spacing-3);
        border-radius: var(--radius-md);
        background-color: var(--bg-card);
        color: var(--text-primary);
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .pagination a:hover {
        background-color: var(--bg-card-hover);
    }

    .pagination span {
        opacity: 0.5;
        cursor: not-allowed;
    }

    /* Mobile-specific adjustments */
    @media (max-width: 768px) {
        .wrapper {
            padding: 0 var(--spacing-2);
        }

        .pagination {
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .pagination a, .pagination span {
            padding: var(--spacing-2);
            font-size: var(--font-size-sm);
        }
    }
</style>
{| end block style |}

{| block content |}
<div class="wrapper">
    {[ if ctx.err_msg ~= '' then ]}
    <p class="error-message">{{ ctx.err_msg }}</p>
    {[ elseif #ctx.vods == 0 then ]}
    <p class="no-results">无结果。</p>
    {[ else ]}
    {[
    local query_prefix = string.format('?kw=%s', __escape(ctx.kw))
    local curr_offset = tonumber(func.uri.query(ctx.com.urls.current):match('&o=(%d+)'))
    ]}
    <div class="pagination">
        <a href="{= query_prefix =}">首页</a>
        {[ if ctx.offset_prev > 0 or curr_offset and curr_offset > 0 then ]}
        <a href="{= query_prefix =}{[ if ctx.offset_prev > 0 then ]}&o={= ctx.offset_prev =}{[ end ]}">上一页</a>
        {[ else ]}
        <span>上一页</span>
        {[ end ]}
        {[ if ctx.offset_next > 0 then ]}
        <a href="{= query_prefix =}&o={= ctx.offset_next =}">下一页</a>
        {[ end ]}
    </div>
    {| include("incl_list.html", { vod_list = ctx.vods }) |}
    <div class="pagination">
        <a href="{= query_prefix =}">首页</a>
        {[ if ctx.offset_prev > 0 or curr_offset and curr_offset > 0 then ]}
        <a href="{= query_prefix =}{[ if ctx.offset_prev > 0 then ]}&o={= ctx.offset_prev =}{[ end ]}">上一页</a>
        {[ else ]}
        <span>上一页</span>
        {[ end ]}
        {[ if ctx.offset_next > 0 then ]}
        <a href="{= query_prefix =}&o={= ctx.offset_next =}">下一页</a>
        {[ end ]}
    </div>
    {[ end ]}
</div>
{| end block content |}
