/**
 * Deferred Firebase Initialization
 * This file handles loading Firebase modules only when needed (e.g., when login button is clicked)
 * This improves initial page load performance by not loading Firebase until necessary
 *
 * It also checks for existing sessions on page load to maintain login state for returning users
 */

// Flag to track if Firebase is already loaded
let firebaseLoaded = false;
let firebaseLoading = false;
let pendingCallbacks = [];

// Check for existing session on page load
document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM content loaded, checking for Firebase configuration');
  
  // Ensure siteConfig is available
  if (!window.siteConfig) {
    console.error('siteConfig is not defined, Firebase authentication will not work');
  }
  
  // Check if Firebase auth is enabled in config
  const isFirebaseAuthEnabled = window.siteConfig && window.siteConfig.auth && window.siteConfig.auth.enabled !== false;

  if (isFirebaseAuthEnabled) {
    // Check for existing Firebase auth token in localStorage or IndexedDB
    checkForExistingSession();
  }
});

/**
 * Check for existing Firebase auth session
 * This is a lightweight check that doesn't load the full Firebase SDK
 */
async function checkForExistingSession() {
  console.log('Checking for existing Firebase session...');

  try {
    // Check for Firebase auth token in IndexedDB
    // This is a lightweight way to check if user is logged in without loading the full Firebase SDK
    const hasSession = await checkForFirebaseAuthInIndexedDB();

    if (hasSession) {
      console.log('Existing Firebase session found, updating UI and loading Firebase...');

      // Immediately update UI to show logged-in state
      // This ensures the UI shows the logged-in state before Firebase is fully loaded
      updateUIForLoggedInUser();

      // Load Firebase to validate the session
      loadFirebase(() => {
        // After Firebase is loaded, load the Firebase sync manager
        loadFirebaseSyncManager();
      });
    } else {
      console.log('No existing Firebase session found, Firebase will load when needed');
    }
  } catch (error) {
    console.error('Error checking for existing session:', error);
  }
}

/**
 * Update UI for logged-in user
 * This function updates the UI to show the logged-in state
 * It's called immediately when a session is detected, before Firebase is fully loaded
 */
function updateUIForLoggedInUser() {
  console.log('Updating UI for logged-in user');

  // Use our global function to update the UI if available
  if (typeof window.checkAndUpdateAuthUI === 'function') {
    window.checkAndUpdateAuthUI();
    return;
  }

  // Fallback to direct UI update if global function is not available
  // Update UI for signed in user
  const authLinks = document.querySelectorAll('.auth-links');
  const userLinks = document.querySelectorAll('.user-links');

  // Hide login/register links
  authLinks.forEach(el => {
    el.style.display = 'none';
  });

  // Show user links (logout button)
  userLinks.forEach(el => {
    el.style.display = 'flex';
  });

  // Try to get user info from cached data or localStorage
  try {
    // First check if we have cached user data from the IndexedDB/localStorage check
    let userData = window.cachedUserData;

    // If no cached data, try to get it from localStorage
    if (!userData) {
      // Check all localStorage keys for Firebase auth data
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.includes('firebase:authUser:')) {
          try {
            const data = JSON.parse(localStorage.getItem(key));
            if (data && (data.displayName || data.email)) {
              userData = data;
              break;
            }
          } catch (e) {
            console.error('Error parsing Firebase auth data from localStorage:', e);
          }
        }
      }
    }

    // Update user display name if available
    if (userData) {
      console.log('Updating UI with user data:', userData.displayName || userData.email);

      const userDisplayElements = document.querySelectorAll('.user-display-name');
      userDisplayElements.forEach(el => {
        // Truncate long usernames/emails to prevent layout issues
        const displayText = userData.displayName || userData.email;
        const truncatedText = displayText.length > 15 ? displayText.substring(0, 12) + '...' : displayText;
        el.textContent = truncatedText;
        el.title = displayText; // Show full name on hover
      });
    } else {
      console.log('No user data available for UI update');

      // If we don't have user data but we know the user is logged in,
      // show a generic username
      const userDisplayElements = document.querySelectorAll('.user-display-name');
      userDisplayElements.forEach(el => {
        el.textContent = '用户';
        el.title = '已登录用户'; // Show full name on hover
      });
    }
  } catch (error) {
    console.error('Error updating UI with user data:', error);

    // Even if there's an error, still show something in the UI
    const userDisplayElements = document.querySelectorAll('.user-display-name');
    userDisplayElements.forEach(el => {
      el.textContent = '用户';
      el.title = '已登录用户'; // Show full name on hover
    });
  }
}

/**
 * Check for Firebase auth data in IndexedDB
 * This is a lightweight way to check if user is logged in without loading the full Firebase SDK
 * @returns {Promise<boolean>} True if Firebase auth data exists in IndexedDB
 */
function checkForFirebaseAuthInIndexedDB() {
  return new Promise((resolve) => {
    // Check if IndexedDB is supported
    if (!window.indexedDB) {
      console.log('IndexedDB not supported, falling back to localStorage check');
      // Fall back to checking localStorage for a token
      let hasLocalStorageToken = false;
      let userData = null;

      // Check all localStorage keys for Firebase auth data
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.includes('firebase:authUser:')) {
          hasLocalStorageToken = true;
          try {
            userData = JSON.parse(localStorage.getItem(key));
            // Store user data in a global variable for UI updates
            if (userData) {
              window.cachedUserData = userData;
            }
          } catch (e) {
            console.error('Error parsing Firebase auth data from localStorage:', e);
          }
          break;
        }
      }

      return resolve(hasLocalStorageToken);
    }

    // Try to open the Firebase auth IndexedDB database
    const request = window.indexedDB.open('firebaseLocalStorageDb');
    let hasAuthData = false;

    // Set a timeout to resolve after 500ms in case the IndexedDB operations take too long
    const timeoutId = setTimeout(() => {
      console.log('IndexedDB check timed out, falling back to localStorage check');

      // Fall back to checking localStorage for a token
      let hasLocalStorageToken = false;

      // Check all localStorage keys for Firebase auth data
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.includes('firebase:authUser:')) {
          hasLocalStorageToken = true;
          try {
            const userData = JSON.parse(localStorage.getItem(key));
            // Store user data in a global variable for UI updates
            if (userData) {
              window.cachedUserData = userData;
            }
          } catch (e) {
            console.error('Error parsing Firebase auth data from localStorage:', e);
          }
          break;
        }
      }

      resolve(hasLocalStorageToken);
    }, 500);

    request.onerror = function() {
      clearTimeout(timeoutId);
      console.log('Error opening IndexedDB, falling back to localStorage check');

      // Fall back to checking localStorage for a token
      let hasLocalStorageToken = false;

      // Check all localStorage keys for Firebase auth data
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.includes('firebase:authUser:')) {
          hasLocalStorageToken = true;
          try {
            const userData = JSON.parse(localStorage.getItem(key));
            // Store user data in a global variable for UI updates
            if (userData) {
              window.cachedUserData = userData;
            }
          } catch (e) {
            console.error('Error parsing Firebase auth data from localStorage:', e);
          }
          break;
        }
      }

      resolve(hasLocalStorageToken);
    };

    request.onsuccess = function(event) {
      try {
        const db = event.target.result;

        // Check if the 'firebaseLocalStorage' object store exists
        if (!db.objectStoreNames.contains('firebaseLocalStorage')) {
          clearTimeout(timeoutId);
          console.log('No firebaseLocalStorage object store found');

          // Fall back to checking localStorage for a token
          let hasLocalStorageToken = false;

          // Check all localStorage keys for Firebase auth data
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.includes('firebase:authUser:')) {
              hasLocalStorageToken = true;
              try {
                const userData = JSON.parse(localStorage.getItem(key));
                // Store user data in a global variable for UI updates
                if (userData) {
                  window.cachedUserData = userData;
                }
              } catch (e) {
                console.error('Error parsing Firebase auth data from localStorage:', e);
              }
              break;
            }
          }

          resolve(hasLocalStorageToken);
          return;
        }

        // Open a transaction to check for auth data
        const transaction = db.transaction(['firebaseLocalStorage'], 'readonly');
        const objectStore = transaction.objectStore('firebaseLocalStorage');
        const getAllRequest = objectStore.getAll();

        getAllRequest.onsuccess = function() {
          clearTimeout(timeoutId);
          const items = getAllRequest.result || [];

          // Check if any of the items contain auth data
          for (const item of items) {
            if (item.key && item.key.includes('authUser')) {
              hasAuthData = true;

              // Try to extract user data for UI updates
              try {
                if (item.value && typeof item.value === 'string') {
                  const userData = JSON.parse(item.value);
                  // Store user data in a global variable for UI updates
                  if (userData) {
                    window.cachedUserData = userData;
                  }
                }
              } catch (e) {
                console.error('Error parsing user data from IndexedDB:', e);
              }

              break;
            }
          }

          console.log('IndexedDB check complete, auth data found:', hasAuthData);

          if (!hasAuthData) {
            // Fall back to checking localStorage for a token
            let hasLocalStorageToken = false;

            // Check all localStorage keys for Firebase auth data
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key && key.includes('firebase:authUser:')) {
                hasLocalStorageToken = true;
                try {
                  const userData = JSON.parse(localStorage.getItem(key));
                  // Store user data in a global variable for UI updates
                  if (userData) {
                    window.cachedUserData = userData;
                  }
                } catch (e) {
                  console.error('Error parsing Firebase auth data from localStorage:', e);
                }
                break;
              }
            }

            resolve(hasLocalStorageToken);
          } else {
            resolve(hasAuthData);
          }
        };

        getAllRequest.onerror = function() {
          clearTimeout(timeoutId);
          console.log('Error getting data from IndexedDB, falling back to localStorage check');

          // Fall back to checking localStorage for a token
          let hasLocalStorageToken = false;

          // Check all localStorage keys for Firebase auth data
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.includes('firebase:authUser:')) {
              hasLocalStorageToken = true;
              try {
                const userData = JSON.parse(localStorage.getItem(key));
                // Store user data in a global variable for UI updates
                if (userData) {
                  window.cachedUserData = userData;
                }
              } catch (e) {
                console.error('Error parsing Firebase auth data from localStorage:', e);
              }
              break;
            }
          }

          resolve(hasLocalStorageToken);
        };
      } catch (error) {
        clearTimeout(timeoutId);
        console.error('Error in IndexedDB transaction:', error);

        // Fall back to checking localStorage for a token
        let hasLocalStorageToken = false;

        // Check all localStorage keys for Firebase auth data
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.includes('firebase:authUser:')) {
            hasLocalStorageToken = true;
            try {
              const userData = JSON.parse(localStorage.getItem(key));
              // Store user data in a global variable for UI updates
              if (userData) {
                window.cachedUserData = userData;
              }
            } catch (e) {
              console.error('Error parsing Firebase auth data from localStorage:', e);
            }
            break;
          }
        }

        resolve(hasLocalStorageToken);
      }
    };
  });
}

/**
 * Show a message to the user
 * @param {string} message - Message to show
 * @param {string} type - Type of message (error, success, info)
 */
function showMessage(message, type = 'info') {
  const messageDiv = document.createElement('div');
  messageDiv.style.position = 'fixed';
  messageDiv.style.top = '10px';
  messageDiv.style.left = '50%';
  messageDiv.style.transform = 'translateX(-50%)';
  messageDiv.style.padding = '12px 24px';
  messageDiv.style.borderRadius = '4px';
  messageDiv.style.fontWeight = '500';
  messageDiv.style.zIndex = '9999';
  messageDiv.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
  messageDiv.style.animation = 'fadeIn 0.3s ease';
  messageDiv.textContent = message;

  // Set styles based on message type
  if (type === 'error') {
    messageDiv.style.backgroundColor = 'rgba(220, 38, 38, 0.9)';
    messageDiv.style.color = 'white';
  } else if (type === 'success') {
    messageDiv.style.backgroundColor = 'rgba(16, 185, 129, 0.9)';
    messageDiv.style.color = 'white';
  } else {
    messageDiv.style.backgroundColor = 'rgba(59, 130, 246, 0.9)';
    messageDiv.style.color = 'white';
  }

  // Add animation style
  const style = document.createElement('style');
  style.textContent = `
    @keyframes fadeIn {
      from { opacity: 0; transform: translate(-50%, -20px); }
      to { opacity: 1; transform: translate(-50%, 0); }
    }
    @keyframes fadeOut {
      from { opacity: 1; transform: translate(-50%, 0); }
      to { opacity: 0; transform: translate(-50%, -20px); }
    }
  `;
  document.head.appendChild(style);

  document.body.appendChild(messageDiv);

  // Remove message after delay
  setTimeout(() => {
    messageDiv.style.animation = 'fadeOut 0.3s ease';
    messageDiv.addEventListener('animationend', () => {
      if (document.body.contains(messageDiv)) {
        document.body.removeChild(messageDiv);
      }
    });
  }, 2000);
}

/**
 * Load Firebase modules and initialize Firebase
 * @param {Function} callback - Function to call when Firebase is loaded
 */
async function loadFirebase(callback) {
  // If Firebase is already loaded, just call the callback
  if (window.firebaseAuth) {
    console.log('Firebase already loaded, skipping initialization');
    if (callback) callback();
    return;
  }

  // Set loading flag
  firebaseLoading = true;

  // Show loading indicator
  showFirebaseLoadingIndicator();

  // Set a timeout to prevent hanging
  const loadingTimeout = setTimeout(() => {
    console.error('Firebase loading timed out');
    firebaseLoading = false;
    hideFirebaseLoadingIndicator();
  }, 30000);

  try {
    console.log('Loading Firebase...');

    // Load Firebase app
    const { initializeApp } = await import('https://www.gstatic.com/firebasejs/9.6.1/firebase-app.js');
    
    // Load Firebase auth
    const { 
      getAuth, 
      onAuthStateChanged, 
      signInWithEmailAndPassword, 
      createUserWithEmailAndPassword, 
      updateProfile, 
      signOut, 
      sendPasswordResetEmail 
    } = await import('https://www.gstatic.com/firebasejs/9.6.1/firebase-auth.js');
    
    // Load Firebase firestore
    const { 
      getFirestore, 
      doc, 
      getDoc, 
      setDoc, 
      updateDoc, 
      arrayUnion, 
      arrayRemove, 
      serverTimestamp,
      onSnapshot,
      writeBatch
    } = await import('https://www.gstatic.com/firebasejs/9.6.1/firebase-firestore.js');

    // Clear the timeout
    clearTimeout(loadingTimeout);

    // Initialize Firebase
    const firebaseConfig = window.siteConfig && window.siteConfig.auth && window.siteConfig.auth.firebase;

    // Check if Firebase is disabled in config or if config is missing
    const disableFirebase = !firebaseConfig || (window.siteConfig && window.siteConfig.auth && window.siteConfig.auth.enabled === false);

    // Declare auth variable at the top level of this function so it's accessible throughout
    let auth = null;
    let db = null;
    let app = null;

    if (!disableFirebase) {
      // Initialize Firebase
      app = initializeApp(firebaseConfig);
      
      // Initialize Firebase Auth and get a reference to the service
      auth = getAuth(app);
      
      // Initialize Firestore
      db = getFirestore(app);
      
      // Store references globally
      window.firebaseApp = app;
      window.firebaseAuth = auth;
      window.db = db;
      
      // Store Firestore functions globally
      window.doc = doc;
      window.getDoc = getDoc;
      window.setDoc = setDoc;
      window.updateDoc = updateDoc;
      window.arrayUnion = arrayUnion;
      window.arrayRemove = arrayRemove;
      window.serverTimestamp = serverTimestamp;
      window.onSnapshot = onSnapshot;
      window.writeBatch = writeBatch;
      
      // Store Auth functions globally
      window.signOut = signOut;
      
      // Set up logout function
      window.logoutUser = function() {
        if (auth) {
          signOut(auth).then(() => {
            console.log('User signed out');
            
            // Clear all session storage flags
            sessionStorage.removeItem('vodg-sync-performed');
            sessionStorage.removeItem('vodg-user-logged-in');
            
            // Clear all user-related localStorage items
            const itemsToClear = [
              'vodg-favorites',        // User favorites
              'vodg-vod-history',      // User viewing and search history
              'vodg-play',             // User watch progress
              'history-active-tab'     // User preferred history tab
            ];
            
            // Clear each localStorage item
            itemsToClear.forEach(item => {
              localStorage.removeItem(item);
            });
            
            // Clear any localStorage items with firebase auth data
            for (let i = 0; i < localStorage.length; i++) {
              const key = localStorage.key(i);
              if (key && (key.includes('firebase:') || key.includes('firebaseLocalStorage'))) {
                localStorage.removeItem(key);
              }
            }
            
            // Remove realtime listeners if they exist
            if (window.syncManager && typeof window.syncManager.removeRealtimeListeners === 'function') {
              window.syncManager.removeRealtimeListeners();
            }
            
            // Reset event dispatch flags
            window.userLoggedInEventDispatched = false;
            window.userLoggedOutEventDispatched = false;
            
            console.log('All user data cleared successfully');
            
            // Show feedback to user before redirecting
            showMessage('登出成功，正在重定向...', 'success');
            
            // Redirect to home page after a short delay
            setTimeout(() => {
              window.location.href = '/';
            }, 1000);
          }).catch((error) => {
            console.error('Sign out error:', error);
            alert('登出失败: ' + error.message);
          });
        } else {
          console.error('Auth not initialized');
          alert('Firebase 认证服务未初始化，请刷新页面重试。');
        }
      };

      // Set up auth state change listener
      onAuthStateChanged(auth, (user) => {
        if (user) {
          // User is signed in
          console.log('User is signed in:', user.uid);

          // Update UI using our global function
          if (typeof window.checkAndUpdateAuthUI === 'function') {
            window.checkAndUpdateAuthUI();
          } else {
            // Fallback to direct UI update if global function is not available
            // Update UI for signed in user
            const authLinks = document.querySelectorAll('.auth-links');
            const userLinks = document.querySelectorAll('.user-links');

            authLinks.forEach(el => {
              el.style.display = 'none';
            });

            userLinks.forEach(el => {
              el.style.display = 'flex'; // Show logout button when user is logged in
            });

            // Update user display name if available
            const userDisplayElements = document.querySelectorAll('.user-display-name');
            userDisplayElements.forEach(el => {
              // Truncate long usernames/emails to prevent layout issues
              const displayText = user.displayName || user.email;
              const truncatedText = displayText.length > 15 ? displayText.substring(0, 12) + '...' : displayText;
              el.textContent = truncatedText;
              el.title = displayText; // Show full name on hover
            });
          }

          // Dispatch a custom event that the user is logged in
          window.dispatchEvent(new CustomEvent('userLoggedIn', { 
            detail: { userId: user.uid } 
          }));
          window.userLoggedInEventDispatched = true;

          // Check if we need to sync data
          // Only sync if the syncManager is available and we haven't already synced
          if (window.syncManager && typeof window.syncManager.syncAll === 'function') {
            // Always setup realtime listeners first to ensure we get real-time updates
            if (window.syncManager.setupRealtimeListeners) {
              console.log('Setting up realtime listeners for real-time data updates');
              window.syncManager.setupRealtimeListeners(user.uid);
            }

            // Always perform an initial sync when page loads to ensure latest data
            console.log('Syncing data for logged in user...');
            window.syncManager.syncAll(user.uid).then(() => {
              // Clean up any duplicates after syncing
              if (typeof window.cleanupLocalStorage === 'function') {
                window.cleanupLocalStorage();
              }
              // Mark that we've synced in this session (but we'll still allow future syncs)
              sessionStorage.setItem('vodg-user-logged-in', 'true');
            }).catch(error => {
              console.error('Error syncing data:', error);
            });
          }
        } else {
          // User is signed out
          console.log('User is signed out');

          // Dispatch a custom event that the user is logged out
          window.dispatchEvent(new CustomEvent('userLoggedOut'));
          window.userLoggedOutEventDispatched = true;

          // Clear the sync performed flag when user signs out
          sessionStorage.removeItem('vodg-sync-performed');

          // Clear the logged-in flag
          sessionStorage.removeItem('vodg-user-logged-in');

          // Update UI using our global function
          if (typeof window.checkAndUpdateAuthUI === 'function') {
            window.checkAndUpdateAuthUI();
          } else {
            // Fallback to direct UI update if global function is not available
            // Update UI for signed out user
            const authLinks = document.querySelectorAll('.auth-links');
            const userLinks = document.querySelectorAll('.user-links');

            console.log('Auth state changed: user signed out, updating UI directly');

            authLinks.forEach(el => {
              console.log('Showing auth link in auth state change:', el);
              el.style.display = 'flex';
            });

            userLinks.forEach(el => {
              console.log('Hiding user link in auth state change:', el);
              el.style.display = 'none'; // Hide logout button when user is not logged in
            });
          }
        }
      });

      // Add utility functions
      window.isUserLoggedIn = function() {
        return auth && auth.currentUser !== null;
      };

      window.getCurrentUserId = function() {
        return auth && auth.currentUser ? auth.currentUser.uid : null;
      };

      // Set up login form submission
      setupLoginForm(signInWithEmailAndPassword, auth);

      // Set up register form submission
      setupRegisterForm(createUserWithEmailAndPassword, updateProfile, auth);

      // Set up forgot password form submission
      setupForgotPasswordForm(sendPasswordResetEmail, auth);

      console.log('Firebase initialization complete');
    } else {
      console.log('Firebase is disabled in config, skipping initialization');
    }

    // Set loaded flag
    firebaseLoaded = true;
    firebaseLoading = false;

    // Hide loading indicator
    hideFirebaseLoadingIndicator();

    // Update UI for logged-in user if auth is initialized and user is logged in
    if (auth && auth.currentUser) {
      console.log('Firebase loaded and user is logged in, updating UI');

      // Use our global function to update the UI
      if (typeof window.checkAndUpdateAuthUI === 'function') {
        window.checkAndUpdateAuthUI();
      } else {
        // Fallback to direct UI update if global function is not available
        // Update UI for logged-in user
        const authLinks = document.querySelectorAll('.auth-links');
        const userLinks = document.querySelectorAll('.user-links');

        authLinks.forEach(el => {
          el.style.display = 'none';
        });

        userLinks.forEach(el => {
          el.style.display = 'flex'; // Show logout button when user is logged in
        });

        // Update user display name if available
        const userDisplayElements = document.querySelectorAll('.user-display-name');
        userDisplayElements.forEach(el => {
          // Truncate long usernames/emails to prevent layout issues
          const displayText = auth.currentUser.displayName || auth.currentUser.email;
          const truncatedText = displayText.length > 15 ? displayText.substring(0, 12) + '...' : displayText;
          el.textContent = truncatedText;
          el.title = displayText; // Show full name on hover
        });
      }
    } else {
      // Even if no user is logged in, still check the UI state
      // This handles cases where the user is logged in via localStorage but not yet via Firebase
      if (typeof window.checkAndUpdateAuthUI === 'function') {
        window.checkAndUpdateAuthUI();
      }
    }

    // Call the callback
    if (callback) callback();

    // Call any pending callbacks
    pendingCallbacks.forEach(cb => cb());
    pendingCallbacks = [];
  } catch (error) {
    console.error('Error loading Firebase:', error);
    showErrorMessage('Error loading Firebase: ' + error.message);

    // Reset flags
    firebaseLoading = false;

    // Clear the timeout
    clearTimeout(loadingTimeout);

    // Hide loading indicator
    hideFirebaseLoadingIndicator();
  }
}

/**
 * Show Firebase loading indicator
 */
function showFirebaseLoadingIndicator() {
  const indicator = document.getElementById('firebase-loading-indicator');
  if (indicator) {
    indicator.style.display = 'flex';
  }
}

/**
 * Hide Firebase loading indicator
 */
function hideFirebaseLoadingIndicator() {
  const indicator = document.getElementById('firebase-loading-indicator');
  if (indicator) {
    indicator.style.display = 'none';
  }
}

/**
 * Show error message
 * @param {string} message - Error message to show
 */
function showErrorMessage(message) {
  const errorDiv = document.createElement('div');
  errorDiv.style.position = 'fixed';
  errorDiv.style.top = '10px';
  errorDiv.style.left = '50%';
  errorDiv.style.transform = 'translateX(-50%)';
  errorDiv.style.backgroundColor = 'rgba(220, 38, 38, 0.9)';
  errorDiv.style.color = 'white';
  errorDiv.style.padding = '10px 20px';
  errorDiv.style.borderRadius = '4px';
  errorDiv.style.zIndex = '9999';
  errorDiv.textContent = message;
  document.body.appendChild(errorDiv);

  // Remove error message after 10 seconds
  setTimeout(() => {
    if (document.body.contains(errorDiv)) {
      document.body.removeChild(errorDiv);
    }
  }, 10000);
}

/**
 * Set up login form submission
 */
function setupLoginForm(signInWithEmailAndPassword, auth) {
  const loginForm = document.getElementById('login-form');
  if (loginForm) {
    const loginErrorDiv = document.getElementById('login-error');
    const loginSuccessDiv = document.getElementById('login-success');

    loginForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      // Hide any previous error messages
      loginErrorDiv.style.display = 'none';
      loginSuccessDiv.style.display = 'none';

      // Get form data
      const email = document.getElementById('login-email').value;
      const password = document.getElementById('login-password').value;

      // Show a subtle loading state on the button instead of full-screen indicator
      const submitButton = loginForm.querySelector('button[type="submit"]');
      const originalButtonText = submitButton.textContent;
      submitButton.textContent = '登录中...';
      submitButton.disabled = true;

      try {
        console.log("Attempting to sign in with:", email);
        // Sign in with email and password
        await signInWithEmailAndPassword(auth, email, password);

        // Reset cleanup flag so it runs once after login
        localStorage.removeItem('vodg-cleanup-performed');

        // Show success message
        loginSuccessDiv.textContent = '登录成功';
        loginSuccessDiv.style.display = 'block';

        // Update UI immediately
        if (typeof window.checkAndUpdateAuthUI === 'function') {
          window.checkAndUpdateAuthUI();
        }

        // Close modal after a short delay and redirect to home page
        setTimeout(() => {
          closeLoginModal();

          // Update UI again after modal is closed
          if (typeof window.checkAndUpdateAuthUI === 'function') {
            window.checkAndUpdateAuthUI();
          }

          // Redirect to home page
          window.location.href = '/';
        }, 1000);
      } catch (error) {
        // Show error message
        let errorMessage = '登录失败，请检查您的邮箱和密码。';

        if (error.code === 'auth/user-not-found') {
          errorMessage = '用户不存在，请检查您的邮箱或注册一个新账号。';
        } else if (error.code === 'auth/wrong-password') {
          errorMessage = '密码错误，请重试。';
        } else if (error.code === 'auth/invalid-email') {
          errorMessage = '邮箱格式不正确，请检查。';
        } else if (error.code === 'auth/too-many-requests') {
          errorMessage = '登录尝试次数过多，请稍后再试。';
        } else if (error.code === 'auth/configuration-not-found') {
          errorMessage = 'Firebase 配置错误，请联系管理员。';
        }

        loginErrorDiv.textContent = errorMessage;
        loginErrorDiv.style.display = 'block';
        console.error('Login error:', error.code, error.message);
      } finally {
        // Reset button state
        submitButton.textContent = originalButtonText;
        submitButton.disabled = false;
      }
    });
  }
}

/**
 * Set up register form submission
 */
function setupRegisterForm(createUserWithEmailAndPassword, updateProfile, auth) {
  const registerForm = document.getElementById('register-form');
  if (registerForm) {
    const registerErrorDiv = document.getElementById('register-error');
    const registerSuccessDiv = document.getElementById('register-success');

    registerForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      // Hide any previous error messages
      registerErrorDiv.style.display = 'none';
      registerSuccessDiv.style.display = 'none';

      // Get form data
      const email = document.getElementById('register-email').value;
      const password = document.getElementById('register-password').value;
      const confirmPassword = document.getElementById('register-confirm-password').value;
      const displayName = document.getElementById('register-display-name').value;

      // Check if passwords match
      if (password !== confirmPassword) {
        registerErrorDiv.textContent = '两次输入的密码不匹配，请重新输入。';
        registerErrorDiv.style.display = 'block';
        return;
      }

      // Validate password strength
      if (password.length < 6) {
        registerErrorDiv.textContent = '密码长度至少需要6个字符。';
        registerErrorDiv.style.display = 'block';
        return;
      }

      // Show a subtle loading state on the button instead of full-screen indicator
      const submitButton = registerForm.querySelector('button[type="submit"]');
      const originalButtonText = submitButton.textContent;
      submitButton.textContent = '注册中...';
      submitButton.disabled = true;

      try {
        console.log("Attempting to create user with email:", email);
        // Create user with email and password
        const userCredential = await createUserWithEmailAndPassword(auth, email, password);
        const user = userCredential.user;

        // Update profile with display name
        await updateProfile(user, {
          displayName: displayName
        });

        // Show success message
        registerSuccessDiv.textContent = '注册成功';
        registerSuccessDiv.style.display = 'block';

        // Update UI immediately
        if (typeof window.checkAndUpdateAuthUI === 'function') {
          window.checkAndUpdateAuthUI();
        }

        // Close modal after a short delay and redirect to home page
        setTimeout(() => {
          closeRegisterModal();

          // Update UI again after modal is closed
          if (typeof window.checkAndUpdateAuthUI === 'function') {
            window.checkAndUpdateAuthUI();
          }

          // Redirect to home page
          window.location.href = '/';
        }, 1000);
      } catch (error) {
        // Show error message
        let errorMessage = '注册失败，请重试。';

        if (error.code === 'auth/email-already-in-use') {
          errorMessage = '该邮箱已被注册，请使用其他邮箱或直接登录。';
        } else if (error.code === 'auth/invalid-email') {
          errorMessage = '邮箱格式不正确，请检查。';
        } else if (error.code === 'auth/weak-password') {
          errorMessage = '密码强度太弱，请使用更复杂的密码。';
        } else if (error.code === 'auth/configuration-not-found') {
          errorMessage = 'Firebase 配置错误，请联系管理员。';
        }

        registerErrorDiv.textContent = errorMessage;
        registerErrorDiv.style.display = 'block';
        console.error('Registration error:', error.code, error.message);
      } finally {
        // Reset button state
        submitButton.textContent = originalButtonText;
        submitButton.disabled = false;
      }
    });
  }
}

/**
 * Set up forgot password form submission
 */
function setupForgotPasswordForm(sendPasswordResetEmail, auth) {
  const forgotPasswordForm = document.getElementById('forgot-password-form');
  const forgotPasswordErrorDiv = document.getElementById('forgot-password-error');
  const forgotPasswordSuccessDiv = document.getElementById('forgot-password-success');

  if (forgotPasswordForm) {
    forgotPasswordForm.addEventListener('submit', async (e) => {
      e.preventDefault();

      // Check if auth is initialized
      if (!auth) {
        forgotPasswordErrorDiv.textContent = 'Firebase 认证服务未初始化，请刷新页面重试。';
        forgotPasswordErrorDiv.style.display = 'block';
        return;
      }

      const email = document.getElementById('forgot-password-email').value;

      if (!email) {
        forgotPasswordErrorDiv.textContent = '请输入您的邮箱地址以重置密码。';
        forgotPasswordErrorDiv.style.display = 'block';
        return;
      }

      // Show loading state
      const submitButton = forgotPasswordForm.querySelector('button[type="submit"]');
      const originalButtonText = submitButton.textContent;
      submitButton.textContent = '发送中...';
      submitButton.disabled = true;

      // Hide any previous messages
      forgotPasswordErrorDiv.style.display = 'none';
      forgotPasswordSuccessDiv.style.display = 'none';

      try {
        console.log("Attempting to send password reset email to:", email);
        // Send password reset email
        await sendPasswordResetEmail(auth, email);

        // Show success message
        forgotPasswordSuccessDiv.textContent = '密码重置邮件已发送，请检查您的邮箱。';
        forgotPasswordSuccessDiv.style.display = 'block';
        forgotPasswordErrorDiv.style.display = 'none';

        // Reset form
        forgotPasswordForm.reset();

        // Close modal after a short delay
        setTimeout(() => {
          closeForgotPasswordModal();
          openLoginModal();
        }, 3000);
      } catch (error) {
        // Show error message
        let errorMessage = '发送密码重置邮件失败，请稍后再试。';

        if (error.code === 'auth/user-not-found') {
          errorMessage = '该邮箱地址未注册。';
        } else if (error.code === 'auth/invalid-email') {
          errorMessage = '邮箱格式不正确，请检查。';
        } else if (error.code === 'auth/configuration-not-found') {
          errorMessage = 'Firebase 配置错误，请联系管理员。';
        }

        forgotPasswordErrorDiv.textContent = errorMessage + ' (' + error.code + ')';
        forgotPasswordErrorDiv.style.display = 'block';
        forgotPasswordSuccessDiv.style.display = 'none';
        console.error('Password reset error:', error.code, error.message);
      } finally {
        // Reset button state
        submitButton.textContent = originalButtonText;
        submitButton.disabled = false;
      }
    });
  }
}

/**
 * Load Firebase Sync Manager
 * This function loads the Firebase Sync Manager script and initializes it
 */
function loadFirebaseSyncManager() {
  // Check if sync manager is already loaded
  if (window.syncManager) {
    console.log('Firebase Sync Manager already loaded');
    return;
  }

  console.log('Loading Firebase Sync Manager...');

  // Create script element
  const script = document.createElement('script');
  script.src = '/statics/firebase-sync.js';
  script.async = true;

  // Set up onload handler
  script.onload = function() {
    console.log('Firebase Sync Manager script loaded');

    // Initialize sync manager
    if (typeof FirebaseSyncManager === 'function') {
      window.syncManager = new FirebaseSyncManager();
      console.log('Firebase Sync Manager initialized');

      // If user is already logged in, set up realtime listeners
      if (window.isUserLoggedIn && window.isUserLoggedIn() && 
          window.syncManager.setupRealtimeListeners) {
        const userId = window.getCurrentUserId();
        if (userId) {
          console.log('Setting up realtime listeners for logged in user');
          window.syncManager.setupRealtimeListeners(userId);
        }
      }
    } else {
      console.error('FirebaseSyncManager class not found');
    }
  };

  // Set up error handler
  script.onerror = function() {
    console.error('Error loading Firebase Sync Manager script');
  };

  // Add script to document
  document.body.appendChild(script);
}

/**
 * Global function to check and update the auth UI state
 * This function is called at multiple points to ensure the UI is updated
 */
function checkAndUpdateAuthUI() {
  console.log('Checking and updating auth UI state...');

  // Get all auth links and user links
  const authLinks = document.querySelectorAll('.auth-links');
  const userLinks = document.querySelectorAll('.user-links');

  // Check if user is logged in via Firebase auth
  if (window.firebaseAuth && window.firebaseAuth.currentUser) {
    console.log('User is logged in via Firebase auth:', window.firebaseAuth.currentUser.uid);

    // Set session flag
    sessionStorage.setItem('vodg-user-logged-in', 'true');

    // Update UI
    authLinks.forEach(el => {
      console.log('Hiding auth link:', el);
      el.style.display = 'none';
    });

    userLinks.forEach(el => {
      console.log('Showing user link:', el);
      el.style.display = 'flex';
    });

    // Update user display name if available
    const userDisplayElements = document.querySelectorAll('.user-display-name');
    userDisplayElements.forEach(el => {
      // Truncate long usernames/emails to prevent layout issues
      const displayText = window.firebaseAuth.currentUser.displayName || window.firebaseAuth.currentUser.email;
      const truncatedText = displayText.length > 15 ? displayText.substring(0, 12) + '...' : displayText;
      el.textContent = truncatedText;
      el.title = displayText; // Show full name on hover
    });

    // Dispatch event that user is logged in if not already dispatched
    if (!window.userLoggedInEventDispatched) {
      window.dispatchEvent(new CustomEvent('userLoggedIn', { 
        detail: { userId: window.firebaseAuth.currentUser.uid } 
      }));
      window.userLoggedInEventDispatched = true;
    }

    return true;
  }

  // Check if user is logged in via localStorage (but Firebase is not loaded yet)
  // This is a fallback for when the page loads before Firebase is initialized
  const firebaseAuthItems = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.includes('firebase:authUser:')) {
      firebaseAuthItems.push(key);
    }
  }

  if (firebaseAuthItems.length > 0) {
    console.log('User is logged in via localStorage:', firebaseAuthItems);

    // Set session flag
    sessionStorage.setItem('vodg-user-logged-in', 'true');

    // Update UI
    authLinks.forEach(el => {
      console.log('Hiding auth link:', el);
      el.style.display = 'none';
    });

    userLinks.forEach(el => {
      console.log('Showing user link:', el);
      el.style.display = 'flex';
    });

    // Show a generic username until we can validate the session
    const userDisplayElements = document.querySelectorAll('.user-display-name');
    userDisplayElements.forEach(el => {
      el.textContent = '用户';
      el.title = '已登录用户';
    });

    // If Firebase is not loaded yet, load it to validate the session
    if (!window.firebaseAuth && !firebaseLoaded && !firebaseLoading) {
      console.log('User is logged in via localStorage but Firebase is not loaded, loading Firebase...');
      loadFirebase(() => {
        // After Firebase is loaded, load the Firebase sync manager
        loadFirebaseSyncManager();
      });
    }

    return true;
  }

  // Check if user was logged in in this session
  // This helps maintain the session across page loads
  const wasLoggedInThisSession = sessionStorage.getItem('vodg-user-logged-in') === 'true';
  console.log('Was logged in this session:', wasLoggedInThisSession);

  if (wasLoggedInThisSession) {
    console.log('User was logged in this session, loading Firebase to validate session...');

    // Temporarily update UI to show logged-in state
    authLinks.forEach(el => {
      console.log('Hiding auth link:', el);
      el.style.display = 'none';
    });

    userLinks.forEach(el => {
      console.log('Showing user link:', el);
      el.style.display = 'flex';
    });

    // Show a generic username until we can validate the session
    const userDisplayElements = document.querySelectorAll('.user-display-name');
    userDisplayElements.forEach(el => {
      el.textContent = '用户';
      el.title = '已登录用户';
    });

    // Load Firebase to validate the session
    if (!window.firebaseAuth && !firebaseLoaded && !firebaseLoading) {
      loadFirebase(() => {
        // After Firebase is loaded, load the Firebase sync manager
        loadFirebaseSyncManager();

        // Check auth state again after Firebase is loaded
        setTimeout(checkAndUpdateAuthUI, 500);
      });
    }

    return true;
  }

  // If we get here, user is not logged in
  console.log('User is not logged in, updating UI for logged-out state');

  // Update UI for logged-out user
  authLinks.forEach(el => {
    console.log('Showing auth link:', el);
    el.style.display = 'flex';
  });

  userLinks.forEach(el => {
    console.log('Hiding user link:', el);
    el.style.display = 'none';
  });

  // Dispatch event that user is logged out if not already dispatched
  if (!window.userLoggedOutEventDispatched) {
    window.dispatchEvent(new CustomEvent('userLoggedOut'));
    window.userLoggedOutEventDispatched = true;
  }

  return false;
}

// Call the function on page load
document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM content loaded, checking auth UI state');
  setTimeout(checkAndUpdateAuthUI, 500); // Slight delay to ensure all elements are loaded
});

// Call the function periodically to ensure the UI is updated
// Use a longer interval to reduce unnecessary checks
setInterval(checkAndUpdateAuthUI, 10000); // Changed from 1000ms to 10000ms (10 seconds)

// Export functions
window.loadFirebase = loadFirebase;
window.loadFirebaseSyncManager = loadFirebaseSyncManager;
window.checkAndUpdateAuthUI = checkAndUpdateAuthUI;
