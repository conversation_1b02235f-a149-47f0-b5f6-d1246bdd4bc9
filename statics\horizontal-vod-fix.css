/**
 * Horizontal VOD Image Fix
 * Makes horizontal VOD images display consistently across desktop and mobile
 */

/* Base styles for horizontal VOD images */
.vod-poster.horizontal,
.media-poster.horizontal {
  aspect-ratio: 16/9 !important; /* Horizontal aspect ratio */
}

/* Ensure images maintain proper aspect ratio while filling container */
.vod-poster.horizontal img,
.media-poster.horizontal img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Add a class for VOD items with horizontal posters */
.vod-item.horizontal,
.media-card.horizontal {
  display: flex;
  flex-direction: column;
}

/* Ensure consistent display across all screen sizes */
@media (max-width: 768px) {
  .vod-poster.horizontal,
  .media-poster.horizontal {
    aspect-ratio: 16/9 !important; /* Maintain horizontal aspect ratio on mobile */
  }
  
  .vod-poster.horizontal img,
  .media-poster.horizontal img {
    object-fit: cover !important; /* Force cover to ensure consistency */
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .vod-poster.horizontal,
  .media-poster.horizontal {
    aspect-ratio: 16/9 !important; /* Maintain horizontal aspect ratio on small mobile */
  }
  
  /* Adjust title position for horizontal images on small screens */
  .vod-item.horizontal .vod-title,
  .media-card.horizontal .media-title {
    padding-top: 0.5rem !important;
  }
}

/* Add a class for horizontal images in history items */
.viewing-history-item-poster.horizontal {
  aspect-ratio: 16/9 !important;
  width: 120px !important;
  height: auto !important;
}

/* Mobile adjustments for history items with horizontal images */
@media (max-width: 480px) {
  .viewing-history-item-poster.horizontal {
    width: 100px !important;
    height: auto !important;
  }
}

/* JavaScript to detect and mark horizontal images */
document.addEventListener('DOMContentLoaded', function() {
  // Function to check if an image is horizontal
  function checkImageOrientation(img) {
    if (img.naturalWidth > img.naturalHeight) {
      // Mark parent container as horizontal
      let parent = img.closest('.vod-poster') || img.closest('.media-poster') || img.closest('.viewing-history-item-poster');
      if (parent) {
        parent.classList.add('horizontal');
        
        // Also mark the grandparent (vod-item or media-card)
        let grandparent = parent.closest('.vod-item') || parent.closest('.media-card') || parent.closest('.viewing-history-item');
        if (grandparent) {
          grandparent.classList.add('horizontal');
        }
      }
    }
  }
  
  // Process all VOD images
  document.querySelectorAll('.vod-poster img, .media-poster img, .viewing-history-item-poster img').forEach(img => {
    // If image is already loaded
    if (img.complete) {
      checkImageOrientation(img);
    } else {
      // If image is still loading
      img.addEventListener('load', function() {
        checkImageOrientation(img);
      });
    }
  });
  
  // Create a mutation observer to handle dynamically added images
  const observer = new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === 1) { // Element node
            // Check for images inside the added node
            const images = node.querySelectorAll('.vod-poster img, .media-poster img, .viewing-history-item-poster img');
            images.forEach(img => {
              if (img.complete) {
                checkImageOrientation(img);
              } else {
                img.addEventListener('load', function() {
                  checkImageOrientation(img);
                });
              }
            });
          }
        });
      }
    });
  });
  
  // Start observing the document with the configured parameters
  observer.observe(document.body, { childList: true, subtree: true });
});
