/* Favorites Styles */

/* Favorite Button */
.favorite-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: var(--bg-card);
  color: var(--text-secondary);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition) var(--transition-ease);
  font-size: 14px;
  font-weight: 500;
  margin-right: 8px;
}

.favorite-button:hover {
  background-color: var(--bg-card-hover);
  color: var(--text-primary);
}

.favorite-icon {
  font-size: 18px;
  transition: transform 0.3s var(--transition-bounce);
}

.favorite-icon.active {
  color: #f59e0b; /* Amber color for active state */
}

.favorite-button:hover .favorite-icon {
  transform: scale(1.2);
}

/* Favorites Tab Styles */
.history-tab[data-tab="favorites"] {
  position: relative;
}

/* Favorites List Item */
.favorites-list .history-empty-icon {
  font-size: 24px;
  color: #f59e0b;
}

/* Favorites Dropdown */
.favorites-dropdown {
  position: relative;
  display: inline-block;
}

.favorites-button {
  display: inline-flex;
  align-items: center;
  padding: 8px 12px;
  background-color: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: all var(--transition) var(--transition-ease);
}

.favorites-button:hover {
  color: var(--primary);
}

.favorites-button-icon {
  margin-right: 4px;
  font-size: 16px;
  color: #f59e0b; /* Amber color */
}

/* Favorites Content styles moved to shared-containers.css for standardization */

/* Favorites List styles moved to shared-containers.css for standardization */

.favorites-list .viewing-history-item,
.favorites-list .history-item {
  border-bottom: 1px solid var(--border-light);
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
  position: relative !important;
}

.favorites-list .viewing-history-item:last-child,
.favorites-list .history-item:last-child {
  border-bottom: none;
}

/* Favorites show state styles moved to shared-containers.css for standardization */

/* Favorites header, title, and close button styles moved to shared-containers.css for standardization */

/* Favorites body and footer styles moved to shared-containers.css for standardization */
.favorites-footer,
#favorites-footer {
  justify-content: center !important; /* Center alignment for consistency */
  display: flex !important; /* Ensure it's displayed */
  padding: 12px 16px !important; /* Add padding */
  width: 100% !important; /* Full width */
  z-index: 1001 !important; /* Ensure it's above other elements */
  position: fixed !important; /* Fixed position */
  bottom: 80px !important; /* Position above the navigation bar */
  left: 0 !important;
  right: 0 !important;
  background-color: var(--bg-dark, #111827) !important; /* Match background color */
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2) !important; /* Add shadow */
  border-top: 1px solid var(--border-light, rgba(75, 85, 99, 0.5)) !important; /* Add border */
  height: 60px !important; /* Fixed height */
}

/* Clear favorites button styles moved to history-tabs.css for standardization */

/* Favorites Backdrop */
.favorites-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition) var(--transition-ease);
}

.favorites-backdrop.show {
  opacity: 1;
  visibility: visible;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  /* Hide the favorites button in the top bar on mobile */
  .favorites-dropdown {
    display: none;
  }

  .favorite-button {
    padding: 6px 12px;
    font-size: 13px;
  }

  .favorite-icon {
    font-size: 16px;
  }

  /* Mobile favorites content styles moved to shared-containers.css for standardization */

  /* Mobile favorites show state styles moved to shared-containers.css for standardization */

  /* Mobile favorites header, title, and close button styles moved to shared-containers.css for standardization */

  /* Mobile favorites body, list, and footer styles moved to shared-containers.css for standardization */

  /* Clear favorites button mobile styles moved to history-tabs.css for standardization */

  /* Favorites backdrop for mobile */
  .favorites-backdrop.show {
    z-index: 999; /* Just below the content */
  }

  /* Viewing history item styles for mobile */
  .favorites-list .viewing-history-item {
    margin-bottom: 8px;
  }

  .favorites-list .viewing-history-item-container {
    padding: 16px !important; /* Standardized padding */
    padding-right: 76px !important; /* Increased to make room for both buttons */
    display: flex !important;
    align-items: flex-start !important;
    position: relative !important;
    width: 100% !important;
    box-sizing: border-box !important;
    gap: 14px !important; /* Increased gap for better spacing */
  }

  .favorites-list .viewing-history-item-poster {
    width: 60px !important; /* Standardized size for mobile */
    height: 90px !important;
    min-width: 60px !important;
    max-width: 60px !important;
    flex-shrink: 0 !important;
    display: block !important;
    margin-right: 0 !important; /* Remove margin, use gap instead */
  }

  .favorites-list .viewing-history-item-content {
    flex: 1 !important;
    margin-left: 0 !important; /* Remove margin, use gap instead */
    width: calc(100% - 74px) !important; /* Fixed calculation based on 60px poster width + 14px gap */
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    padding-left: 0 !important; /* Remove padding as we're using margin instead */
  }

  .favorites-list .viewing-history-item-title {
    font-size: 15px !important; /* Larger text */
    margin-bottom: 6px !important; /* Match history item margin */
    font-weight: 600 !important; /* Bolder for better readability */
    color: var(--text-primary) !important;
    white-space: nowrap !important; /* Match history item text overflow handling */
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    display: block !important;
    width: 100% !important;
  }

  .favorites-list .viewing-history-item-actions {
    display: flex !important;
    flex-direction: row !important;
    position: absolute !important;
    top: 12px !important; /* Standardized positioning */
    right: 12px !important; /* Standardized positioning */
    gap: 8px !important; /* Standardized gap */
    align-items: center !important;
    justify-content: center !important;
  }

  .favorites-list .viewing-history-item-delete {
    position: relative !important;
    font-size: 20px !important; /* Larger icon */
    min-width: 40px !important; /* Larger touch target */
    min-height: 40px !important; /* Larger touch target */
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 !important;
    flex-shrink: 0 !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
    background: none !important;
    border: none !important;
    color: var(--text-tertiary, #9ca3af) !important;
    opacity: 0.7 !important;
  }

  .favorites-list .viewing-history-item-delete:hover,
  .favorites-list .viewing-history-item-delete:active {
    color: #ef4444 !important;
    opacity: 1 !important;
    background-color: rgba(255, 255, 255, 0.15) !important;
  }

  .favorites-list .history-item-actions {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    margin-left: auto;
  }

  /* Add episode text styling to match history items */
  .favorites-list .viewing-history-item-episode {
    font-size: 13px !important; /* Larger text */
    display: block !important;
    width: 100% !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    color: #ff9800 !important; /* Match history item episode color */
    margin-bottom: 8px !important; /* Increased spacing */
    line-height: 1.4 !important;
    font-weight: 500 !important;
  }

  /* Adjust favorites footer for mobile */
  .favorites-footer,
  #favorites-footer {
    justify-content: center !important; /* Center alignment for consistency */
    padding: 12px 16px !important; /* Add padding */
    position: fixed !important; /* Fixed position */
    bottom: 76px !important; /* Position above the navigation bar for small devices */
    height: 60px !important; /* Fixed height */
  }
}

/* Animation for favorite button */
@keyframes favorite-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.favorite-button:active .favorite-icon {
  animation: favorite-pulse 0.3s ease-in-out;
}
