{| extend("base.html") |}

{| block head_title |}{{ ctx.com.site.title }} - 发现精彩影视内容{| end block head_title |}

{| block top_bar_search_form |}{| end block top_bar_search_form |}

{| block content |}
{[
local total = func.vod.count(0)
local amount = total
if total > 9999 then
amount = string.format('%.1f 万', total / 10000)
end
]}

<!-- Hero Section -->
<section class="hero">
    <h1 class="hero-title">发现精彩影视内容</h1>
    <p class="hero-subtitle">探索最新、最热门的电影和电视剧</p>

    <!-- Hero Search Form -->
    <form class="search-form hero-search" action="/vod/search/" method="GET">
        <select name="field" class="search-select" aria-label="搜索类型">
            <option value="title">搜标题</option>
            <option value="tag">搜标签</option>
            <option value="staff">搜人员</option>
        </select>
        <input name="kw"
               id="hero-search-input"
               class="search-input"
               placeholder="输入关键词"
               aria-label="搜索关键词">
        <button type="submit" class="search-button">搜索</button>
    </form>

    <!-- Statistics -->
    <div class="hero-stats">
        <span class="stat-item">
            <span class="stat-label">最近更新：</span>
            <span class="stat-value">{= func.vod.count(86400000) =}</span>
        </span>
        <span class="stat-separator">•</span>
        <span class="stat-item">
            <span class="stat-label">全部：</span>
            <span class="stat-value" title="{= total =}">{= amount =}</span>
        </span>
    </div>
</section>

<!-- Main Content Tabs -->
<section class="tabs content-tabs">
    <!-- Tab Navigation -->
    <div class="tab-buttons" role="tablist" aria-label="内容分类">
        <button class="tab-button active"
                data-tab="recent"
                role="tab"
                aria-selected="true"
                aria-controls="recent-tab"
                id="recent-button">
            最近更新
        </button>
        <button class="tab-button"
                data-tab="douban"
                role="tab"
                aria-selected="false"
                aria-controls="douban-tab"
                id="douban-button">
            豆瓣推荐
        </button>
    </div>

    <!-- Tab Content Container -->
    <div class="tab-content-container">
        <!-- Recent Updates Tab -->
        <div class="tab-content active"
             id="recent-tab"
             role="tabpanel"
             aria-labelledby="recent-button">
            {| include("vod/incl_list.html", { vod_list = func.vod.last_updated() }) |}
        </div>

        <!-- Douban Recommendations Tab -->
        <div class="tab-content"
             id="douban-tab"
             role="tabpanel"
             aria-labelledby="douban-button">

            <!-- Douban Controls -->
            <div class="douban-controls">
                <!-- Type Toggle -->
                <div class="toggle-container" role="radiogroup" aria-label="内容类型">
                    <div class="toggle-slider" aria-hidden="true"></div>
                    <button id="douban-movie-toggle"
                            class="toggle-button active"
                            role="radio"
                            aria-checked="true"
                            aria-label="电影">
                        电影
                    </button>
                    <button id="douban-tv-toggle"
                            class="toggle-button"
                            role="radio"
                            aria-checked="false"
                            aria-label="电视剧">
                        电视剧
                    </button>
                </div>

                <!-- Tags and Refresh -->
                <div class="douban-tags-container">
                    <div id="douban-tags"
                         class="tags-container"
                         role="group"
                         aria-label="推荐标签"></div>
                    <button id="douban-refresh"
                            class="btn btn-outline refresh-button"
                            aria-label="换一批推荐内容">
                        换一批
                        <span class="refresh-icon" aria-hidden="true">&#x21bb;</span>
                    </button>
                </div>
            </div>

            <!-- Douban Results Grid -->
            <div id="douban-results"
                 class="card-grid douban-grid"
                 role="region"
                 aria-label="豆瓣推荐内容"
                 aria-live="polite">
                <!-- Results will be populated by JavaScript -->
            </div>
        </div>
    </div>
</section>
{| end block content |}

{| block style |}
<style>
    /* Hero Section Enhancements */
    .hero {
        position: relative;
        overflow: hidden;
    }

    .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(ellipse at center, rgba(37, 99, 235, 0.1) 0%, transparent 70%);
        pointer-events: none;
    }

    .hero-search {
        position: relative;
        z-index: 1;
        max-width: 600px;
        margin: 0 auto var(--spacing-6);
    }

    .hero-stats {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-4);
        color: var(--text-tertiary);
        font-size: var(--font-size-sm);
        margin-bottom: var(--spacing-6);
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-1);
    }

    .stat-value {
        font-weight: var(--font-weight-medium);
        color: var(--text-secondary);
    }

    .stat-separator {
        opacity: 0.5;
    }

    /* Content Tabs */
    .content-tabs {
        margin-top: var(--spacing-8);
    }

    /* Douban Controls */
    .douban-controls {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: var(--spacing-6);
        gap: var(--spacing-4);
        flex-wrap: wrap;
    }

    .douban-tags-container {
        display: flex;
        align-items: flex-start;
        gap: var(--spacing-4);
        flex: 1;
        min-width: 0;
    }

    .tags-container {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-3);
        flex: 1;
        min-width: 0;
    }

    .tag-button {
        position: relative;
        overflow: hidden;
        padding: var(--spacing-2) var(--spacing-4);
        border-radius: var(--radius-md);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        transition: all var(--transition) var(--ease-in-out);
        border: none;
        cursor: pointer;
        background-color: var(--bg-card-hover);
        color: var(--text-tertiary);
        box-shadow: var(--shadow-sm);
    }

    .tag-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: var(--primary);
        transform: scaleX(0);
        transform-origin: left;
        transition: transform var(--transition) var(--ease-in-out);
        z-index: -1;
    }

    .tag-button.active {
        background-color: transparent;
        color: white;
        transform: translateY(-1px);
        box-shadow: var(--shadow);
    }

    .tag-button.active::before {
        transform: scaleX(1);
    }

    .tag-button:not(.active):hover {
        background-color: var(--bg-card);
        color: var(--text-secondary);
        transform: translateY(-1px);
    }

    .refresh-button {
        display: flex;
        align-items: center;
        gap: var(--spacing-2);
        white-space: nowrap;
        flex-shrink: 0;
    }

    .refresh-icon {
        display: inline-block;
        transition: transform var(--transition) var(--ease-in-out);
    }

    .refresh-button:hover .refresh-icon {
        transform: rotate(90deg);
    }

    /* Douban Grid */
    .douban-grid {
        min-height: 400px;
        position: relative;
    }

    .douban-grid.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;
        border: 3px solid var(--border);
        border-top-color: var(--primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    /* Responsive Design */
    @media (max-width: 640px) {
        .hero-search {
            flex-direction: column;
        }

        .hero-stats {
            flex-direction: column;
            gap: var(--spacing-2);
        }

        .douban-controls {
            flex-direction: column;
            align-items: stretch;
        }

        .douban-tags-container {
            flex-direction: column;
            gap: var(--spacing-3);
        }

        .refresh-button {
            align-self: flex-end;
            max-width: 120px;
        }

        .tags-container {
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .hero {
            padding: var(--spacing-8) 0;
        }

        .hero-title {
            font-size: var(--font-size-3xl);
        }

        .hero-subtitle {
            font-size: var(--font-size-base);
        }
    }

    /* Animation for tab content */
    .tab-content {
        animation: none;
    }

    .tab-content.active {
        animation: fadeInUp 0.4s var(--ease-out);
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Ensure smooth transitions */
    @media (prefers-reduced-motion: reduce) {
        .tab-content.active {
            animation: none;
        }

        .refresh-button:hover .refresh-icon {
            transform: none;
        }

        .tag-button:hover,
        .tag-button.active {
            transform: none;
        }
    }
</style>
{| end block style |}

{| block script |}
<script>
    // Enhanced tab switching with better UX
    document.addEventListener('DOMContentLoaded', function() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        // Tab switching functionality
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                const tabName = this.getAttribute('data-tab');

                // Don't do anything if the current tab is already active
                if (this.classList.contains('active')) return;

                // Update ARIA attributes and visual state
                tabButtons.forEach(btn => {
                    btn.classList.remove('active');
                    btn.setAttribute('aria-selected', 'false');
                });

                tabContents.forEach(content => {
                    content.classList.remove('active');
                });

                // Activate current tab
                this.classList.add('active');
                this.setAttribute('aria-selected', 'true');

                const targetContent = document.getElementById(tabName + '-tab');
                if (targetContent) {
                    targetContent.classList.add('active');
                }

                // Initialize Douban tab if needed
                if (tabName === 'douban' && typeof initDoubanTab === 'function') {
                    setTimeout(initDoubanTab, 100);
                }

                // Log tab switch for analytics
                Core.Logger.log('Tab switched to:', tabName);
            });
        });

        // Initialize toggle slider state for Douban controls
        const toggleSlider = document.querySelector('.toggle-slider');
        const tvToggle = document.getElementById('douban-tv-toggle');
        const movieToggle = document.getElementById('douban-movie-toggle');

        if (toggleSlider && tvToggle && movieToggle) {
            // Set initial position
            const isMovieActive = movieToggle.classList.contains('active');
            toggleSlider.style.transform = isMovieActive ? 'translateX(0)' : 'translateX(100%)';

            // Handle toggle clicks
            [movieToggle, tvToggle].forEach(toggle => {
                toggle.addEventListener('click', function() {
                    const isMovie = this.id === 'douban-movie-toggle';

                    // Update active states
                    movieToggle.classList.toggle('active', isMovie);
                    tvToggle.classList.toggle('active', !isMovie);

                    // Update ARIA states
                    movieToggle.setAttribute('aria-checked', isMovie);
                    tvToggle.setAttribute('aria-checked', !isMovie);

                    // Move slider
                    toggleSlider.style.transform = isMovie ? 'translateX(0)' : 'translateX(100%)';

                    Core.Logger.log('Douban type switched to:', isMovie ? 'movie' : 'tv');
                });
            });
        }

        // Enhanced search functionality
        const heroSearchInput = document.getElementById('hero-search-input');
        if (heroSearchInput) {
            // Auto-focus on desktop
            if (Core.Environment.isDesktop()) {
                heroSearchInput.focus();
            }

            // Add search suggestions or history if available
            heroSearchInput.addEventListener('input', Core.Performance.debounce(function() {
                const query = this.value.trim();
                if (query.length > 2) {
                    // Could implement search suggestions here
                    Core.Logger.log('Search query:', query);
                }
            }, 300));
        }
    });
</script>

<!-- Load Douban functionality -->
<script src="/statics/vod/douban_tab_new.js?v=2" defer></script>
{| end block script |}
