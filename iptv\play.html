{| extend("../base.html") |}

{| block content |}
<div id="player-wrapper">
    <div class="player-placeholder">
        <div class="loading" style="margin: auto;"></div>
        <div id="player-loading-status">播放列表加载中，数据量较大，请稍后......</div>
    </div>
</div>
<div>正在播放：<span id="curr-chan"></span>&emsp;&emsp;数据来自网络公共列表，节目单可能有差错</div>
<div id="iptv-channels"></div>
<div id="iptv-epg"></div>
<style>
    #iptv-channels {
        max-height: 300px;
        overflow-y: auto;
        margin-bottom: 2em;
    }

    .channel {
        flex: 1;
        display: inline-flex;
        justify-content: space-between;
        margin-left: 0.5em;
    }

    .channel>span {
        display: flex;
        align-items: top;
    }

    .channel img {
        max-height: 1em;
    }

    .epg {
        display: inline-block;
    }

    .epg-list {
        display: none;
    }

    .epg-list-show {
        display: block;
    }
</style>
{| end block content |}

{| block script |}
<script src="https://s4.zstatic.net/ajax/libs/hls.js/1.5.18/hls.min.js"></script>
<script src="https://s4.zstatic.net/ajax/libs/dplayer/1.27.1/DPlayer.min.js"></script>
<script>
    const play = ({ src }) => {
        const player = new DPlayer({
            container: document.querySelector('#player-wrapper'),
            video: { url: src, type: 'hls' },
        })
        player.play()
    }
    const playChannel = (src, title, logo) => {
        src = src.replace(/^http:/i, 'https:')
        play({ src })
        document.querySelector("#curr-chan").textContent = title
    }
    const selectChannel = (src, title, logo) => {
        playChannel(src, title, logo)
        const hist = JSON.parse(localStorage.getItem('vodg-vod-history'))
        const item = {
            id: 'iptv-' + src,
            url: '/iptv/play/',
            title: title,
            poster: logo,
        }
        if (hist) {
            for (let i = hist.length; i >= 0; --i) {
                if (hist[i]) {
                    if (item.id == hist[i].id) {
                        hist.splice(i, 1)
                    }
                } else {
                    hist.splice(i, 1)
                }
            }
            hist.unshift(item)
            localStorage.setItem('vodg-vod-history', JSON.stringify(hist))
        } else {
            localStorage.setItem('vodg-vod-history', JSON.stringify([item]))
        }
    }
    const toggleEpg = (className) => {
        const cls = document.getElementsByClassName(className)[0].classList
        const CLS = 'epg-list-show'
        cls.contains(CLS) ? cls.remove(CLS) : cls.add(CLS)
        try {
            event.preventDefault()
            event.stopPropagation()
        } catch (_) { }
        return false
    }
</script>
<script type="module">
    import { channels, epg } from '/iptv/statics/iptv-data.js'
    await (async () => {
        const timeStr = (time) => {
            const h = time.substr(8, 2)
            const m = time.substr(10, 2)
            return `${h}:${m}`
        }
        const nowTime = (() => {
            const dt = new Date()
            const h = String(dt.getHours()).padStart(2, '0')
            const m = String(dt.getMinutes()).padStart(2, '0')
            return `${h}:${m}`
        })()
        document.getElementById('player-wrapper').innerHTML = ''
        const player = play({ src: '' })
        let num = 1;
        for (let i = 0; i < channels.length; i++) {
            const ch = channels[i]
            let epgHtml = ''
            for (let j = 0; j < epg.channels.length; j++) {
                if (ch.code == epg.channels[j].code) {
                    let epgList = ''
                    for (let k = 0; k < epg.channels[j].programmes.length; k++) {
                        const prog = epg.channels[j].programmes[k]
                        if (nowTime < timeStr(prog.stop)) {
                            epgList += `<div class="epg-item">${timeStr(prog.start)} - ${timeStr(prog.stop)} ${prog.title}</div>`
                        }
                    }
                    if (epgList.length > 0) {
                        epgHtml = `&nbsp;<div class="epg"><button class="epg-btn" onclick="toggleEpg('epg-list-${i}-${j}')" title="节目单">单</button>
<div class="epg-list epg-list-${i}-${j}">${epgList}</div></div>`
                    }
                    break
                }
            }
            let child = document.createElement('div')
            child.style.display = 'flex'
            child.innerHTML = `${num}. <div class="channel">
<span><a href="javascript:selectChannel('${ch.uri}', '${num}. ${ch.title}', '${ch.logo}')">${ch.title}</a>${epgHtml}</span>
<span>${ch.group_title}</span>
</div>`
            document.querySelector('#iptv-channels').append(child)
            num++
        }
        const hist = JSON.parse(localStorage.getItem('vodg-vod-history'))
        if (hist) {
            for (let i = 0; i < hist.length; i++) {
                if (hist[i] && hist[i].id.startsWith('iptv-')) {
                    playChannel(hist[i].id.replace('iptv-', ''), hist[i].title)
                    break
                }
            }
        } else if (channels[0]) {
            playChannel(channels[0].uri, `1. ${channels[0].title}`)
        }
    })();
</script>
{| end block script |}

{| block style |}
<style>
    @media screen and (max-width: 599px) {
        #player-wrapper {
            width: 100%;
        }
    }

    @media screen and (min-width: 600px) {
        #player-wrapper {
            height: 450px;
            margin-top: 2rem; /* Add spacing between player and header in desktop view */
        }
    }

    .player-placeholder {
        text-align: center;
        padding: 50px 0;
    }

    @media screen and (min-width: 600px) {
        .player-placeholder {
            padding-top: 200px;
        }
    }
</style>
{| end block style |}
