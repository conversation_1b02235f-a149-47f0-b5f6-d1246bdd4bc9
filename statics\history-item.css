/* Enhanced History Item Styles */

/* Viewing History Item */
.viewing-history-item {
  list-style: none !important;
  border-bottom: 1px solid var(--border-light) !important;
  padding: 0 !important;
  margin: 0 !important;
  position: relative !important;
}

.viewing-history-item:last-child {
  border-bottom: none;
}

.viewing-history-item-container {
  display: flex !important;
  width: 100% !important;
  position: relative !important;
  align-items: flex-start !important;
  gap: 12px !important;
  flex-direction: row !important;
  padding: 16px !important; /* Increased padding for better touch targets */
  padding-right: 80px !important; /* Increased to make room for both buttons */
  box-sizing: border-box !important;
  cursor: pointer !important;
  transition: transform 0.3s ease, background-color 0.2s ease !important;
  border-radius: 4px !important;

}

.viewing-history-item-container:hover {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

.viewing-history-item-poster {
  width: 70px !important; /* Increased size for better visibility */
  height: 105px !important; /* Increased size for better visibility */
  min-width: 70px !important;
  max-width: 70px !important;
  flex: 0 0 70px !important;
  flex-shrink: 0 !important;
  border-radius: 6px !important; /* Slightly larger radius */
  overflow: hidden !important;
  background-color: #1f2937 !important;
  position: relative !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25) !important; /* Enhanced shadow */
  border: 1px solid rgba(255, 255, 255, 0.15) !important;
  margin-right: 14px !important;
  display: block !important; /* Ensure it's displayed */
}

.viewing-history-item-poster img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  display: block !important;
}

.viewing-history-item-poster-fallback {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: #2d3748 !important;
  color: #9ca3af !important;
  font-size: 16px !important;
}

.viewing-history-item-content {
  flex: 1 !important;
  margin-left: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: flex-start !important;
  min-width: 0 !important; /* Ensures text truncation works */
  color: white !important;
  background-color: transparent !important;
  z-index: 5 !important;
  position: relative !important;
  width: calc(100% - 70px) !important; /* Adjusted for larger poster */
  padding-right: 20px !important; /* Make room for delete button */
  overflow: hidden !important;
}

.viewing-history-item-title {
  font-weight: 600 !important;
  color: white !important;
  margin-bottom: 6px !important; /* Increased spacing */
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  font-size: 15px !important; /* Slightly larger font */
  line-height: 1.4 !important;
  display: block !important;
  max-width: 100% !important;
}

.viewing-history-item-episode {
  font-size: 13px !important; /* Slightly larger font */
  color: #ff9800 !important;
  margin-bottom: 8px !important; /* Increased spacing */
  line-height: 1.4 !important;
  display: block !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 100% !important;
  font-weight: 500 !important;
}

.viewing-history-item-progress-container {
  height: 4px !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-radius: 999px !important;
  overflow: hidden !important;
  margin-top: 4px !important;
  margin-bottom: 4px !important;
  width: 100% !important;
  display: block !important;
}

.viewing-history-item-progress-bar {
  height: 100% !important;
  background-color: #ff9800 !important;
  border-radius: 999px !important;
  display: block !important;
}

.viewing-history-item-progress-text {
  font-size: 11px !important;
  color: #ffb74d !important;
  text-align: right !important;
  font-weight: 600 !important;
  display: block !important;
  line-height: 1.2 !important;
  margin-top: 2px !important;
}

.viewing-history-item-delete {
  position: relative !important; /* Changed from absolute to relative */
  font-size: 18px !important; /* Larger font size */
  color: #9ca3af !important;
  opacity: 0.7 !important;
  cursor: pointer !important;
  padding: 0 !important;
  border-radius: 6px !important; /* Larger radius */
  transition: all 0.2s ease !important;
  background: none !important;
  border: none !important;
  line-height: 1 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 32px !important; /* Larger touch target */
  min-height: 32px !important; /* Larger touch target */
  width: 32px !important;
  height: 32px !important;
  z-index: 10 !important;
}

.viewing-history-item-delete:hover,
.viewing-history-item-delete:active {
  color: #ff9800 !important;
  opacity: 1 !important;
  background-color: rgba(255, 255, 255, 0.15) !important;
}

/* Favorite indicator styles */
.viewing-history-item.is-favorite {
  background-color: rgba(255, 152, 0, 0.05) !important;
}

.viewing-history-item-favorite-badge {
  position: absolute !important;
  top: 0 !important;
  right: 0 !important;
  background-color: #f59e0b !important;
  color: white !important;
  font-size: 12px !important;
  padding: 2px 4px !important;
  border-radius: 0 6px 0 6px !important;
  z-index: 5 !important;
}

.viewing-history-item-actions {
  display: flex !important;
  flex-direction: row !important; /* Changed from column to row */
  position: absolute !important;
  top: 12px !important;
  right: 12px !important;
  gap: 8px !important;
  align-items: center !important;
  justify-content: center !important;
}

.viewing-history-item-favorite-toggle {
  font-size: 18px !important;
  color: #9ca3af !important;
  opacity: 0.7 !important;
  cursor: pointer !important;
  padding: 0 !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  background: none !important;
  border: none !important;
  line-height: 1 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 32px !important;
  min-height: 32px !important;
  width: 32px !important;
  height: 32px !important;
  z-index: 10 !important;
}

.viewing-history-item-favorite-toggle:hover {
  color: #f59e0b !important;
  opacity: 1 !important;
  background-color: rgba(255, 255, 255, 0.15) !important;
}

.viewing-history-item-favorite-toggle.active {
  color: #f59e0b !important;
  opacity: 1 !important;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .viewing-history-item-container {
    padding: 16px !important; /* Standardized padding */
    padding-right: 76px !important; /* Increased to make room for both buttons */
    width: 100% !important; /* Ensure full width */
    box-sizing: border-box !important;
    gap: 14px !important; /* Increased gap for better spacing */
  }

  .viewing-history-item-poster {
    width: 60px !important; /* Standardized size for mobile */
    height: 90px !important;
    min-width: 60px !important;
    max-width: 60px !important;
    display: block !important; /* Ensure it's displayed */
    margin-right: 0 !important; /* Remove margin, use gap instead */
  }

  .viewing-history-item-content {
    margin-left: 0 !important; /* Remove margin, use gap instead */
    width: calc(100% - 74px) !important; /* Fixed calculation based on 60px poster width + 14px gap */
    flex: 1 !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .viewing-history-item-title {
    font-size: 14px !important; /* Larger than original */
    display: block !important;
    width: 100% !important;
  }

  .viewing-history-item-episode {
    font-size: 12px !important; /* Larger than original */
    display: block !important;
    width: 100% !important;
  }

  .viewing-history-item-delete {
    min-width: 30px !important; /* Larger touch target */
    min-height: 30px !important; /* Larger touch target */
    width: 30px !important;
    height: 30px !important;
    font-size: 18px !important;
    display: flex !important;
  }

  .viewing-history-item-actions {
    top: 10px !important;
    right: 8px !important;
    gap: 6px !important;
    flex-direction: row !important; /* Ensure row layout on mobile */
    display: flex !important;
  }

  .viewing-history-item-favorite-toggle {
    min-width: 30px !important;
    min-height: 30px !important;
    width: 30px !important;
    height: 30px !important;
    font-size: 16px !important;
    display: flex !important;
  }

  /* Fix for history list */
  .history-list,
  .viewing-list,
  .search-list,
  .favorites-list {
    width: 100% !important;
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
    padding-bottom: 180px !important; /* Increased bottom padding to ensure clear button is visible */
    list-style: none !important;
    /* max-height removed - now handled in shared-containers.css */
    overflow-y: auto !important;
    overflow-x: hidden !important;
    visibility: visible !important;
    opacity: 1 !important;
    will-change: opacity !important; /* Optimize for animation */
    backface-visibility: hidden !important; /* Prevent flickering */
    -webkit-backface-visibility: hidden !important;
    transform: translateZ(0) !important; /* Force GPU acceleration */
    -webkit-transform: translateZ(0) !important;
    -webkit-overflow-scrolling: touch !important; /* Smooth scrolling on iOS */
  }

  .history-list li,
  .viewing-list li,
  .search-list li,
  .favorites-list li {
    width: 100% !important;
    display: block !important;
    box-sizing: border-box !important;
    visibility: visible !important;
    opacity: 1 !important;
    will-change: opacity !important; /* Optimize for animation */
    backface-visibility: hidden !important; /* Prevent flickering */
    -webkit-backface-visibility: hidden !important;
    transform: translateZ(0) !important; /* Force GPU acceleration */
    -webkit-transform: translateZ(0) !important;
  }

  /* Fix for search history items */
  .history-item {
    list-style: none !important;
    border-bottom: 1px solid var(--border-light, rgba(75, 85, 99, 0.5)) !important;
    padding: 0 !important;
    margin: 0 !important;
    position: relative !important;
  }

  .history-item:last-child {
    border-bottom: none !important;
  }

  .history-item-container {
    display: flex !important;
    width: 100% !important;
    position: relative !important;
    align-items: center !important;
    padding: 16px !important; /* Standardized padding */
    padding-right: 60px !important; /* Increased for better touch target */
    box-sizing: border-box !important;
    cursor: pointer !important;
    transition: background-color 0.2s ease !important;
    min-height: 60px !important; /* Ensure minimum height for better touch targets */
  }

  .history-item-container:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }

  .history-item-icon {
    margin-right: 14px !important; /* Increased margin for better spacing */
    font-size: 18px !important; /* Larger icon */
    color: var(--text-secondary, #e5e7eb) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 24px !important;
    width: 24px !important;
  }

  .history-item-title {
    font-size: 15px !important; /* Larger text */
    color: var(--text-primary, #f9fafb) !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    flex: 1 !important;
    font-weight: 500 !important; /* Added weight for better readability */
  }

  .history-item-delete {
    position: absolute !important;
    right: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    font-size: 20px !important; /* Larger icon */
    color: var(--text-tertiary, #9ca3af) !important;
    opacity: 0.7 !important;
    cursor: pointer !important;
    padding: 0 !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
    background: none !important;
    border: none !important;
    line-height: 1 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    min-width: 40px !important; /* Larger touch target */
    min-height: 40px !important; /* Larger touch target */
    width: 40px !important;
    height: 40px !important;
    z-index: 10 !important;
  }

  .history-item-delete:hover,
  .history-item-delete:active {
    color: #ef4444 !important;
    opacity: 1 !important;
    background-color: rgba(255, 255, 255, 0.15) !important;
  }
}
