<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- Basic Meta Tags -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="theme-color" content="#111827">
    <meta name="description" content="VOD Guide - 您的在线观影指南">
    <meta name="keywords" content="VOD, 电影, 电视剧, 动漫, 综艺, 在线观看">
    <meta name="author" content="VOD Guide Team">

    <!-- Favicon -->
    <link rel="icon" href="/statics/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="/statics/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="/statics/apple-touch-icon.png">
    <link rel="manifest" href="/statics/manifest.json">

    <!-- Title -->
    <title>{| block head_title |}{{ ctx.com.site.title }}{| end block head_title |}</title>

    <!-- Preload Critical Resources -->
    {| block head_preload |}
    <link rel="preload" href="/statics/css-variables.css" as="style">
    <link rel="preload" href="/statics/main.css" as="style">
    <link rel="preload" href="/statics/core.js" as="script">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" as="style">
    <link rel="preload" href="https://s4.zstatic.net/ajax/libs/hls.js/1.5.18/hls.min.js" as="script">
    {| end block head_preload |}

    <!-- Critical CSS - Inline for fastest rendering -->
    <style>
        /* Critical above-the-fold styles */
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            background: #111827;
            color: #f9fafb;
            overflow-x: hidden;
        }
        .site-header {
            background: #1f2937;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 20;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;
        }
        .site-logo {
            font-size: 1.25rem;
            font-weight: 700;
            color: #f9fafb;
        }
        .site-logo a {
            color: inherit;
            text-decoration: none;
        }
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .main-content {
            min-height: 50vh;
        }
    </style>

    <!-- Load Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Main Stylesheets -->
    <link rel="stylesheet" href="/statics/main.css">

    <!-- Component-specific CSS loaded conditionally -->
    <link rel="stylesheet" href="/statics/shared-containers.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/history-item.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/history-tabs.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/skeleton-loader.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/favorites.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/confirm-dialog.css" media="print" onload="this.media='all'">

    <!-- Mobile-specific CSS -->
    <link rel="stylesheet" href="/statics/mobile-search.css" media="(max-width: 768px)">
    <link rel="stylesheet" href="/statics/mobile-header.css" media="(max-width: 768px)">
    <link rel="stylesheet" href="/statics/mobile-view-fix.css" media="(max-width: 768px)">
    <link rel="stylesheet" href="/statics/bottom-nav.css" media="(max-width: 768px)">
    <link rel="stylesheet" href="/statics/direct-nav.css" media="(max-width: 768px)">

    <!-- Specialized CSS -->
    <link rel="stylesheet" href="/statics/auth.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/category-config.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/horizontal-vod-fix.css" media="print" onload="this.media='all'">

    <!-- Fallback for browsers that don't support onload on link -->
    <noscript>
        <link rel="stylesheet" href="/statics/shared-containers.css">
        <link rel="stylesheet" href="/statics/history-item.css">
        <link rel="stylesheet" href="/statics/history-tabs.css">
        <link rel="stylesheet" href="/statics/skeleton-loader.css">
        <link rel="stylesheet" href="/statics/favorites.css">
        <link rel="stylesheet" href="/statics/confirm-dialog.css">
        <link rel="stylesheet" href="/statics/mobile-search.css">
        <link rel="stylesheet" href="/statics/mobile-header.css">
        <link rel="stylesheet" href="/statics/mobile-view-fix.css">
        <link rel="stylesheet" href="/statics/bottom-nav.css">
        <link rel="stylesheet" href="/statics/direct-nav.css">
        <link rel="stylesheet" href="/statics/auth.css">
        <link rel="stylesheet" href="/statics/category-config.css">
        <link rel="stylesheet" href="/statics/horizontal-vod-fix.css">
    </noscript>

    <!-- Block for additional styles -->
    {| block style |}{| end block style |}
</head>

<body{[ if ctx.com.urls.current == '/' then ]} class="index-page"{[ end ]}>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="skip-link">跳转到主要内容</a>

    <!-- Site Header -->
    <header class="site-header">
        <div class="container header-container">
            {| block top_bar |}
            <!-- Site Logo -->
            <div class="site-logo">
                <a href="/" id="site-logo-link">{{ ctx.com.site.title }}</a>
            </div>

            <!-- Search Form (Desktop) -->
            {| block top_bar_search_form |}
            <form class="search-form" action="/vod/search/" method="GET">
                <select name="field" class="search-select" aria-label="搜索类型">
                    <option value="title">搜标题</option>
                    <option value="tag">搜标签</option>
                    <option value="staff">搜人员</option>
                </select>
                <input name="kw" id="idx-search-input" class="search-input" placeholder="输入关键词" aria-label="搜索关键词">
                <button type="submit" class="search-button">搜索</button>
            </form>
            {| end block top_bar_search_form |}

            <!-- Main Navigation -->
            <nav class="main-nav" role="navigation" aria-label="主导航">
                <!-- Desktop Navigation Buttons -->
                <div class="desktop-nav-buttons">
                    <button class="category-button btn btn-outline" aria-haspopup="true" aria-expanded="false">
                        <span class="category-button-icon" aria-hidden="true">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="3" y1="12" x2="21" y2="12"></line>
                                <line x1="3" y1="6" x2="21" y2="6"></line>
                                <line x1="3" y1="18" x2="21" y2="18"></line>
                            </svg>
                        </span>
                        分类
                    </button>

                    <button class="history-button btn btn-outline" aria-haspopup="true" aria-expanded="false">
                        <span class="history-button-icon" aria-hidden="true">&#128340;</span>
                        浏览历史
                    </button>

                    <button class="favorites-button btn btn-outline" aria-haspopup="true" aria-expanded="false">
                        <span class="favorites-button-icon" aria-hidden="true">&#9733;</span>
                        收藏夹
                    </button>
                </div>

                <!-- Admin Link -->
                {[ if ctx.com.user.is_admin then ]}
                <a href="{= ctx.com.urls.admin =}" class="nav-link">管理</a>
                {[ end ]}

                <!-- Authentication Links -->
                <div class="auth-links header-auth-links">
                    <a href="#" class="nav-link login-link" onclick="openLoginModal(); return false;">登录</a>
                    <span class="auth-separator">/</span>
                    <a href="#" class="nav-link register-link" onclick="openRegisterModal(); return false;">注册</a>
                </div>
                <div class="user-links">
                    <span class="user-display-name nav-link"></span>
                    <a href="#" class="nav-link logout-btn" onclick="confirmLogout(); return false;">登出</a>
                </div>
            </nav>
            {| end block top_bar |}
        </div>
    </header>

    <!-- Main Content Area -->
    <div class="main-container">
        <main class="main-content" id="main-content" role="main">
            {| block content |}{| end block content |}
        </main>
    </div>

    <!-- Footer -->
    <footer class="container" role="contentinfo">
        {| block footer |}{| end block footer |}
    </footer>

    <!-- Bottom Navigation for Mobile -->
    <nav class="bottom-nav" role="navigation" aria-label="移动端导航">
        <div class="bottom-nav-buttons">
            <a href="/" class="bottom-nav-btn {[ if ctx.com.urls.current == '/' then ]}active{[ end ]}" aria-label="首页">
                <div class="bottom-nav-btn-icon" aria-hidden="true">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                        <polyline points="9 22 9 12 15 12 15 22"></polyline>
                    </svg>
                </div>
                <span class="bottom-nav-btn-label">首页</span>
            </a>

            <button class="bottom-nav-btn" data-action="categories" aria-label="分类">
                <div class="bottom-nav-btn-icon" aria-hidden="true">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="3" y1="12" x2="21" y2="12"></line>
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <line x1="3" y1="18" x2="21" y2="18"></line>
                    </svg>
                </div>
                <span class="bottom-nav-btn-label">分类</span>
            </button>

            <button class="bottom-nav-btn" data-action="search" aria-label="搜索">
                <div class="bottom-nav-btn-icon" aria-hidden="true">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="11" cy="11" r="8"></circle>
                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                    </svg>
                </div>
                <span class="bottom-nav-btn-label">搜索</span>
            </button>

            <button class="bottom-nav-btn" data-action="history" aria-label="历史">
                <div class="bottom-nav-btn-icon" aria-hidden="true">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                </div>
                <span class="bottom-nav-btn-label">历史</span>
            </button>

            <button class="bottom-nav-btn" data-action="favorites" aria-label="收藏">
                <div class="bottom-nav-btn-icon" aria-hidden="true">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                    </svg>
                </div>
                <span class="bottom-nav-btn-label">收藏</span>
            </button>
        </div>
    </nav>

    <!-- Include dropdown content templates -->
    {| include("templates/dropdowns.html") |}

    <!-- Include confirmation dialogs -->
    {| include("templates/dialogs.html") |}

    <!-- Scripts -->
    {| block script |}{| end block script |}

    <!-- Core JavaScript - Load first -->
    <script src="/statics/console-production.js"></script>
    <script src="/statics/config/config.js"></script>
    <script src="/statics/core.js"></script>

    <!-- Navigation System -->
    <script src="/statics/navigation.js"></script>

    <!-- Feature Scripts - Load with defer for better performance -->
    <script src="/statics/direct-nav.js" defer></script>
    <script src="/statics/category-config.js" defer></script>
    <script src="/statics/optimized-history.js" defer></script>
    <script src="/statics/favorites.js" defer></script>
    <script src="/statics/deferred-firebase.js" defer></script>
    <script src="/statics/collapsible-header.js" defer></script>
    <script src="/statics/register-sw.js" defer></script>

    <!-- Performance optimizations -->
    <script src="/statics/performance-optimizations.js" defer></script>

    <!-- Initialize application -->
    <script>
        // Simple initialization
        document.addEventListener('DOMContentLoaded', function() {
            // Safe logging
            if (typeof Core !== 'undefined' && Core.Logger) {
                Core.Logger.log('Application initialized');
            } else {
                console.log('Application initialized');
            }

            // Initialize Firebase auth visibility
            const isFirebaseAuthEnabled = window.siteConfig?.auth?.enabled !== false;
            if (!isFirebaseAuthEnabled) {
                const authLinks = document.querySelectorAll('.auth-links');
                const userLinks = document.querySelectorAll('.user-links');
                authLinks.forEach(el => el.style.display = 'none');
                userLinks.forEach(el => el.style.display = 'none');
            }
        });
    </script>
</body>
</html>
