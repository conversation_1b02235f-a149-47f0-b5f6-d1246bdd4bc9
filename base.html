<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- Basic Meta Tags -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="theme-color" content="#111827">
    <meta name="description" content="VOD Guide - 您的在线观影指南">
    <meta name="keywords" content="VOD, 电影, 电视剧, 动漫, 综艺, 在线观看">
    <meta name="author" content="VOD Guide Team">

    <!-- Favicon -->
    <link rel="icon" href="/statics/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="/statics/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="/statics/apple-touch-icon.png">
    <link rel="manifest" href="/statics/manifest.json">

    <!-- Console suppression in production environment -->
    <script src="/statics/console-production.js"></script>
    
    <!-- Site configuration script - Load this first -->
    <script src="/statics/config/config.js"></script>
    
    <!-- Check if config is loaded -->
    <script>
      // Utility for conditional logging only in development
      const isDev = window.location.hostname === 'localhost' || 
                    window.location.hostname === '127.0.0.1' ||
                    window.location.hostname.includes('.local');
      const logInfo = isDev ? console.log : function() {};
      const logError = isDev ? console.error : function() {};
      
      // Ensure siteConfig is available
      if (!window.siteConfig) {
        logError('siteConfig is not defined, attempting to load default config');
        // Use fetch to load the config instead of hardcoding sensitive information
        fetch('/statics/config/config.js')
          .then(response => {
            if (!response.ok) {
              throw new Error('Failed to load config');
            }
            return response.text();
          })
          .then(text => {
            // Safely evaluate the config script
            const script = document.createElement('script');
            script.textContent = text;
            document.head.appendChild(script);
            logInfo('Default config loaded successfully');
          })
          .catch(error => {
            logError('Could not load config:', error);
          });
      } else {
        logInfo('siteConfig loaded successfully');
      }
    </script>

    <!-- Critical CSS - Inline for faster rendering -->
    <style>
        /* Critical CSS for initial render */
        @import url('/statics/critical.css');
        
        /* Modern UI components - Load asynchronously */
        @import url('/statics/modern-ui.css');

        /* Main container */
        .main-container {
            display: flex;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            position: relative;
        }

        .main-content {
            flex: 1;
        }

        /* Category button styles */
        .category-button {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            background-color: var(--bg-card, #1f2937);
            color: var(--text-primary, #f9fafb);
            border: 1px solid var(--border, #374151);
            border-radius: 0.5rem;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }

        .category-button:hover {
            background-color: var(--bg-card-hover, #2d3748);
            border-color: var(--accent, #f97316);
        }

        .category-button-icon {
            display: flex;
            align-items: center;
            margin-right: 0.5rem;
        }

        /* Categories dropdown */
        .categories-content {
            visibility: hidden;
            position: fixed;
            top: 4rem;
            right: auto;
            left: 50%;
            transform: translateX(-50%);
            margin: 0;
            padding: 1rem;
            border-radius: 0.5rem;
            background-color: var(--bg-dark, #111827);
            color: var(--text-primary, #f9fafb);
            width: 300px;
            max-width: 90%;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2), 0 0 0 1px var(--border, #374151);
            z-index: 100;
            opacity: 0;
            transition: visibility 0s linear 0.3s, opacity 0.3s;
        }

        .categories-content.show {
            visibility: visible;
            opacity: 1;
            transition: visibility 0s linear 0s, opacity 0.3s;
        }

        .categories-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border, #374151);
        }

        .categories-title {
            font-size: 1.2rem;
            margin: 0;
            font-weight: 600;
        }

        .categories-close {
            background: none;
            border: none;
            color: var(--text-tertiary, #9ca3af);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0;
            line-height: 1;
            transition: color 0.2s;
        }

        .categories-close:hover {
            color: var(--text-primary, #f9fafb);
        }

        .categories-body {
            max-height: 70vh;
            overflow-y: auto;
        }

        .categories-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .categories-list li {
            margin-bottom: 0.5rem;
        }

        .categories-list a {
            display: block;
            padding: 0.75rem 1rem;
            color: var(--text-secondary, #e5e7eb);
            text-decoration: none;
            border-radius: 0.5rem;
            transition: all 0.2s;
            font-weight: 500;
        }

        .categories-list a:hover {
            background-color: var(--bg-card-hover, #2d3748);
            color: var(--accent, #f97316);
            padding-left: 1.25rem;
        }

        /* Categories backdrop removed */

        /* Mobile styles */
        @media (max-width: 768px) {
            .categories-content {
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                width: 100%;
                max-width: 100%;
                height: 100vh;
                border-radius: 0;
                transform: translateY(100%);
                opacity: 1;
                transition: transform 0.3s;
            }

            .categories-content.show {
                transform: translateY(0);
            }

            .categories-body {
                max-height: calc(100vh - 4rem);
            }

            .categories-list a {
                padding: 1rem;
                font-size: 1.1rem;
            }
        }
    </style>
    {| block head_preload |}
    <!-- Preload critical resources with correct attributes -->
    <link rel="preload" href="https://s4.zstatic.net/ajax/libs/hls.js/1.5.18/hls.min.js" as="script">
    <!-- Remove DPlayer preload -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    {| end block head_preload |}
    <!-- Non-critical CSS loaded asynchronously -->
    <link rel="stylesheet" href="/statics/modern-ui.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/style.css" media="print" onload="this.media='all'">
    <!-- Non-critical CSS with media queries for conditional loading -->
    <!-- Expandable FAB, fixed header buttons, and mobile header buttons removed - using bottom navigation instead -->
    <link rel="stylesheet" href="/statics/shared-containers.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/history-item.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/history-tabs.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/skeleton-loader.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/favorites.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/mobile-search.css" media="(max-width: 768px)">
    <link rel="stylesheet" href="/statics/mobile-header.css" media="(max-width: 768px)">
    <link rel="stylesheet" href="/statics/mobile-view-fix.css" media="(max-width: 768px)">
    <link rel="stylesheet" href="/statics/bottom-nav.css" media="(max-width: 768px)">
    <link rel="stylesheet" href="/statics/direct-nav.css" media="(max-width: 768px)">
    <link rel="stylesheet" href="/statics/header-custom.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/auth.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/confirm-dialog.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/category-config.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="/statics/horizontal-vod-fix.css" media="print" onload="this.media='all'">
    <!-- Fallback for browsers that don't support onload on link -->
    <noscript>
        <link rel="stylesheet" href="/statics/modern-ui.css">
        <link rel="stylesheet" href="/statics/style.css">
        <link rel="stylesheet" href="/statics/shared-containers.css">
        <link rel="stylesheet" href="/statics/history-item.css">
        <link rel="stylesheet" href="/statics/history-tabs.css">
        <link rel="stylesheet" href="/statics/skeleton-loader.css">
        <link rel="stylesheet" href="/statics/favorites.css">
        <link rel="stylesheet" href="/statics/mobile-search.css">
        <link rel="stylesheet" href="/statics/mobile-header.css">
        <link rel="stylesheet" href="/statics/mobile-view-fix.css">
        <link rel="stylesheet" href="/statics/bottom-nav.css">
        <link rel="stylesheet" href="/statics/direct-nav.css">
        <link rel="stylesheet" href="/statics/header-custom.css">
        <link rel="stylesheet" href="/statics/auth.css">
        <link rel="stylesheet" href="/statics/confirm-dialog.css">
        <link rel="stylesheet" href="/statics/category-config.css">
        <link rel="stylesheet" href="/statics/horizontal-vod-fix.css">
    </noscript>
    {| block style |}{| end block style |}
</head>
<body{[ if ctx.com.urls.current == '/' then ]} class="index-page"{[ end ]}>
    <header class="site-header">
        <div class="container header-container">
            {| block top_bar |}
            <div class="site-logo">
                <a href="/" id="site-logo-link">{{ ctx.com.site.title }}</a>
            </div>

            {| block top_bar_search_form |}
            <form class="search-form" action="/vod/search/" method="GET">
                <select name="field" class="search-select">
                    <option value="title">搜标题</option>
                    <option value="tag">搜标签</option>
                    <option value="staff">搜人员</option>
                </select>
                <input name="kw" id="idx-search-input" class="search-input" placeholder="输入关键词" onfocus="this.select()">
                <button type="submit" class="search-button">搜索</button>
            </form>
            {| end block top_bar_search_form |}

            <div class="main-nav">
                <!-- Desktop history and favorites buttons -->
                <div class="desktop-nav-buttons">
                    <div class="category-dropdown">
                        <button class="category-button" onclick="toggleCategories()">
                            <span class="category-button-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <line x1="3" y1="12" x2="21" y2="12"></line>
                                    <line x1="3" y1="6" x2="21" y2="6"></line>
                                    <line x1="3" y1="18" x2="21" y2="18"></line>
                                </svg>
                            </span>
                            分类
                        </button>
                    </div>

                    <div class="history-dropdown">
                        <button class="history-button">
                            <span class="history-button-icon">&#128340;</span>
                            浏览历史
                        </button>
                    </div>

                    <div class="favorites-dropdown">
                        <button class="favorites-button">
                            <span class="favorites-button-icon">&#9733;</span>
                            收藏夹
                        </button>
                    </div>
                </div>

                <!-- Mobile header buttons have been removed - using bottom navigation instead -->

                {[ if ctx.com.user.is_admin then ]}
                <a href="{= ctx.com.urls.admin =}" class="nav-link">管理</a>
                {[ end ]}

                <!-- Firebase Auth Links - Only shown if Firebase auth is enabled in config -->
                <div class="auth-links header-auth-links" style="display: flex;">
                    <a href="#" class="nav-link login-link" onclick="openLoginModal(); return false;">登录</a>
                    <span class="auth-separator">/</span>
                    <a href="#" class="nav-link register-link" onclick="openRegisterModal(); return false;">注册</a>
                </div>
                <div class="user-links">
                    <span class="user-display-name nav-link"></span>
                    <a href="#" class="nav-link logout-btn" onclick="confirmLogout(); return false;">登出</a>
                </div>

                <script>
                    // Hide auth links if Firebase auth is disabled in config
                    document.addEventListener('DOMContentLoaded', function() {
                        const isFirebaseAuthEnabled = window.siteConfig && window.siteConfig.auth && window.siteConfig.auth.enabled !== false;
                        if (!isFirebaseAuthEnabled) {
                            // Hide auth links if Firebase auth is disabled
                            const authLinks = document.querySelectorAll('.auth-links');
                            const userLinks = document.querySelectorAll('.user-links');

                            authLinks.forEach(el => {
                                el.style.display = 'none';
                            });

                            userLinks.forEach(el => {
                                el.style.display = 'none';
                            });
                        }
                    });
                </script>

                <script>
                    // Make sign out function globally available
                    window.signOutUser = function() {
                        // Check if Firebase is loaded
                        if (window.firebaseAuth) {
                            // If Firebase is already loaded, use the implementation from deferred-firebase.js
                            console.log('Firebase is loaded, signing out user');
                            if (typeof window.logoutUser === 'function') {
                                window.logoutUser();
                            } else {
                                console.error('logoutUser function not available');
                                alert('登出失败，请刷新页面后重试。');
                            }
                        } else {
                            // If Firebase is not loaded, we need to load it first
                            console.log('Firebase not loaded, loading it now...');
                            if (typeof loadFirebase === 'function') {
                                loadFirebase(() => {
                                    // Once Firebase is loaded, try signing out again
                                    console.log('Firebase loaded, now signing out');
                                    if (typeof window.logoutUser === 'function') {
                                        window.logoutUser();
                                    } else {
                                        console.error('logoutUser function not available after loading Firebase');
                                        alert('登出失败，请刷新页面后重试。');
                                    }
                                });
                            } else {
                                console.error('loadFirebase function not available');
                                alert('登出失败，请刷新页面后重试。');
                            }
                        }
                    };

                    // Add confirmation before logout to prevent accidental touches
                    window.confirmLogout = function() {
                        // Check if we're on mobile
                        const isMobile = window.innerWidth <= 768;

                        if (isMobile) {
                            // On mobile, show our custom confirmation dialog
                            const confirmDialog = document.getElementById('logout-confirm-dialog');
                            confirmDialog.classList.add('active');
                            document.body.style.overflow = 'hidden'; // Prevent scrolling
                        } else {
                            // On desktop, just log out directly
                            signOutUser();
                        }
                    };

                    // Function to close the logout confirmation dialog
                    window.closeLogoutConfirm = function() {
                        const confirmDialog = document.getElementById('logout-confirm-dialog');
                        confirmDialog.classList.remove('active');
                        document.body.style.overflow = ''; // Re-enable scrolling
                    };

                    // Function to execute logout after confirmation
                    window.executeLogout = function() {
                        closeLogoutConfirm();
                        signOutUser();
                    };

                    // Clear History confirmation functions
                    let historyTypeToBeCleared = 'all'; // Default value

                    // Function to show clear history confirmation dialog
                    window.confirmClearHistory = function(type = 'all') {
                        // Store the type to be cleared
                        historyTypeToBeCleared = type;

                        // Show the confirmation dialog
                        const confirmDialog = document.getElementById('clear-history-confirm-dialog');
                        confirmDialog.classList.add('active');
                        document.body.style.overflow = 'hidden'; // Prevent scrolling
                    };

                    // Function to close the clear history confirmation dialog
                    window.closeClearHistoryConfirm = function() {
                        const confirmDialog = document.getElementById('clear-history-confirm-dialog');
                        confirmDialog.classList.remove('active');
                        document.body.style.overflow = ''; // Re-enable scrolling
                    };

                    // Function to execute clear history after confirmation
                    window.executeClearHistory = function() {
                        closeClearHistoryConfirm();
                        clearHistory(historyTypeToBeCleared);
                    };

                    // Clear Favorites confirmation functions
                    // Function to show clear favorites confirmation dialog
                    window.confirmClearFavorites = function() {
                        // Show the confirmation dialog
                        const confirmDialog = document.getElementById('clear-favorites-confirm-dialog');
                        confirmDialog.classList.add('active');
                        document.body.style.overflow = 'hidden'; // Prevent scrolling
                    };

                    // Function to close the clear favorites confirmation dialog
                    window.closeClearFavoritesConfirm = function() {
                        const confirmDialog = document.getElementById('clear-favorites-confirm-dialog');
                        confirmDialog.classList.remove('active');
                        document.body.style.overflow = ''; // Re-enable scrolling
                    };

                    // Function to execute clear favorites after confirmation
                    window.executeClearFavorites = function() {
                        closeClearFavoritesConfirm();
                        clearFavorites();
                    };

                    // Add event listener to close dialogs when clicking on backdrop
                    document.addEventListener('DOMContentLoaded', function() {
                        // Logout confirmation dialog
                        const logoutConfirmDialog = document.getElementById('logout-confirm-dialog');
                        if (logoutConfirmDialog) {
                            logoutConfirmDialog.addEventListener('click', function(e) {
                                // Only close if the click was directly on the backdrop, not on the dialog
                                if (e.target === logoutConfirmDialog) {
                                    closeLogoutConfirm();
                                }
                            });
                        }

                        // Clear history confirmation dialog
                        const clearHistoryConfirmDialog = document.getElementById('clear-history-confirm-dialog');
                        if (clearHistoryConfirmDialog) {
                            clearHistoryConfirmDialog.addEventListener('click', function(e) {
                                // Only close if the click was directly on the backdrop, not on the dialog
                                if (e.target === clearHistoryConfirmDialog) {
                                    closeClearHistoryConfirm();
                                }
                            });
                        }

                        // Clear favorites confirmation dialog
                        const clearFavoritesConfirmDialog = document.getElementById('clear-favorites-confirm-dialog');
                        if (clearFavoritesConfirmDialog) {
                            clearFavoritesConfirmDialog.addEventListener('click', function(e) {
                                // Only close if the click was directly on the backdrop, not on the dialog
                                if (e.target === clearFavoritesConfirmDialog) {
                                    closeClearFavoritesConfirm();
                                }
                            });
                        }
                    });
                </script>
            </div>
            {| end block top_bar |}
        </div>
    </header>

    <!-- Fixed header buttons have been removed - using bottom navigation instead -->

    <div class="main-container">
        <main class="main-content">
            {| block content |}{| end block content |}
        </main>
    </div>

    <footer class="container">
        {| block footer |}{| end block footer |}
    </footer>

    <!-- Bottom Navigation for Mobile -->
    <nav class="bottom-nav">
        <div class="bottom-nav-buttons">
            <!-- Home button -->
            <a href="/" class="bottom-nav-btn {[ if ctx.com.urls.current == '/' then ]}active{[ end ]}">
                <div class="bottom-nav-btn-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                        <polyline points="9 22 9 12 15 12 15 22"></polyline>
                    </svg>
                </div>
                <span class="bottom-nav-btn-label">首页</span>
            </a>

            <!-- Category button -->
            <button class="bottom-nav-btn" data-action="categories" onclick="directToggleCategories(); return false;">
                <div class="bottom-nav-btn-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="3" y1="12" x2="21" y2="12"></line>
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <line x1="3" y1="18" x2="21" y2="18"></line>
                    </svg>
                </div>
                <span class="bottom-nav-btn-label">分类</span>
            </button>

            <!-- Search button -->
            <button class="bottom-nav-btn" data-action="search" onclick="directOpenMobileSearch(); return false;">
                <div class="bottom-nav-btn-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="11" cy="11" r="8"></circle>
                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                    </svg>
                </div>
                <span class="bottom-nav-btn-label">搜索</span>
            </button>

            <!-- History button -->
            <button class="bottom-nav-btn" data-action="history" onclick="directToggleHistory(); return false;">
                <div class="bottom-nav-btn-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                </div>
                <span class="bottom-nav-btn-label">历史</span>
            </button>

            <!-- Favorites button -->
            <button class="bottom-nav-btn" data-action="favorites" onclick="directToggleFavorites(); return false;">
                <div class="bottom-nav-btn-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                    </svg>
                </div>
                <span class="bottom-nav-btn-label">收藏</span>
            </button>
        </div>
    </nav>

    <!-- Floating action button removed - using bottom navigation instead -->

    <!-- History Content -->
    <div class="history-content" id="history-dropdown">
        <div class="history-header">
            <h3 class="history-title">历史记录</h3>
            <div class="header-actions">
                <button class="refresh-data-button" onclick="refreshHistoryFromFirebase()" title="从云端刷新数据" style="background: none; border: none; color: #666; cursor: pointer; font-size: 18px; margin-right: 10px;">
                    🔄
                </button>
                <button class="clear-history-button" onclick="confirmClearHistory('all')">
                    清除
                </button>
                <span class="history-close" onclick="directCloseAll()">&times;</span>
            </div>
        </div>

        <!-- History Tabs -->
        <div class="history-tabs" role="tablist">
            <button class="history-tab active" data-tab="viewing" role="tab" aria-selected="true" aria-controls="viewing-history" id="viewing-tab">观看历史</button>
            <button class="history-tab" data-tab="search" role="tab" aria-selected="false" aria-controls="search-history" id="search-tab">搜索历史</button>
        </div>

        <!-- Viewing History Tab Content -->
        <div class="history-tab-content active" id="viewing-history" role="tabpanel" aria-labelledby="viewing-tab">
            <div class="history-body">
                <ul class="history-list viewing-list"></ul>
                <!-- Footer removed as clear button moved to header -->
            </div>
        </div>

        <!-- Search History Tab Content -->
        <div class="history-tab-content" id="search-history" role="tabpanel" aria-labelledby="search-tab">
            <div class="history-body">
                <ul class="history-list search-list"></ul>
                <!-- Footer removed as clear button moved to header -->
            </div>
        </div>

        <!-- No empty footer div needed anymore -->
    </div>

    <!-- History Backdrop -->
    <div id="history-backdrop" class="history-backdrop"></div>

    <!-- Favorites Content -->
    <div class="favorites-content" id="favorites-dropdown">
        <div class="favorites-header">
            <h3 class="favorites-title">收藏夹</h3>
            <div class="header-actions">
                <button class="refresh-data-button" onclick="refreshHistoryFromFirebase()" title="从云端刷新数据" style="background: none; border: none; color: #666; cursor: pointer; font-size: 18px; margin-right: 10px;">
                    🔄
                </button>
                <button class="clear-favorites-button" onclick="confirmClearFavorites()">
                    清除
                </button>
                <span class="favorites-close" onclick="directCloseAll()">&times;</span>
            </div>
        </div>

        <!-- Favorites Content Body -->
        <div class="favorites-body">
            <ul class="favorites-list"></ul>
            <!-- Footer removed as clear button moved to header -->
        </div>

        <!-- No empty footer div needed anymore -->
    </div>

    <!-- Favorites Backdrop -->
    <div id="favorites-backdrop" class="favorites-backdrop"></div>

    <!-- Categories Content -->
    <div class="categories-content" id="categories-dropdown">
        <div class="categories-header">
            <h3 class="categories-title">分类导航</h3>
            <span class="categories-close" onclick="directCloseAll()">&times;</span>
        </div>

        <div class="categories-body">
            <ul class="categories-list" id="categories-list">
                <!-- Categories will be populated from config.js -->
            </ul>
        </div>
    </div>

    <!-- Categories Backdrop -->
    <div id="categories-backdrop" class="categories-backdrop"></div>

    <!-- Mobile Search Overlay -->
    <div class="mobile-search-overlay">
        <div class="mobile-search-container">
            <div class="mobile-search-header">
                <h3>搜索</h3>
                <button class="mobile-search-close" onclick="directCloseAll()">&times;</button>
            </div>
            <div class="mobile-search-form">
                <form action="/vod/search/" method="GET">
                    <div class="mobile-search-input-group">
                        <input type="text" name="kw" class="mobile-search-input" placeholder="输入关键词" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false">
                        <button type="submit" class="mobile-search-submit">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
                        </button>
                    </div>
                    <div class="mobile-search-options">
                        <label class="mobile-search-option">
                            <input type="radio" name="field" value="title" checked>
                            <span>搜标题</span>
                        </label>
                        <label class="mobile-search-option">
                            <input type="radio" name="field" value="tag">
                            <span>搜标签</span>
                        </label>
                        <label class="mobile-search-option">
                            <input type="radio" name="field" value="staff">
                            <span>搜人员</span>
                        </label>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {| block script |}{| end block script |}

    <!-- Load utility library first -->
    <script src="/statics/utils.js"></script>
    <!-- Load performance optimizations early -->
    <script src="/statics/performance-optimizations.js"></script>
    <!-- Load deferred Firebase initialization -->
    <script src="/statics/deferred-firebase.js" defer></script>
    <!-- Load other scripts with defer to improve page load performance -->
    <script src="/statics/optimized-history.js" defer></script>
    <script src="/statics/collapsible-header.js" defer></script>
    <script src="/statics/favorites.js" defer></script>
    <!-- Register service worker for offline support and caching -->
    <script src="/statics/register-sw.js" defer></script>

    <!-- Modern Firebase loading indicator -->
    <div id="firebase-loading-indicator" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999; background-color: rgba(255, 255, 255, 0.95); border-radius: 8px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); padding: 20px; width: auto; max-width: 90%; text-align: center;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 12px;">
            <div style="width: 24px; height: 24px; border: 2px solid #f3f3f3; border-top: 2px solid #3498db; border-radius: 50%; animation: spin 0.8s linear infinite;"></div>
            <div style="font-size: 14px; color: #333;">正在加载...</div>
        </div>
    </div>

    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    </div>

    <!-- Load direct navigation functionality -->
    <script src="/statics/direct-nav.js"></script>

    <!-- Search scroll fix and popup position fix removed -->

    <!-- Simple history fix, desktop navigation fix, and dropdown reopen fix removed -->

    <!-- Simple initialization script -->
    <script>
        // Initialize direct navigation on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Use conditional logging for production
            const isDev = window.location.hostname === 'localhost' || 
                          window.location.hostname === '127.0.0.1' ||
                          window.location.hostname.includes('.local');
            const logInfo = isDev ? console.log : function() {};
            
            logInfo('Initializing direct navigation');

            // Set up direct navigation after a short delay
            setTimeout(function() {
                if (typeof setupDirectNavigation === 'function') {
                    setupDirectNavigation();
                }
                logInfo('Direct navigation initialized');
            }, 500);
            
            // Ensure all data has proper timestamps for conflict resolution
            setTimeout(function() {
                if (typeof Utils !== 'undefined' && typeof Utils.ensureDataTimestamps === 'function') {
                    logInfo('Ensuring all data has timestamps for proper sync');
                    Utils.ensureDataTimestamps();
                }
            }, 800);
            
            // Auto-sync data on app start (if user is logged in)
            setTimeout(function() {
                if (typeof Utils !== 'undefined' && typeof Utils.autoSyncOnAppStart === 'function') {
                    logInfo('Triggering auto-sync on app start');
                    Utils.autoSyncOnAppStart();
                } else {
                    logInfo('Auto-sync function not available yet, will try again');
                    // Try again after a short delay to ensure Utils is loaded
                    setTimeout(function() {
                        if (typeof Utils !== 'undefined' && typeof Utils.autoSyncOnAppStart === 'function') {
                            logInfo('Triggering delayed auto-sync on app start');
                            Utils.autoSyncOnAppStart();
                        } else {
                            logInfo('Auto-sync function still not available');
                        }
                    }, 2000);
                }
            }, 1000);
            
            // Setup app resume sync functionality
            setTimeout(function() {
                if (typeof Utils !== 'undefined' && typeof Utils.setupAppResumeSync === 'function') {
                    logInfo('Setting up app resume sync');
                    Utils.setupAppResumeSync();
                } else {
                    logInfo('App resume sync function not available yet, will try again');
                    // Try again after a short delay to ensure Utils is loaded
                    setTimeout(function() {
                        if (typeof Utils !== 'undefined' && typeof Utils.setupAppResumeSync === 'function') {
                            logInfo('Setting up delayed app resume sync');
                            Utils.setupAppResumeSync();
                        } else {
                            logInfo('App resume sync function still not available');
                        }
                    }, 2000);
                }
            }, 1500);
        });
        
        // Function to manually refresh data from Firebase
        function refreshHistoryFromFirebase() {
            // Use conditional logging for production
            const isDev = window.location.hostname === 'localhost' || 
                          window.location.hostname === '127.0.0.1' ||
                          window.location.hostname.includes('.local');
            const logInfo = isDev ? console.log : function() {};
            const logError = isDev ? console.error : function() {};
            
            logInfo('Manually refreshing data from Firebase');
            if (window.syncManager && typeof window.syncManager.forceRefreshFromFirestore === 'function') {
                const userId = window.getCurrentUserId ? window.getCurrentUserId() : null;
                if (userId) {
                    // Show feedback to user
                    const refreshBtn = document.querySelector('.refresh-data-button');
                    if (refreshBtn) {
                        const originalText = refreshBtn.innerHTML;
                        refreshBtn.innerHTML = '⏳';
                        refreshBtn.disabled = true;
                        
                        // Call the force refresh function
                        window.syncManager.forceRefreshFromFirestore(userId)
                            .then(() => {
                                logInfo('Manual refresh completed successfully');
                                refreshBtn.innerHTML = '✅';
                                
                                // Restore the original button after a delay
                                setTimeout(() => {
                                    refreshBtn.innerHTML = originalText;
                                    refreshBtn.disabled = false;
                                }, 1500);
                            })
                            .catch(error => {
                                logError('Error in manual refresh:', error);
                                refreshBtn.innerHTML = '❌';
                                
                                // Restore the original button after a delay
                                setTimeout(() => {
                                    refreshBtn.innerHTML = originalText;
                                    refreshBtn.disabled = false;
                                }, 1500);
                            });
                    } else {
                        window.syncManager.forceRefreshFromFirestore(userId);
                    }
                } else {
                    logError('Cannot refresh: No user is logged in');
                    alert('请先登录以刷新数据');
                }
            } else {
                logError('Cannot refresh: syncManager or forceRefreshFromFirestore is not available');
                alert('刷新功能不可用，请刷新页面重试');
            }
        }
    </script>

    <script>
        // History functions
        window.closeHistory = () => {
            const historyContent = document.getElementById('history-dropdown');
            const historyBackdrop = document.querySelector('.history-backdrop');
            const CLS = 'show';
            const isMobile = window.innerWidth <= 768;

            // Hide the history dropdown immediately
            historyContent.classList.remove(CLS);
            if (historyBackdrop) {
                historyBackdrop.classList.remove(CLS);
                historyBackdrop.style.display = 'none';
            }

            // Handle mobile-specific cleanup
            if (isMobile) {
                // No animation - immediately hide
                historyContent.style.transform = '';

                // Enable scrolling on mobile
                document.body.style.overflow = '';
                document.documentElement.style.overflow = '';
                document.body.style.position = '';
                document.body.style.width = '';
                document.body.style.height = '';
                document.body.style.top = '';

                // Restore scroll position if needed
                const scrollY = parseInt(document.body.getAttribute('data-scroll-position') || '0');
                window.scrollTo(0, scrollY);
            }

            // Remove any active click outside handler
            if (typeof removeClickOutsideHandler === 'function') {
                removeClickOutsideHandler();
            }

            // Immediately hide the dropdown without animation
            historyContent.style.display = 'none';
            historyContent.style.visibility = 'hidden';
            historyContent.style.zIndex = '-1';
            historyContent.style.pointerEvents = 'none';
            historyContent.style.opacity = '0';
            
            // Reset active state on bottom nav to home
            const homeBtn = document.querySelector('.bottom-nav-btn[href="/"]');
            if (homeBtn && window.innerWidth <= 768) {
                document.querySelectorAll('.bottom-nav-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                homeBtn.classList.add('active');
            }
        };

        // Favorites functions
        window.openFavorites = () => {
            const favoritesContent = document.getElementById('favorites-dropdown');
            const favoritesBackdrop = document.querySelector('.favorites-backdrop');
            const favoritesButton = document.querySelector('.favorites-button');
            const CLS = 'show';
            const isMobile = window.innerWidth <= 768;

            // Position the dropdown near the button on desktop
            if (!isMobile && favoritesButton) {
                const buttonRect = favoritesButton.getBoundingClientRect();
                favoritesContent.style.top = (buttonRect.bottom + 10) + 'px';
                favoritesContent.style.right = (window.innerWidth - buttonRect.right) + 'px';
                favoritesContent.style.left = '';
                favoritesContent.style.margin = '';
                favoritesContent.style.transform = '';
            }
            // Full screen for mobile (CSS handles most of the styling)
            else if (isMobile) {
                // Reset any inline styles that might interfere with our CSS
                favoritesContent.style.top = '';
                favoritesContent.style.right = '';
                favoritesContent.style.left = '';
                favoritesContent.style.margin = '';
                favoritesContent.style.width = '';
                favoritesContent.style.maxWidth = '';
                favoritesContent.style.maxHeight = '';
                favoritesContent.style.transform = 'translateY(100%)';
                favoritesContent.style.position = 'fixed';
            }

            // Make sure visibility and z-index are properly set
            favoritesContent.style.visibility = 'visible';
            favoritesContent.style.zIndex = '100'; // Match the CSS z-index
            favoritesContent.style.pointerEvents = 'auto'; // Ensure it captures clicks
            favoritesContent.style.overflow = 'hidden'; // Prevent the container from scrolling
            favoritesContent.style.overflowY = 'hidden'; // Explicitly prevent vertical scrolling

            // Show the dropdown with a small delay to allow the transform to take effect
            setTimeout(() => {
                // For mobile, reset transform to show the dropdown
                if (isMobile) {
                    favoritesContent.style.transform = 'translateY(0)';
                }

                favoritesContent.classList.add(CLS);
                if (favoritesBackdrop) {
                    favoritesBackdrop.classList.add(CLS);
                }
            }, 10);

            // Disable scrolling on mobile
            if (isMobile) {
                document.body.style.overflow = 'hidden';
            }

            // Close when clicking outside (desktop only)
            if (!isMobile) {
                // Remove any existing handler first
                removeClickOutsideHandler();

                setTimeout(() => {
                    activeClickOutsideHandler = (e) => {
                        if (!favoritesContent.contains(e.target) &&
                            !e.target.classList.contains('favorites-button')) {
                            closeFavorites();
                        }
                    };
                    document.addEventListener('click', activeClickOutsideHandler);
                }, 10);
            }

            // Load favorites items
            if (typeof window.loadFavoritesItems === 'function') {
                window.loadFavoritesItems();
            }
        };

        window.closeFavorites = function() {
            const favoritesContent = document.getElementById('favorites-dropdown');
            const favoritesBackdrop = document.querySelector('.favorites-backdrop');
            const CLS = 'show';
            const isMobile = window.innerWidth <= 768;

            // Hide the favorites dropdown
            if (favoritesContent) {
                favoritesContent.classList.remove(CLS);
            }
            if (favoritesBackdrop) {
                favoritesBackdrop.classList.remove(CLS);
            }

            // Enable scrolling on mobile
            if (isMobile) {
                document.body.style.overflow = '';
                
                // Add a small delay before resetting the transform to ensure smooth transition
                setTimeout(() => {
                    if (historyContent) {
                        historyContent.style.transform = '';
                    }
                }, 300);
            }

            // Make sure the dropdown is not capturing any events
            favoritesContent.style.visibility = 'hidden';
            favoritesContent.style.pointerEvents = 'none';

            // Reset z-index to ensure it doesn't block other elements
            favoritesContent.style.zIndex = '-1';

            // After a short delay, reset the visibility to the default state
            setTimeout(() => {
                favoritesContent.style.visibility = '';
                favoritesContent.style.zIndex = '';
                favoritesContent.style.pointerEvents = '';
            }, 100);

            // Remove any active click outside handler
            if (typeof removeClickOutsideHandler === 'function') {
                removeClickOutsideHandler();
            }
        };

        window.toggleFavorites = () => {
            const favoritesContent = document.getElementById('favorites-dropdown');
            const CLS = 'show';
            const isMobile = window.innerWidth <= 768;

            // Always use direct navigation on mobile to avoid conflicts
            if (isMobile) {
                if (typeof window.directToggleFavorites === 'function') {
                    window.directToggleFavorites();
                }
                return;
            }

            // For desktop only - toggle the visibility
            if (favoritesContent.classList.contains(CLS)) {
                closeFavorites();

                // Reset active state on bottom nav
                if (window.innerWidth <= 768) {
                    // Find the home button and make it active
                    const homeBtn = document.querySelector('.bottom-nav-btn[href="/"]');
                    if (homeBtn) {
                        document.querySelectorAll('.bottom-nav-btn').forEach(btn => {
                            btn.classList.remove('active');
                        });
                        homeBtn.classList.add('active');
                    }
                }
            } else {
                // Reset any styles that might prevent the dropdown from showing
                favoritesContent.style.display = '';
                favoritesContent.style.visibility = '';
                favoritesContent.style.opacity = '';
                favoritesContent.style.zIndex = '';
                favoritesContent.style.pointerEvents = '';

                openFavorites();

                // Set active state for bottom nav button
                if (window.innerWidth <= 768) {
                    // Remove active class from all buttons
                    document.querySelectorAll('.bottom-nav-btn').forEach(btn => {
                        btn.classList.remove('active');
                    });

                    // Add active class to favorites button
                    const favoritesBtn = document.querySelector('.bottom-nav-btn[data-action="favorites"]');
                    if (favoritesBtn) {
                        favoritesBtn.classList.add('active');
                    }
                }
            }
        };

        window.openHistory = () => {
            const historyContent = document.getElementById('history-dropdown');
            const historyBackdrop = document.querySelector('.history-backdrop');
            const historyButton = document.querySelector('.history-button');
            const CLS = 'show';
            const isMobile = window.innerWidth <= 768;

            // Position the dropdown near the button on desktop
            if (!isMobile && historyButton) {
                const buttonRect = historyButton.getBoundingClientRect();
                historyContent.style.top = (buttonRect.bottom + 10) + 'px';
                historyContent.style.right = (window.innerWidth - buttonRect.right) + 'px';
                historyContent.style.left = '';
                historyContent.style.margin = '';
                historyContent.style.transform = '';
            }
            // Full screen for mobile (CSS handles most of the styling)
            else if (isMobile) {
                // Reset any inline styles that might interfere with our CSS
                historyContent.style.top = '';
                historyContent.style.right = '';
                historyContent.style.left = '';
                historyContent.style.margin = '';
                historyContent.style.width = '';
                historyContent.style.maxWidth = '';
                historyContent.style.maxHeight = '';
                historyContent.style.transform = 'translateY(100%)';
                historyContent.style.position = 'fixed';

                // FAB code removed - using fixed header buttons instead
            }

            // Make sure visibility and z-index are properly set
            historyContent.style.visibility = 'visible';
            historyContent.style.zIndex = '100'; // Match the CSS z-index
            historyContent.style.pointerEvents = 'auto'; // Ensure it captures clicks
            historyContent.style.overflow = 'hidden'; // Prevent the container from scrolling
            historyContent.style.overflowY = 'hidden'; // Explicitly prevent vertical scrolling

            // Show the dropdown with a small delay to allow the transform to take effect
            setTimeout(() => {
                // For mobile, reset transform to show the dropdown
                if (isMobile) {
                    historyContent.style.transform = 'translateY(0)';
                }

                historyContent.classList.add(CLS);
                if (historyBackdrop) {
                    historyBackdrop.classList.add(CLS);
                }
            }, 10);

            // Disable scrolling on mobile
            if (isMobile) {
                document.body.style.overflow = 'hidden';
            }

            // Close when clicking outside (desktop only)
            if (!isMobile) {
                // Remove any existing handler first
                removeClickOutsideHandler();

                setTimeout(() => {
                    activeClickOutsideHandler = (e) => {
                        if (!historyContent.contains(e.target) &&
                            !e.target.classList.contains('history-button')) {
                            closeHistory();
                        }
                    };
                    document.addEventListener('click', activeClickOutsideHandler);
                }, 10);
            }
        };

        // toggleHistory function is now defined in optimized-history.js

        // Store reference to the click event handler
        let activeClickOutsideHandler = null;

        // Function to remove click outside handler
        window.removeClickOutsideHandler = () => {
            if (activeClickOutsideHandler) {
                document.removeEventListener('click', activeClickOutsideHandler);
                activeClickOutsideHandler = null;
            }
        };

        // Mobile Search Functions
        const openMobileSearch = () => {
            const searchOverlay = document.querySelector('.mobile-search-overlay');
            if (searchOverlay) {
                // Make sure visibility and z-index are properly set
                searchOverlay.style.visibility = 'visible';
                searchOverlay.style.zIndex = '1000'; // High z-index
                searchOverlay.style.pointerEvents = 'auto'; // Ensure it captures clicks

                // Adjust container for small screens
                const searchContainer = searchOverlay.querySelector('.mobile-search-container');
                const isMobile = window.innerWidth <= 768;
                const isSmallMobile = window.innerWidth <= 480;

                if (searchContainer) {
                    if (isSmallMobile) {
                        // Full width for small screens
                        searchContainer.style.width = '100%';
                        searchContainer.style.maxWidth = '100%';
                        searchContainer.style.borderRadius = '0';
                        searchContainer.style.height = '100%';
                        searchContainer.style.margin = '0';

                        // Adjust the overlay position
                        searchOverlay.style.alignItems = 'flex-start';
                    } else if (isMobile) {
                        // Slightly smaller for tablets
                        searchContainer.style.width = '95%';
                        searchContainer.style.maxWidth = '500px';
                        searchContainer.style.borderRadius = 'var(--radius-lg)';
                        searchContainer.style.margin = 'auto';

                        // Center the overlay
                        searchOverlay.style.alignItems = 'center';
                    }

                    // Ensure the form and input group are properly sized
                    const searchForm = searchContainer.querySelector('.mobile-search-form form');
                    const inputGroup = searchContainer.querySelector('.mobile-search-input-group');
                    const searchInput = searchContainer.querySelector('.mobile-search-input');

                    if (searchForm) searchForm.style.width = '100%';
                    if (inputGroup) inputGroup.style.width = '100%';
                    if (searchInput) {
                        searchInput.style.width = '100%';
                        searchInput.style.boxSizing = 'border-box';
                    }
                }

                // Show the search overlay
                searchOverlay.classList.add('active');
                document.body.style.overflow = 'hidden';

                // Set active state for bottom nav button
                if (isMobile) {
                    // Remove active class from all buttons
                    document.querySelectorAll('.bottom-nav-btn').forEach(btn => {
                        btn.classList.remove('active');
                    });

                    // Add active class to search button
                    const searchBtn = document.querySelector('.bottom-nav-btn[data-action="search"]');
                    if (searchBtn) {
                        searchBtn.classList.add('active');
                    }
                }

                // Focus on the search input
                setTimeout(() => {
                    const searchInput = document.querySelector('.mobile-search-input');
                    if (searchInput) {
                        searchInput.focus();
                    }
                }, 300);

                console.log('Mobile search opened');
            }
        };

        const closeMobileSearch = () => {
            const searchOverlay = document.querySelector('.mobile-search-overlay');
            if (searchOverlay) {
                searchOverlay.classList.remove('active');
                document.body.style.overflow = '';
                document.documentElement.style.overflow = '';
                document.body.style.position = '';
                document.body.style.width = '';
                document.body.style.height = '';
                document.body.style.top = '';

                // Restore scroll position if needed
                const scrollY = parseInt(document.body.getAttribute('data-scroll-position') || '0');
                window.scrollTo(0, scrollY);

                // Reset visibility and z-index to ensure it doesn't block other elements
                searchOverlay.style.visibility = 'hidden';
                searchOverlay.style.zIndex = '-1';
                searchOverlay.style.pointerEvents = 'none';
                searchOverlay.style.display = 'none'; // Ensure it's completely hidden

                // Reset container styles
                const searchContainer = searchOverlay.querySelector('.mobile-search-container');
                if (searchContainer) {
                    // Reset container styles
                    searchContainer.style.width = '';
                    searchContainer.style.maxWidth = '';
                    searchContainer.style.borderRadius = '';
                    searchContainer.style.height = '';
                    searchContainer.style.margin = '';

                    // Reset form and input styles
                    const searchForm = searchContainer.querySelector('.mobile-search-form form');
                    const inputGroup = searchContainer.querySelector('.mobile-search-input-group');
                    const searchInput = searchContainer.querySelector('.mobile-search-input');

                    if (searchForm) searchForm.style.width = '';
                    if (inputGroup) inputGroup.style.width = '';
                    if (searchInput) {
                        searchInput.style.width = '';
                        searchInput.style.boxSizing = '';
                        searchInput.blur(); // Remove focus from the input
                    }
                }

                // Reset active state on bottom nav
                if (window.innerWidth <= 768) {
                    // Find the home button and make it active
                    const homeBtn = document.querySelector('.bottom-nav-btn[href="/"]');
                    if (homeBtn) {
                        document.querySelectorAll('.bottom-nav-btn').forEach(btn => {
                            btn.classList.remove('active');
                        });
                        homeBtn.classList.add('active');
                    }
                }

                console.log('Mobile search closed');
            }
        };

        // Collapse history dropdown on scroll (desktop only)
        window.addEventListener('scroll', () => {
            if (window.innerWidth > 768) {
                const historyContent = document.getElementById('history-dropdown');
                if (historyContent && historyContent.classList.contains('show')) {
                    closeHistory();
                }
            }
        });

        // Handle escape key to close history
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const historyContent = document.getElementById('history-dropdown');
                if (historyContent && historyContent.classList.contains('show')) {
                    closeHistory();
                }
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            const historyContent = document.getElementById('history-dropdown');
            const searchOverlay = document.querySelector('.mobile-search-overlay');
            const isMobile = window.innerWidth <= 768;
            const isSmallMobile = window.innerWidth <= 480;

            // Handle history dropdown resize
            if (historyContent && historyContent.classList.contains('show')) {
                // Close and reopen to apply correct positioning
                closeHistory();

                // Small delay to ensure close completes
                setTimeout(() => {
                    openHistory();
                }, 50);
            }

            // Handle search overlay resize
            if (searchOverlay && searchOverlay.classList.contains('active')) {
                // Adjust container for small screens
                const searchContainer = searchOverlay.querySelector('.mobile-search-container');

                if (searchContainer) {
                    if (isSmallMobile) {
                        // Full width for small screens
                        searchContainer.style.width = '100%';
                        searchContainer.style.maxWidth = '100%';
                        searchContainer.style.borderRadius = '0';
                        searchContainer.style.height = '100%';
                        searchContainer.style.margin = '0';

                        // Adjust the overlay position
                        searchOverlay.style.alignItems = 'flex-start';
                    } else if (isMobile) {
                        // Slightly smaller for tablets
                        searchContainer.style.width = '95%';
                        searchContainer.style.maxWidth = '500px';
                        searchContainer.style.borderRadius = 'var(--radius-lg)';
                        searchContainer.style.margin = 'auto';

                        // Center the overlay
                        searchOverlay.style.alignItems = 'center';
                    }

                    // Ensure the form and input group are properly sized
                    const searchForm = searchContainer.querySelector('.mobile-search-form form');
                    const inputGroup = searchContainer.querySelector('.mobile-search-input-group');
                    const searchInput = searchContainer.querySelector('.mobile-search-input');

                    if (searchForm) searchForm.style.width = '100%';
                    if (inputGroup) inputGroup.style.width = '100%';
                    if (searchInput) {
                        searchInput.style.width = '100%';
                        searchInput.style.boxSizing = 'border-box';
                    }
                }
            }
        });

        // Function to remove a single history item
        // Make it globally accessible for real-time updates
        window.removeHistoryItem = (id) => {
            let hist = JSON.parse(localStorage.getItem('vodg-vod-history')) || [];

            // Find and remove the item with the matching id
            hist = hist.filter(item => item && item.id !== id);

            // Save the updated history
            localStorage.setItem('vodg-vod-history', JSON.stringify(hist));

            // If user is logged in, sync directly to Firebase
            if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
                const userId = window.getCurrentUserId();
                if (userId && window.syncManager) {
                    // Determine if this is a viewing history or search history item
                    if (id.startsWith('search-')) {
                        if (typeof window.syncManager.forceSearchHistorySync === 'function') {
                            window.syncManager.forceSearchHistorySync(userId);
                        } else {
                            window.syncManager.syncToFirestore(userId, 'searchHistory');
                        }
                    } else {
                        if (typeof window.syncManager.forceViewingHistorySync === 'function') {
                            window.syncManager.forceViewingHistorySync(userId);
                        } else {
                            window.syncManager.syncToFirestore(userId, 'viewingHistory');
                        }
                    }
                }
            } else {
                // If not logged in, just dispatch the event
                if (id.startsWith('search-')) {
                    window.dispatchEvent(new CustomEvent('searchHistoryUpdated'));
                } else {
                    window.dispatchEvent(new CustomEvent('viewingHistoryUpdated'));
                }
            }

            // Refresh the history list display
            loadHistoryItems();

            // Show feedback message (optional)
            const feedbackEl = document.createElement('div');
            feedbackEl.className = 'history-feedback';
            feedbackEl.textContent = '已删除';
            feedbackEl.style.position = 'absolute';
            feedbackEl.style.bottom = '10px';
            feedbackEl.style.left = '50%';
            feedbackEl.style.transform = 'translateX(-50%)';
            feedbackEl.style.backgroundColor = 'var(--secondary)';
            feedbackEl.style.color = 'white';
            feedbackEl.style.padding = '5px 10px';
            feedbackEl.style.borderRadius = '4px';
            feedbackEl.style.fontSize = 'var(--font-size-sm)';
            feedbackEl.style.opacity = '0';
            feedbackEl.style.transition = 'opacity 0.3s ease';

            const historyContent = document.getElementById('history-dropdown');
            historyContent.appendChild(feedbackEl);

            // Show and then hide the feedback
            setTimeout(() => {
                feedbackEl.style.opacity = '1';
                setTimeout(() => {
                    feedbackEl.style.opacity = '0';
                    setTimeout(() => {
                        historyContent.removeChild(feedbackEl);
                    }, 300);
                }, 1500);
            }, 10);
        };

        // Function to clear all history or specific type
        // Make it globally accessible for real-time updates
        window.clearHistory = (type = 'all') => {
            let hist = JSON.parse(localStorage.getItem('vodg-vod-history')) || [];

            if (type === 'all') {
                // Clear all history
                localStorage.removeItem('vodg-vod-history');
                
                // If user is logged in, sync directly to Firebase
                if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
                    const userId = window.getCurrentUserId();
                    if (userId && window.syncManager) {
                        if (typeof window.syncManager.forceViewingHistorySync === 'function') {
                            window.syncManager.forceViewingHistorySync(userId);
                        } else {
                            window.syncManager.syncToFirestore(userId, 'viewingHistory');
                        }

                        if (typeof window.syncManager.forceSearchHistorySync === 'function') {
                            window.syncManager.forceSearchHistorySync(userId);
                        } else {
                            window.syncManager.syncToFirestore(userId, 'searchHistory');
                        }
                    }
                } else {
                    // If not logged in, just dispatch the events
                    window.dispatchEvent(new CustomEvent('viewingHistoryUpdated'));
                    window.dispatchEvent(new CustomEvent('searchHistoryUpdated'));
                }
            } else if (type === 'viewing') {
                // Keep only search history
                hist = hist.filter(item => item && item.id && item.id.startsWith('search-'));
                localStorage.setItem('vodg-vod-history', JSON.stringify(hist));
                
                // If user is logged in, sync directly to Firebase
                if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
                    const userId = window.getCurrentUserId();
                    if (userId && window.syncManager) {
                        if (typeof window.syncManager.forceViewingHistorySync === 'function') {
                            window.syncManager.forceViewingHistorySync(userId);
                        } else {
                            window.syncManager.syncToFirestore(userId, 'viewingHistory');
                        }
                    }
                } else {
                    // If not logged in, just dispatch the event
                    window.dispatchEvent(new CustomEvent('viewingHistoryUpdated'));
                }
            } else if (type === 'search') {
                // Keep only viewing history
                hist = hist.filter(item => item && item.id && !item.id.startsWith('search-'));
                localStorage.setItem('vodg-vod-history', JSON.stringify(hist));
                
                // If user is logged in, sync directly to Firebase
                if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
                    const userId = window.getCurrentUserId();
                    if (userId && window.syncManager) {
                        if (typeof window.syncManager.forceSearchHistorySync === 'function') {
                            window.syncManager.forceSearchHistorySync(userId);
                        } else {
                            window.syncManager.syncToFirestore(userId, 'searchHistory');
                        }
                    }
                } else {
                    // If not logged in, just dispatch the event
                    window.dispatchEvent(new CustomEvent('searchHistoryUpdated'));
                }
            }

            // Refresh the history list display
            loadHistoryItems();

            // Show feedback message
            const clearButton = document.querySelector('.clear-history-button');
            if (clearButton) {
                const originalHTML = clearButton.innerHTML;
                clearButton.innerHTML = '<span>✓ 已清除</span>';
                clearButton.style.backgroundColor = 'var(--secondary)';
                clearButton.disabled = true;

                // Reset button after 2 seconds
                setTimeout(() => {
                    clearButton.innerHTML = originalHTML;
                    clearButton.style.backgroundColor = '';
                    clearButton.disabled = false;
                }, 2000);
            }
        };

        // Load history items on page load
        loadHistoryItems();

        // Favorites functionality - Make functions globally available
        window.closeFavorites = function() {
            const favoritesContent = document.getElementById('favorites-dropdown');
            const favoritesBackdrop = document.querySelector('.favorites-backdrop');
            const CLS = 'show';
            const isMobile = window.innerWidth <= 768;

            // Hide the favorites dropdown
            if (favoritesContent) {
                favoritesContent.classList.remove(CLS);
            }
            if (favoritesBackdrop) {
                favoritesBackdrop.classList.remove(CLS);
            }

            // Enable scrolling on mobile
            if (isMobile) {
                document.body.style.overflow = '';
            }

            // Make sure the dropdown is not capturing any events
            favoritesContent.style.visibility = 'hidden';
            favoritesContent.style.pointerEvents = 'none';

            // Reset z-index to ensure it doesn't block other elements
            favoritesContent.style.zIndex = '-1';

            // After a short delay, reset the visibility to the default state
            setTimeout(() => {
                favoritesContent.style.visibility = '';
                favoritesContent.style.zIndex = '';
                favoritesContent.style.pointerEvents = '';
            }, 100);

            console.log('Favorites closed');

            // After the favorites panel is closed, ensure both dropdowns are reset properly
            setTimeout(() => {
                if (favoritesContent) {
                    favoritesContent.style.transform = '';
                    favoritesContent.style.display = 'none';
                }
            }, 500);
        };

        window.openFavorites = function() {
            const favoritesContent = document.getElementById('favorites-dropdown');
            const favoritesBackdrop = document.querySelector('.favorites-backdrop');
            const favoritesButton = document.querySelector('.favorites-button');
            const CLS = 'show';
            const isMobile = window.innerWidth <= 768;

            // Position the dropdown near the button on desktop
            if (!isMobile && favoritesButton) {
                const buttonRect = favoritesButton.getBoundingClientRect();
                favoritesContent.style.top = (buttonRect.bottom + 10) + 'px';
                favoritesContent.style.right = (window.innerWidth - buttonRect.right) + 'px';
                favoritesContent.style.left = '';
                favoritesContent.style.margin = '';
                favoritesContent.style.transform = '';
            }
            // Full screen for mobile (CSS handles most of the styling)
            else if (isMobile) {
                // Reset any inline styles that might interfere with our CSS
                favoritesContent.style.top = '';
                favoritesContent.style.right = '';
                favoritesContent.style.left = '';
                favoritesContent.style.margin = '';
                favoritesContent.style.width = '';
                favoritesContent.style.maxWidth = '';
                favoritesContent.style.maxHeight = '';
                favoritesContent.style.transform = 'translateY(100%)';
                favoritesContent.style.position = 'fixed';
            }

            // Make sure visibility and z-index are properly set
            favoritesContent.style.visibility = 'visible';
            favoritesContent.style.zIndex = '100'; // Match the CSS z-index
            favoritesContent.style.pointerEvents = 'auto'; // Ensure it captures clicks
            favoritesContent.style.overflow = 'hidden'; // Prevent the container from scrolling
            favoritesContent.style.overflowY = 'hidden'; // Explicitly prevent vertical scrolling

            // Show the dropdown with a small delay to allow the transform to take effect
            setTimeout(() => {
                // For mobile, reset transform to show the dropdown
                if (isMobile) {
                    favoritesContent.style.transform = 'translateY(0)';
                }

                favoritesContent.classList.add(CLS);
                if (favoritesBackdrop) {
                    favoritesBackdrop.classList.add(CLS);
                }
            }, 10);

            // Disable scrolling on mobile
            if (isMobile) {
                document.body.style.overflow = 'hidden';
            }

            // Load favorites items
            if (typeof loadFavoritesItems === 'function') {
                loadFavoritesItems();
            }

            // Close when clicking outside (desktop only)
            if (!isMobile) {
                // Remove any existing handler first
                removeClickOutsideHandler();

                setTimeout(() => {
                    activeClickOutsideHandler = (e) => {
                        if (!favoritesContent.contains(e.target) &&
                            !e.target.classList.contains('favorites-button')) {
                            closeFavorites();
                        }
                    };
                    document.addEventListener('click', activeClickOutsideHandler);
                }, 10);
            }
        };

        // Make toggleFavorites globally available
        window.toggleFavorites = function() {
            const favoritesContent = document.getElementById('favorites-dropdown');
            const CLS = 'show';

            // Toggle the visibility
            if (favoritesContent.classList.contains(CLS)) {
                closeFavorites();

                // Reset active state on bottom nav
                if (window.innerWidth <= 768) {
                    // Find the home button and make it active
                    const homeBtn = document.querySelector('.bottom-nav-btn[href="/"]');
                    if (homeBtn) {
                        document.querySelectorAll('.bottom-nav-btn').forEach(btn => {
                            btn.classList.remove('active');
                        });
                        homeBtn.classList.add('active');
                    }
                }
            } else {
                openFavorites();

                // Set active state for bottom nav button
                if (window.innerWidth <= 768) {
                    // Remove active class from all buttons
                    document.querySelectorAll('.bottom-nav-btn').forEach(btn => {
                        btn.classList.remove('active');
                    });

                    // Add active class to favorites button
                    const favoritesBtn = document.querySelector('.bottom-nav-btn[onclick*="toggleFavorites"]');
                    if (favoritesBtn) {
                        favoritesBtn.classList.add('active');
                    }
                }


            }
        };

        // Handle escape key to close favorites
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const favoritesContent = document.getElementById('favorites-dropdown');
                if (favoritesContent && favoritesContent.classList.contains('show')) {
                    closeFavorites();
                }
            }
        });

        // Handle window resize for favorites
        window.addEventListener('resize', () => {
            const favoritesContent = document.getElementById('favorites-dropdown');
            const isMobile = window.innerWidth <= 768;

            if (favoritesContent && favoritesContent.classList.contains('show')) {
                // Close and reopen to apply correct positioning
                closeFavorites();

                // Small delay to ensure close completes
                setTimeout(() => {
                    openFavorites();
                }, 50);
            }
        });

        // Collapse favorites dropdown on scroll (desktop only)
        window.addEventListener('scroll', () => {
            if (window.innerWidth > 768) {
                const favoritesContent = document.getElementById('favorites-dropdown');
                if (favoritesContent && favoritesContent.classList.contains('show')) {
                    closeFavorites();
                }
            }
        });
    </script>

    <!-- Sidebar handling script -->
    <script>

        // Categories dropdown functionality - Make functions globally available
        // Function to open categories dropdown
        window.openCategories = function() {
            const categoriesDropdown = document.getElementById('categories-dropdown');
            const categoriesBackdrop = document.getElementById('categories-backdrop');
            const isMobile = window.innerWidth <= 768;

            if (categoriesDropdown) {
                // Make sure visibility and z-index are properly set
                categoriesDropdown.style.visibility = 'visible';
                categoriesDropdown.style.zIndex = '1000'; // High z-index
                categoriesDropdown.style.pointerEvents = 'auto'; // Ensure it captures clicks
                categoriesDropdown.style.display = 'block';

                // Desktop-specific styling
                if (!isMobile) {
                    categoriesDropdown.style.position = 'fixed';
                    categoriesDropdown.style.maxHeight = '80vh';
                    categoriesDropdown.style.overflowY = 'auto';
                    categoriesDropdown.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2), 0 0 0 1px #374151';
                    categoriesDropdown.style.border = '1px solid #374151';
                    categoriesDropdown.style.borderRadius = '8px';
                    categoriesDropdown.style.backgroundColor = 'var(--bg-dark, #111827)';

                    // Only prevent scrolling on mobile
                    document.body.style.overflow = '';
                } else {
                    // Mobile-specific styling
                    document.body.style.overflow = 'hidden'; // Prevent scrolling on mobile
                }

                // Show the dropdown
                categoriesDropdown.classList.add('show');
                if (categoriesBackdrop) {
                    categoriesBackdrop.classList.add('show');
                }

                console.log('Categories dropdown opened');
            }
        }

        // Function to close categories dropdown
        window.closeCategories = function() {
            const categoriesDropdown = document.getElementById('categories-dropdown');
            const categoriesBackdrop = document.getElementById('categories-backdrop');
            const isMobile = window.innerWidth <= 768;

            if (categoriesDropdown) {
                categoriesDropdown.classList.remove('show');
                if (categoriesBackdrop) {
                    categoriesBackdrop.classList.remove('show');
                }

                // Always restore scrolling
                document.body.style.overflow = '';

                // Reset visibility and z-index to ensure it doesn't block other elements
                categoriesDropdown.style.visibility = 'hidden';
                categoriesDropdown.style.zIndex = '-1';

                // Desktop-specific cleanup
                if (!isMobile) {
                    // Reset desktop-specific styles
                    categoriesDropdown.style.boxShadow = '';
                    categoriesDropdown.style.border = '';
                    categoriesDropdown.style.borderRadius = '';
                    categoriesDropdown.style.backgroundColor = '';
                }

                // After a short delay, reset the visibility to the default state
                setTimeout(() => {
                    categoriesDropdown.style.visibility = '';
                    categoriesDropdown.style.zIndex = '';
                    categoriesDropdown.style.display = '';

                    // For desktop, ensure transform is reset
                    if (!isMobile) {
                        categoriesDropdown.style.transform = '';
                        categoriesDropdown.style.position = '';
                    }
                }, 100);

                console.log('Categories dropdown closed');
            }
        }

        // Toggle categories dropdown
        window.toggleCategories = function() {
            const categoriesDropdown = document.getElementById('categories-dropdown');
            if (!categoriesDropdown) return;

            const isMobile = window.innerWidth <= 768;

            // For desktop, position the dropdown correctly
            if (!isMobile) {
                const categoryButton = document.querySelector('.category-button');
                if (categoryButton) {
                    const buttonRect = categoryButton.getBoundingClientRect();
                    categoriesDropdown.style.top = (buttonRect.bottom + 10) + 'px';
                    categoriesDropdown.style.left = buttonRect.left + 'px';
                    categoriesDropdown.style.transform = 'none';
                    categoriesDropdown.style.position = 'fixed';
                    categoriesDropdown.style.maxHeight = '80vh';
                    categoriesDropdown.style.overflowY = 'auto';
                    categoriesDropdown.style.backgroundColor = 'var(--bg-dark, #111827)';
                    categoriesDropdown.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2), 0 0 0 1px #374151';
                    categoriesDropdown.style.border = '1px solid #374151';
                    categoriesDropdown.style.borderRadius = '8px';
                }
            }

            if (categoriesDropdown.classList.contains('show')) {
                closeCategories();

                // Reset active state on bottom nav
                if (isMobile) {
                    // Find the home button and make it active
                    const homeBtn = document.querySelector('.bottom-nav-btn[href="/"]');
                    if (homeBtn) {
                        document.querySelectorAll('.bottom-nav-btn').forEach(btn => {
                            btn.classList.remove('active');
                        });
                        homeBtn.classList.add('active');
                    }
                }
            } else {
                // Close other dropdowns if open
                if (typeof closeHistory === 'function') closeHistory();
                if (typeof closeFavorites === 'function') closeFavorites();
                if (typeof closeMobileSearch === 'function') closeMobileSearch();

                // Reset any properties that might prevent the dropdown from showing
                categoriesDropdown.style.display = '';
                categoriesDropdown.style.visibility = '';
                categoriesDropdown.style.opacity = '';
                categoriesDropdown.style.zIndex = '';
                categoriesDropdown.style.pointerEvents = '';

                // For desktop, ensure transform is reset
                if (!isMobile) {
                    categoriesDropdown.style.transform = 'none';
                }

                openCategories();

                // Set active state for bottom nav button
                if (isMobile) {
                    // Remove active class from all buttons
                    document.querySelectorAll('.bottom-nav-btn').forEach(btn => {
                        btn.classList.remove('active');
                    });

                    // Add active class to categories button
                    const categoriesBtn = document.querySelector('.bottom-nav-btn[data-action="categories"]');
                    if (categoriesBtn) {
                        categoriesBtn.classList.add('active');
                    }
                }
            }
        }

        // Add event listeners for categories when the DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            const categoriesDropdown = document.getElementById('categories-dropdown');
            const categoriesBackdrop = document.getElementById('categories-backdrop');
            const categoriesClose = document.querySelector('.categories-close');
            const categoryButton = document.querySelector('.category-button');

            // Close categories when clicking the close button
            if (categoriesClose) {
                categoriesClose.addEventListener('click', window.closeCategories);
            }

            // Close categories when clicking the backdrop
            if (categoriesBackdrop) {
                categoriesBackdrop.addEventListener('click', window.closeCategories);
            }

            // Add click outside handler for desktop mode
            document.addEventListener('click', function(e) {
                if (window.innerWidth > 768) {
                    if (categoriesDropdown &&
                        categoriesDropdown.classList.contains('show') &&
                        !categoriesDropdown.contains(e.target) &&
                        categoryButton &&
                        !categoryButton.contains(e.target)) {
                        window.closeCategories();
                    }
                }
            });

            // Position the dropdown correctly for desktop
            function positionCategoriesDropdown() {
                if (!categoriesDropdown) return;

                if (window.innerWidth > 768) {
                    if (categoryButton) {
                        const buttonRect = categoryButton.getBoundingClientRect();
                        categoriesDropdown.style.top = (buttonRect.bottom + 10) + 'px';
                        categoriesDropdown.style.left = buttonRect.left + 'px';
                        categoriesDropdown.style.transform = 'none';
                        categoriesDropdown.style.position = 'fixed';
                        categoriesDropdown.style.maxHeight = '80vh';
                        categoriesDropdown.style.overflowY = 'auto';
                        categoriesDropdown.style.backgroundColor = 'var(--bg-dark, #111827)';
                        categoriesDropdown.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2), 0 0 0 1px #374151';
                        categoriesDropdown.style.border = '1px solid #374151';
                        categoriesDropdown.style.borderRadius = '8px';
                    }
                } else {
                    // Reset for mobile
                    categoriesDropdown.style.top = '';
                    categoriesDropdown.style.left = '';
                    categoriesDropdown.style.transform = '';
                    categoriesDropdown.style.position = '';
                }
            }

            // Position dropdown on load
            positionCategoriesDropdown();

            // Position dropdown on resize
            window.addEventListener('resize', function() {
                positionCategoriesDropdown();

                // Close dropdown on resize
                if (categoriesDropdown && categoriesDropdown.classList.contains('show')) {
                    window.closeCategories();
                }
            });
        });

        // Close categories when pressing Escape key
        document.addEventListener('keydown', function(e) {
            const categoriesDropdown = document.getElementById('categories-dropdown');
            if (e.key === 'Escape' && categoriesDropdown && categoriesDropdown.classList.contains('show')) {
                window.closeCategories();
            }
        });



        // Handle device-specific UI elements
        document.addEventListener('DOMContentLoaded', function() {
            // Function to handle device-specific UI elements
            function handleDeviceSpecificUI() {
                const isMobile = window.innerWidth <= 768;
                const mobileButtons = document.querySelector('.mobile-header-buttons');
                const desktopButtons = document.querySelector('.desktop-nav-buttons');

                if (isMobile) {
                    // Mobile device
                    if (mobileButtons) mobileButtons.style.display = 'flex';
                    if (desktopButtons) desktopButtons.style.display = 'none';
                } else {
                    // Desktop device
                    if (mobileButtons) mobileButtons.style.display = 'none';
                    if (desktopButtons) desktopButtons.style.display = 'flex';
                }
            }

            // Run on page load
            handleDeviceSpecificUI();

            // Run on resize
            window.addEventListener('resize', handleDeviceSpecificUI);

            // Delayed check for reliability
            setTimeout(handleDeviceSpecificUI, 500);
        });
    </script>

    <!-- Login Modal -->
    <div class="auth-modal" id="login-modal">
        <div class="auth-modal-content">
            <div class="auth-modal-header">
                <h2>登录</h2>
                <span class="auth-modal-close" onclick="closeLoginModal()">&times;</span>
            </div>
            <div class="auth-modal-body">
                <div id="login-error" class="auth-error"></div>
                <div id="login-success" class="auth-success"></div>

                <form id="login-form" class="auth-form">
                    <div class="form-group">
                        <label for="login-email">邮箱</label>
                        <input type="email" id="login-email" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="login-password">密码</label>
                        <input type="password" id="login-password" name="password" required>
                    </div>

                    <button type="submit" class="auth-button">登录</button>
                </form>

                <div class="auth-links">
                    <p>还没有账号？<a href="#" onclick="closeLoginModal(); openRegisterModal(); return false;">注册</a></p>
                    <p><a href="#" id="forgot-password" onclick="closeLoginModal(); openForgotPasswordModal(); return false;">忘记密码？</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Register Modal -->
    <div class="auth-modal" id="register-modal">
        <div class="auth-modal-content">
            <div class="auth-modal-header">
                <h2>注册</h2>
                <span class="auth-modal-close" onclick="closeRegisterModal()">&times;</span>
            </div>
            <div class="auth-modal-body">
                <div id="register-error" class="auth-error"></div>
                <div id="register-success" class="auth-success"></div>

                <form id="register-form" class="auth-form">
                    <div class="form-group">
                        <label for="register-display-name">用户名</label>
                        <input type="text" id="register-display-name" name="display-name" required>
                    </div>

                    <div class="form-group">
                        <label for="register-email">邮箱</label>
                        <input type="email" id="register-email" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="register-password">密码</label>
                        <input type="password" id="register-password" name="password" required minlength="6">
                    </div>

                    <div class="form-group">
                        <label for="register-confirm-password">确认密码</label>
                        <input type="password" id="register-confirm-password" name="confirm-password" required minlength="6">
                    </div>

                    <button type="submit" class="auth-button">注册</button>
                </form>

                <div class="auth-links">
                    <p>已有账号？<a href="#" onclick="closeRegisterModal(); openLoginModal(); return false;">登录</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Forgot Password Modal -->
    <div class="auth-modal" id="forgot-password-modal">
        <div class="auth-modal-content">
            <div class="auth-modal-header">
                <h2>重置密码</h2>
                <span class="auth-modal-close" onclick="closeForgotPasswordModal()">&times;</span>
            </div>
            <div class="auth-modal-body">
                <div id="forgot-password-error" class="auth-error"></div>
                <div id="forgot-password-success" class="auth-success"></div>

                <p class="forgot-password-info">请输入您的注册邮箱，我们将向您发送密码重置链接。</p>

                <form id="forgot-password-form" class="auth-form">
                    <div class="form-group">
                        <label for="forgot-password-email">邮箱</label>
                        <input type="email" id="forgot-password-email" name="email" required>
                    </div>

                    <button type="submit" class="auth-button">发送重置链接</button>
                </form>

                <div class="auth-links">
                    <p>记起密码了？<a href="#" onclick="closeForgotPasswordModal(); openLoginModal(); return false;">返回登录</a></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Auth Modal Backdrop -->
    <div class="auth-modal-backdrop" id="auth-modal-backdrop"></div>

    <!-- Confirmation Dialog for Logout -->
    <div class="confirm-dialog-backdrop" id="logout-confirm-dialog">
        <div class="confirm-dialog">
            <div class="confirm-dialog-header">
                <h3 class="confirm-dialog-title">确认登出</h3>
            </div>
            <div class="confirm-dialog-body">
                <p>确定要登出账号吗？</p>
            </div>
            <div class="confirm-dialog-footer">
                <button class="confirm-dialog-button confirm-dialog-cancel" onclick="closeLogoutConfirm()">取消</button>
                <button class="confirm-dialog-button confirm-dialog-confirm" onclick="executeLogout()">确认</button>
            </div>
        </div>
    </div>

    <!-- Clear History Confirmation Dialog -->
    <div class="confirm-dialog-backdrop" id="clear-history-confirm-dialog">
        <div class="confirm-dialog">
            <div class="confirm-dialog-title">确认清除历史</div>
            <div class="confirm-dialog-message">您确定要清除所有历史记录吗？</div>
            <div class="confirm-dialog-buttons">
                <button class="confirm-dialog-button confirm-dialog-cancel" onclick="closeClearHistoryConfirm()">取消</button>
                <button class="confirm-dialog-button confirm-dialog-confirm" onclick="executeClearHistory()">确认</button>
            </div>
        </div>
    </div>

    <!-- Confirmation Dialog for Clear Favorites -->
    <div class="confirm-dialog-backdrop" id="clear-favorites-confirm-dialog">
        <div class="confirm-dialog">
            <div class="confirm-dialog-title">确认清除收藏</div>
            <div class="confirm-dialog-message">您确定要清除所有收藏吗？</div>
            <div class="confirm-dialog-buttons">
                <button class="confirm-dialog-button confirm-dialog-cancel" onclick="closeClearFavoritesConfirm()">取消</button>
                <button class="confirm-dialog-button confirm-dialog-confirm" onclick="executeClearFavorites()">确认</button>
            </div>
        </div>
    </div>

    <!-- Auth Modal Scripts - Simplified to use deferred loading -->
    <script>
        // Simple initialization for auth-related UI
        document.addEventListener('DOMContentLoaded', function() {
            // Check if Firebase auth is enabled in config
            const isFirebaseAuthEnabled = window.siteConfig && window.siteConfig.auth && window.siteConfig.auth.enabled !== false;

            // Update UI based on auth status
            if (!isFirebaseAuthEnabled) {
                // Hide auth links if Firebase auth is disabled
                const authLinks = document.querySelectorAll('.auth-links');
                const userLinks = document.querySelectorAll('.user-links');

                authLinks.forEach(el => {
                    el.style.display = 'none';
                });

                userLinks.forEach(el => {
                    el.style.display = 'none';
                });
            } else {
                // Check auth UI state immediately
                // Use logInfo for conditional logging
                const isDev = window.location.hostname === 'localhost' || 
                              window.location.hostname === '127.0.0.1' ||
                              window.location.hostname.includes('.local');
                const logInfo = isDev ? console.log : function() {};
                logInfo('Checking auth UI state on page load');

                // Preload Firebase in the background when the page loads
                setTimeout(function() {
                    if (typeof window.loadFirebase === 'function') {
                        window.loadFirebase(() => {
                            // After Firebase is loaded, load the Firebase sync manager
                            if (typeof window.loadFirebaseSyncManager === 'function') {
                                window.loadFirebaseSyncManager();
                            }
                        });
                    }
                }, 1000); // Slight delay to prioritize critical page content first

                // Call our global function to update the UI if available
                setTimeout(function() {
                    if (typeof window.checkAndUpdateAuthUI === 'function') {
                        window.checkAndUpdateAuthUI();
                    }
                }, 100);

                // Call it again after a short delay to ensure it's applied
                setTimeout(function() {
                    if (typeof window.checkAndUpdateAuthUI === 'function') {
                        window.checkAndUpdateAuthUI();
                    }
                }, 1000);
            }
        });

        // Make modal functions globally available
        window.openLoginModal = function() {
            // Check if Firebase auth is enabled
            const isFirebaseAuthEnabled = window.siteConfig && window.siteConfig.auth && window.siteConfig.auth.enabled !== false;
            if (!isFirebaseAuthEnabled) {
                // Use conditional logging
                const isDev = window.location.hostname === 'localhost' || 
                              window.location.hostname === '127.0.0.1' ||
                              window.location.hostname.includes('.local');
                const logInfo = isDev ? console.log : function() {};
                logInfo('Firebase auth is disabled, login modal will not be shown');
                return;
            }

            // Show the modal immediately - Firebase should already be loaded
            document.getElementById('login-modal').style.display = 'flex';
            document.getElementById('auth-modal-backdrop').style.display = 'block';
            document.body.style.overflow = 'hidden';

            // Focus on the email input
            setTimeout(() => {
                document.getElementById('login-email').focus();
            }, 100);
        };

        window.closeLoginModal = function() {
            document.getElementById('login-modal').style.display = 'none';
            document.getElementById('auth-modal-backdrop').style.display = 'none';
            document.body.style.overflow = '';
        };

        window.openRegisterModal = function() {
            // Check if Firebase auth is enabled
            const isFirebaseAuthEnabled = window.siteConfig && window.siteConfig.auth && window.siteConfig.auth.enabled !== false;
            if (!isFirebaseAuthEnabled) {
                // Use conditional logging
                const isDev = window.location.hostname === 'localhost' || 
                              window.location.hostname === '127.0.0.1' ||
                              window.location.hostname.includes('.local');
                const logInfo = isDev ? console.log : function() {};
                logInfo('Firebase auth is disabled, register modal will not be shown');
                return;
            }

            // Show the modal immediately - Firebase should already be loaded
            document.getElementById('register-modal').style.display = 'flex';
            document.getElementById('auth-modal-backdrop').style.display = 'block';
            document.body.style.overflow = 'hidden';

            // Focus on the display name input
            setTimeout(() => {
                document.getElementById('register-display-name').focus();
            }, 100);
        };

        window.closeRegisterModal = function() {
            document.getElementById('register-modal').style.display = 'none';
            document.getElementById('auth-modal-backdrop').style.display = 'none';
            document.body.style.overflow = '';
        };

        window.openForgotPasswordModal = function() {
            // Check if Firebase auth is enabled
            const isFirebaseAuthEnabled = window.siteConfig && window.siteConfig.auth && window.siteConfig.auth.enabled !== false;
            if (!isFirebaseAuthEnabled) {
                // Use conditional logging
                const isDev = window.location.hostname === 'localhost' || 
                              window.location.hostname === '127.0.0.1' ||
                              window.location.hostname.includes('.local');
                const logInfo = isDev ? console.log : function() {};
                logInfo('Firebase auth is disabled, forgot password modal will not be shown');
                return;
            }

            // Show the modal immediately - Firebase should already be loaded
            document.getElementById('forgot-password-modal').style.display = 'flex';
            document.getElementById('auth-modal-backdrop').style.display = 'block';
            document.body.style.overflow = 'hidden';

            // If email was entered in login form, copy it to forgot password form
            const loginEmail = document.getElementById('login-email').value;
            if (loginEmail) {
                document.getElementById('forgot-password-email').value = loginEmail;
            }

            // Focus on the email input
            setTimeout(() => {
                document.getElementById('forgot-password-email').focus();
            }, 100);
        };

        window.closeForgotPasswordModal = function() {
            document.getElementById('forgot-password-modal').style.display = 'none';
            document.getElementById('auth-modal-backdrop').style.display = 'none';
            document.body.style.overflow = '';
        };

        // Close modals when clicking on backdrop
        document.getElementById('auth-modal-backdrop').addEventListener('click', () => {
            closeLoginModal();
            closeRegisterModal();
            closeForgotPasswordModal();
        });

        // Close modals when pressing Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeLoginModal();
                closeRegisterModal();
                closeForgotPasswordModal();
                closeLogoutConfirm(); // Close logout confirmation
                closeClearHistoryConfirm(); // Close clear history confirmation
                closeClearFavoritesConfirm(); // Close clear favorites confirmation
            }
        });
    </script>

    <!-- Category configuration script -->
    <script src="/statics/category-config.js"></script>
</body>
</html>
