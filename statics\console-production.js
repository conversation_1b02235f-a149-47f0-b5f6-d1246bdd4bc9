/**
 * Console Output Suppression for Production
 * 
 * This script replaces all console methods with empty functions
 * to prevent any console output in production environments.
 * 
 * Include this script before any other scripts in production.
 */

(function() {
  // Save original console methods for potential restoration if needed
  const originalConsole = {
    log: console.log,
    error: console.error,
    warn: console.warn,
    info: console.info,
    debug: console.debug,
    trace: console.trace,
    table: console.table,
    dir: console.dir,
    dirxml: console.dirxml,
    group: console.group,
    groupCollapsed: console.groupCollapsed,
    groupEnd: console.groupEnd,
    time: console.time,
    timeEnd: console.timeEnd,
    timeLog: console.timeLog,
    count: console.count,
    countReset: console.countReset,
    assert: console.assert,
    clear: console.clear
  };

  // Function to create no-op functions that return the first argument
  // This allows for chaining methods when needed
  const createNoOp = () => function() { 
    return arguments.length > 0 ? arguments[0] : undefined; 
  };

  // Replace all console methods with empty functions
  Object.keys(originalConsole).forEach(method => {
    console[method] = createNoOp();
  });

  // Optional: Add a method to restore console functionality if needed
  window._restoreConsole = function(specificMethods) {
    if (Array.isArray(specificMethods)) {
      // Restore only specific methods
      specificMethods.forEach(method => {
        if (originalConsole[method]) {
          console[method] = originalConsole[method];
        }
      });
      return `Restored console methods: ${specificMethods.join(', ')}`;
    } else {
      // Restore all methods
      Object.keys(originalConsole).forEach(method => {
        console[method] = originalConsole[method];
      });
      return "All console functionality restored";
    }
  };

  // Add method to allow temporary logging in production
  window._tempLog = function() {
    originalConsole.log.apply(console, arguments);
    return arguments.length > 0 ? arguments[0] : undefined;
  };
})();
