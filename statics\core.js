/**
 * Core JavaScript Library - Consolidated and Optimized
 * Version: 2.0 - Refactored for better maintainability
 * 
 * This file consolidates utility functions from:
 * - utils.js
 * - utils-enhanced.js
 * - performance-optimizations.js
 * - performance-enhanced.js
 */

'use strict';

// === ENVIRONMENT DETECTION ===

const Environment = {
  // Cache detection results for performance
  _cache: new Map(),
  
  isDevelopment() {
    if (this._cache.has('isDev')) return this._cache.get('isDev');
    
    const isDev = window.location.hostname === 'localhost' || 
                  window.location.hostname === '127.0.0.1' ||
                  window.location.hostname.includes('.local');
    
    this._cache.set('isDev', isDev);
    return isDev;
  },
  
  isMobile() {
    if (this._cache.has('isMobile')) return this._cache.get('isMobile');
    
    const isMobile = window.innerWidth <= 768 || 
                     /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    this._cache.set('isMobile', isMobile);
    return isMobile;
  },
  
  isTablet() {
    if (this._cache.has('isTablet')) return this._cache.get('isTablet');
    
    const isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;
    
    this._cache.set('isTablet', isTablet);
    return isTablet;
  },
  
  isDesktop() {
    return !this.isMobile() && !this.isTablet();
  },
  
  supportsWebP() {
    if (this._cache.has('webp')) return this._cache.get('webp');
    
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    const webpSupport = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    
    this._cache.set('webp', webpSupport);
    return webpSupport;
  },
  
  prefersReducedMotion() {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  },
  
  prefersDarkMode() {
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  },
  
  // Clear cache on resize
  clearCache() {
    this._cache.clear();
  }
};

// === LOGGING UTILITY ===

const Logger = {
  _isDev: Environment.isDevelopment(),
  
  log(...args) {
    if (this._isDev) console.log(...args);
  },
  
  error(...args) {
    if (this._isDev) console.error(...args);
  },
  
  warn(...args) {
    if (this._isDev) console.warn(...args);
  },
  
  info(...args) {
    if (this._isDev) console.info(...args);
  },
  
  group(label) {
    if (this._isDev) console.group(label);
  },
  
  groupEnd() {
    if (this._isDev) console.groupEnd();
  },
  
  time(label) {
    if (this._isDev) console.time(label);
  },
  
  timeEnd(label) {
    if (this._isDev) console.timeEnd(label);
  }
};

// === DOM UTILITIES ===

const DOM = {
  /**
   * Create an element with attributes and children
   */
  createElement(tag, attributes = {}, children = []) {
    const element = document.createElement(tag);
    
    // Set attributes
    Object.entries(attributes).forEach(([key, value]) => {
      if (key === 'className') {
        element.className = value;
      } else if (key === 'innerHTML') {
        element.innerHTML = value;
      } else if (key === 'textContent') {
        element.textContent = value;
      } else if (key.startsWith('data-')) {
        element.setAttribute(key, value);
      } else if (key.startsWith('aria-')) {
        element.setAttribute(key, value);
      } else {
        element[key] = value;
      }
    });
    
    // Add children
    if (Array.isArray(children)) {
      children.forEach(child => {
        if (typeof child === 'string') {
          element.appendChild(document.createTextNode(child));
        } else if (child instanceof Node) {
          element.appendChild(child);
        }
      });
    } else if (typeof children === 'string') {
      element.textContent = children;
    }
    
    return element;
  },
  
  /**
   * Query selector with error handling
   */
  $(selector, context = document) {
    try {
      return context.querySelector(selector);
    } catch (error) {
      Logger.error('Invalid selector:', selector, error);
      return null;
    }
  },
  
  /**
   * Query selector all with error handling
   */
  $$(selector, context = document) {
    try {
      return Array.from(context.querySelectorAll(selector));
    } catch (error) {
      Logger.error('Invalid selector:', selector, error);
      return [];
    }
  },
  
  /**
   * Add event listener with automatic cleanup
   */
  on(element, event, handler, options = {}) {
    if (!element || typeof handler !== 'function') return null;
    
    element.addEventListener(event, handler, options);
    
    // Return cleanup function
    return () => element.removeEventListener(event, handler, options);
  },
  
  /**
   * Add event listener that fires once
   */
  once(element, event, handler, options = {}) {
    return this.on(element, event, handler, { ...options, once: true });
  },
  
  /**
   * Check if element is in viewport
   */
  isInViewport(element, threshold = 0) {
    if (!element) return false;
    
    const rect = element.getBoundingClientRect();
    const windowHeight = window.innerHeight || document.documentElement.clientHeight;
    const windowWidth = window.innerWidth || document.documentElement.clientWidth;
    
    return (
      rect.top >= -threshold &&
      rect.left >= -threshold &&
      rect.bottom <= windowHeight + threshold &&
      rect.right <= windowWidth + threshold
    );
  },
  
  /**
   * Smooth scroll to element
   */
  scrollTo(element, options = {}) {
    if (!element) return;
    
    const defaultOptions = {
      behavior: Environment.prefersReducedMotion() ? 'auto' : 'smooth',
      block: 'start',
      inline: 'nearest'
    };
    
    element.scrollIntoView({ ...defaultOptions, ...options });
  },
  
  /**
   * Get element's computed style property
   */
  getStyle(element, property) {
    if (!element) return null;
    return window.getComputedStyle(element).getPropertyValue(property);
  },
  
  /**
   * Set multiple CSS properties
   */
  setStyles(element, styles) {
    if (!element || !styles) return;
    
    Object.entries(styles).forEach(([property, value]) => {
      element.style[property] = value;
    });
  }
};

// === STORAGE UTILITIES ===

const Storage = {
  /**
   * Get item from localStorage with error handling
   */
  get(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      Logger.error('localStorage get failed:', error);
      return defaultValue;
    }
  },
  
  /**
   * Set item in localStorage with error handling
   */
  set(key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      Logger.error('localStorage set failed:', error);
      return false;
    }
  },
  
  /**
   * Remove item from localStorage
   */
  remove(key) {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      Logger.error('localStorage remove failed:', error);
      return false;
    }
  },
  
  /**
   * Clear all localStorage
   */
  clear() {
    try {
      localStorage.clear();
      return true;
    } catch (error) {
      Logger.error('localStorage clear failed:', error);
      return false;
    }
  },
  
  /**
   * Check if localStorage is available
   */
  isAvailable() {
    try {
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch (error) {
      return false;
    }
  }
};

// === PERFORMANCE UTILITIES ===

const Performance = {
  /**
   * Debounce function calls
   */
  debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        timeout = null;
        if (!immediate) func.apply(this, args);
      };
      const callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) func.apply(this, args);
    };
  },
  
  /**
   * Throttle function calls
   */
  throttle(func, limit) {
    let inThrottle;
    return function executedFunction(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },
  
  /**
   * Request animation frame with fallback
   */
  raf(callback) {
    return (window.requestAnimationFrame || 
            window.webkitRequestAnimationFrame || 
            window.mozRequestAnimationFrame || 
            ((cb) => setTimeout(cb, 16)))(callback);
  },
  
  /**
   * Cancel animation frame
   */
  cancelRaf(id) {
    return (window.cancelAnimationFrame || 
            window.webkitCancelAnimationFrame || 
            window.mozCancelAnimationFrame || 
            clearTimeout)(id);
  },
  
  /**
   * Measure performance
   */
  measure(name, fn) {
    if (!Environment.isDevelopment()) return fn();
    
    const start = performance.now();
    const result = fn();
    const end = performance.now();
    
    Logger.log(`${name} took ${end - start} milliseconds`);
    return result;
  }
};

// === MAIN CORE OBJECT ===

const Core = {
  Environment,
  Logger,
  DOM,
  Storage,
  Performance,
  
  // Version info
  version: '2.0.0',
  
  // Initialize core functionality
  init() {
    Logger.log('Core library initialized v' + this.version);
    
    // Clear environment cache on resize
    window.addEventListener('resize', Performance.debounce(() => {
      Environment.clearCache();
    }, 250));
    
    // Add global error handling
    window.addEventListener('error', (event) => {
      Logger.error('Global error:', event.error);
    });
    
    // Add unhandled promise rejection handling
    window.addEventListener('unhandledrejection', (event) => {
      Logger.error('Unhandled promise rejection:', event.reason);
    });
  }
};

// Make Core available globally
window.Core = Core;

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => Core.init());
} else {
  Core.init();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = Core;
}
