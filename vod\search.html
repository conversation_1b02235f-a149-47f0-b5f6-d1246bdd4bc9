{| extend("../base.html") |}

{| block head |}
<script>
    // Add search-results-page class to body
    document.addEventListener('DOMContentLoaded', function() {
        document.body.classList.add('search-results-page');

        // Mobile scrolling fix
        if (window.innerWidth <= 768) {
            // Ensure content is scrollable on mobile
            const mainContent = document.querySelector('main.main-content');
            if (mainContent) {
                // Apply styles directly to ensure scrolling works
                mainContent.style.overflowY = 'auto';
                mainContent.style.webkitOverflowScrolling = 'touch';
                mainContent.style.paddingBottom = window.innerWidth <= 480 ? '66px' : '70px';
            }
            
            // Fix for results visibility
            const vodList = document.querySelector('.vod-list');
            if (vodList) {
                vodList.style.paddingBottom = '10px';
            }
            
            // Ensure pagination is visible
            const pagination = document.querySelector('.flex.flex-wrap.items-center.justify-center.gap-2.mt-6');
            if (pagination) {
                pagination.style.marginBottom = '20px';
            }
        }

        // Ensure the header works properly on desktop
        if (window.innerWidth > 768) {
            const header = document.querySelector('.site-header');
            if (header) {
                // Make sure the header has the correct styles
                header.style.position = 'sticky';
                header.style.top = '0';
                header.style.zIndex = '10';

                // Reset any properties that might prevent the header from collapsing
                header.style.transform = '';
            }

            // Hide the bottom navigation bar in desktop mode
            const bottomNav = document.querySelector('.bottom-nav');
            if (bottomNav) {
                bottomNav.style.display = 'none';
                bottomNav.style.visibility = 'hidden';
                bottomNav.style.opacity = '0';
                bottomNav.style.pointerEvents = 'none';
            }

            // Reset any padding added for the bottom nav
            const mainContent = document.querySelector('main.main-content');
            if (mainContent) {
                mainContent.style.paddingBottom = '';
            }
        }
    });

    // Run immediately as well
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        document.body.classList.add('search-results-page');

        // Mobile scrolling fix for immediate execution
        if (window.innerWidth <= 768) {
            // Ensure content is scrollable on mobile
            const mainContent = document.querySelector('main.main-content');
            if (mainContent) {
                // Apply styles directly to ensure scrolling works
                mainContent.style.overflowY = 'auto';
                mainContent.style.webkitOverflowScrolling = 'touch';
                mainContent.style.paddingBottom = window.innerWidth <= 480 ? '66px' : '70px';
            }
            
            // Fix for results visibility
            const vodList = document.querySelector('.vod-list');
            if (vodList) {
                vodList.style.paddingBottom = '10px';
            }
            
            // Ensure pagination is visible
            const pagination = document.querySelector('.flex.flex-wrap.items-center.justify-center.gap-2.mt-6');
            if (pagination) {
                pagination.style.marginBottom = '20px';
            }
        }

        // Hide the bottom navigation bar in desktop mode
        if (window.innerWidth > 768) {
            const bottomNav = document.querySelector('.bottom-nav');
            if (bottomNav) {
                bottomNav.style.display = 'none';
                bottomNav.style.visibility = 'hidden';
                bottomNav.style.opacity = '0';
                bottomNav.style.pointerEvents = 'none';
            }

            // Reset any padding added for the bottom nav
            const mainContent = document.querySelector('main.main-content');
            if (mainContent) {
                mainContent.style.paddingBottom = '';
            }
        }
    }
</script>
{| end block head |}

{| block style |}
<style>
    /* Additional mobile-specific styles */
    @media (max-width: 480px) {
        .search-header h2 {
            font-size: 1.1rem !important;
        }

        .search-count {
            font-size: 0.75rem !important;
        }

        .pagination-btn {
            padding: 0.25rem 0.5rem !important;
            font-size: 0.875rem !important;
        }

        .vod-title {
            font-size: 0.75rem !important;
            line-height: 1.2 !important;
        }

        .vod-badge {
            font-size: 0.7rem !important;
            padding: 0.1rem 0.3rem !important;
        }

        .vod-memo {
            font-size: 0.7rem !important;
            padding: 0.1rem 0.3rem !important;
        }
    }
</style>
{| end block style |}

{| block content |}
<!-- Tailwind CDN Integration -->
<link href="https://cdn.jsdelivr.net/npm/tailwindcss@3.4.1/dist/tailwind.min.css" rel="stylesheet">

<div class="wrapper max-w-6xl mx-auto p-4">
    <!-- Search Results Header -->
    <div class="search-header mb-4">
        {[ if ctx.err_msg ~= '' then ]}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <p>{{ ctx.err_msg }}</p>
        </div>
        {[ elseif #ctx.vods == 0 then ]}
        <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
            <p>无结果，请更换关键词重试。</p>
        </div>
        {[ else ]}
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
            <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2 sm:mb-0">搜索结果: "{{ ctx.kw }}"</h2>
            <div class="search-count text-sm text-gray-500 dark:text-gray-400">找到 {= #ctx.vods =} 条结果</div>
        </div>

        <!-- Pagination Links -->
        {[
        local query_prefix = string.format('?kw=%s', __escape(ctx.kw))
        if ctx.field ~= '' then
            query_prefix = string.format('%s&field=%s', query_prefix, __escape(ctx.field))
        end
        ]}
        <div class="flex flex-wrap items-center gap-2 mb-4">
            <a href="{= query_prefix =}" class="pagination-btn px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition">首页</a>
            <a href="{= query_prefix =}&o={= ctx.offset_prev =}" class="pagination-btn px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition">上一页</a>
            {[ if ctx.offset_next > 0 then ]}
            <a href="{= query_prefix =}&o={= ctx.offset_next =}" class="pagination-btn px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition">下一页</a>
            {[ end ]}
        </div>
        {[ end ]}
    </div>

    <!-- Grid Display for Search Results -->
    {[ if #ctx.vods > 0 then ]}
    <div class="vod-list grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        {[ for _, vod in ipairs(ctx.vods) do ]}
        <div class="vod-item bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden transition-transform hover:scale-105">
            <a href="/vod/detail/?id={= vod.id =}" class="block" title="{{ vod.title }}">
                <div class="vod-poster relative aspect-[2/3] bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                    <img loading="lazy" data-src="{= vod.poster =}" class="object-cover w-full h-full" alt="{{ vod.title }}" onerror="this.onerror=null; this.style.display='none'; this.parentNode.innerHTML += '<div class=\'flex items-center justify-center w-full h-full text-gray-500\'>🎬</div>';">
                    {[ if vod.douban and vod.douban.score and vod.douban.score ~= '0.0' then ]}
                    <div class="vod-badge absolute top-2 left-2 bg-yellow-400 text-xs font-bold px-2 py-1 rounded shadow">{{ vod.douban.score }}</div>
                    {[ end ]}
                    <div class="vod-memo absolute bottom-2 left-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                        {{ vod.memo
                            :gsub('更新至第', '第')
                            :gsub('更新至', '第')
                            :gsub('更新第', '第')
                            :gsub('更新(%d)', '第%1')
                            :gsub('更新', '')
                            :gsub('连载中 连载到', '第')
                        }}
                    </div>
                </div>
                <div class="p-2">
                    <div class="vod-title text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{{ vod.title }}</div>
                </div>
            </a>
        </div>
        {[ end ]}
        {[ for _=0, 10, 1 do ]}
        <div class="vod-item"></div>
        {[ end ]}
    </div>

    <!-- Bottom Pagination Links -->
    <div class="flex flex-wrap items-center justify-center gap-2 mt-6">
        <a href="{= query_prefix =}" class="pagination-btn px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition">首页</a>
        <a href="{= query_prefix =}&o={= ctx.offset_prev =}" class="pagination-btn px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition">上一页</a>
        {[ if ctx.offset_next > 0 then ]}
        <a href="{= query_prefix =}&o={= ctx.offset_next =}" class="pagination-btn px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition">下一页</a>
        {[ end ]}
    </div>
    {[ end ]}
</div>
{| end block content |}

{| block script |}
{[ local srch = ctx.kw:gsub("'", "\\'") ]}
<script>document.getElementById('idx-search-input').value = '{= srch =}'</script>
{[ if ctx.field == 'tag' then ]}
<script>document.querySelectorAll('.idx-search select option')[1].selected = true</script>
{[ elseif ctx.field == 'staff' then ]}
<script>document.querySelectorAll('.idx-search select option')[2].selected = true</script>
{[ end ]}
<script>
    // Lazy load images with error handling
    setTimeout(() => {
        document.querySelectorAll('.vod-poster img')
            .forEach((img) => {
                const dataSrc = img.getAttribute('data-src');
                if (dataSrc) {
                    // Check if the URL is valid (not containing /vod/search/null)
                    if (!dataSrc.includes('/vod/search/null')) {
                        img.setAttribute('src', dataSrc);
                    } else {
                        // Handle invalid URL
                        img.style.display = 'none';
                        img.parentNode.innerHTML += '<div class="flex items-center justify-center w-full h-full text-gray-500">🎬</div>';
                    }
                }
            });
    }, 300);

    // Save search history
    (() => {
        const hist = JSON.parse(localStorage.getItem('vodg-vod-history')) || [];
        const now = Date.now();
        const item = {
            id: 'search-{= ctx.com.urls.current =}',
            url: '{= ctx.com.urls.current =}',
            title: '搜索：{{ ctx.com.title }}',
            timestamp: now, // Add timestamp for syncing
            lastWatched: now // Add lastWatched for consistency with favorites
        };

        // Remove existing entries with same ID
        for (let i = 0; i < hist.length; i++) {
            if (hist[i] && item.id === hist[i].id) {
                hist.splice(i, 1);
                i--;
            }
        }

        // Add new search to history
        hist.unshift(item);
        localStorage.setItem('vodg-vod-history', JSON.stringify(hist));

        // If user is logged in, sync directly to Firebase
        if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
            const userId = window.getCurrentUserId();
            if (userId && window.syncManager) {
                if (typeof window.syncManager.forceSearchHistorySync === 'function') {
                    window.syncManager.forceSearchHistorySync(userId);
                } else {
                    window.syncManager.syncToFirestore(userId, 'searchHistory');
                }
            }
        } else {
            // If not logged in, just dispatch the event
            window.dispatchEvent(new CustomEvent('searchHistoryUpdated'));
        }
    })();
</script>
{| end block script |}