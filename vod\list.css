/* Modern VOD List Page Styles */
body {
  margin: 0;
  font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
  background: #18181c;
  color: #f5f5f7;
}

.navbar {
  position: sticky;
  top: 0;
  width: 100%;
  background: #23232b;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.navbar .logo {
  font-weight: bold;
  font-size: 1.5rem;
  color: #ffb300;
  letter-spacing: 2px;
  text-decoration: none;
}
.navbar nav a {
  color: #f5f5f7;
  margin-left: 2rem;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
}
.navbar nav a:hover {
  color: #ffb300;
}

.vod-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 2rem;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}
.vod-item {
  background: #23232b;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  transition: transform 0.2s, box-shadow 0.2s, border 0.2s;
  border: 2px solid transparent;
  display: flex;
  flex-direction: column;
}
.vod-item:hover {
  transform: translateY(-8px) scale(1.03);
  box-shadow: 0 8px 24px rgba(0,0,0,0.22);
  border-color: #ffb300;
}
.vod-poster {
  position: relative;
  width: 100%;
  aspect-ratio: 2/3;
  background: #1a1a1f;
  display: flex;
  align-items: center;
  justify-content: center;
}
.vod-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}
.vod-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background: #ffb300;
  color: #23232b;
  font-weight: bold;
  border-radius: 8px;
  padding: 0.2em 0.7em;
  font-size: 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.12);
}
.vod-memo {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  background: rgba(40,40,50,0.85);
  color: #fff;
  font-size: 0.92rem;
  border-radius: 6px;
  padding: 0.3em 0.7em;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
}
.vod-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffb300;
  margin: 1rem 1rem 0.8rem 1rem;
  text-align: center;
  min-height: 2.5em;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media (max-width: 700px) {
  .vod-list {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 1rem;
    padding: 1rem;
  }
  .vod-title {
    font-size: 1rem;
    margin: 0.7rem 0.5rem 0.7rem 0.5rem;
  }
  .navbar {
    padding: 0.7rem 1rem;
  }
}
