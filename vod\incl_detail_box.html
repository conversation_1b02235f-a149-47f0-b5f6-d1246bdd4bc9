<div class="detail-container" data-vod-id="{= ctx.vod.id =}">
    <div class="detail-poster">
        <img loading="lazy" class="poster-{= ctx.vod.id =}" src="{= ctx.vod.poster =}" alt="{{ ctx.vod.title }}">
    </div>
    <div class="detail-info">
        <h1 class="detail-title">{{ ctx.vod.title }}</h1>
        <div class="vod-actions">
            <!-- Favorites button will be added here by JavaScript -->
        </div>

        <div class="detail-meta">
            {[ if ctx.vod.published ~= 0 then ]}
            <span>{= func.time.format(ctx.vod.published):sub(1, 4) =}</span>
            <span>•</span>
            {[ end ]}

            <span>{{ ctx.vod.kind }}</span>
            <span>•</span>
            <span>{{ ctx.vod.location }}</span>

            {[ if ctx.vod.douban.score ~= '0.0' then ]}
            <span>•</span>
            <span style="color: var(--accent); font-weight: 600;">
                {[ if ctx.vod.douban.id == '' then ]}
                {{ ctx.vod.douban.score }}
                {[ else ]}
                <a href="https://www.douban.com/subject/{= ctx.vod.douban.id =}/"
                    rel="nofollow noopener noreferrer"
                    target="_blank">{{ ctx.vod.douban.score }}</a>
                {[ end ]}
            </span>
            {[ end ]}
        </div>

        <div class="detail-description">
            {= ctx.vod.summary:gsub('\n', '<br>') =}
        </div>

        <div style="display: grid; grid-template-columns: auto 1fr; gap: 0.75rem 1.5rem; color: var(--text-secondary);">
            <div style="color: var(--text-tertiary);">状态：</div>
            <div>{{ ctx.vod.memo }}</div>

            <div style="color: var(--text-tertiary);">更新时间：</div>
            <div>{= func.time.format(ctx.vod.updated):sub(1, 10) =}</div>

            <div style="color: var(--text-tertiary);">导演：</div>
            <div>{{ ctx.vod.director }}</div>

            <div style="color: var(--text-tertiary);">编剧：</div>
            <div>{{ ctx.vod.writer }}</div>

            <div style="color: var(--text-tertiary);">演员：</div>
            <div>{{ ctx.vod.cast }}</div>

            <div style="color: var(--text-tertiary);">语言：</div>
            <div>{{ ctx.vod.language }}</div>
        </div>
    </div>
</div>
{[ local posters = string.format('["%s"]', table.concat(ctx.vod.posters, '","')) ]}
<script>
    const posters = JSON.parse('{= posters =}');
    const loadImg = (cls, srcs) => {
        let src = srcs.pop()
        while (src === '') {
            src = srcs.pop()
        }
        if (src) {
            let img = new Image()
            img.onload = () => {
                for (const tag of document.getElementsByClassName(cls)) {
                    if (tag.src != src) {
                        tag.src = src
                    }
                }
            }
            img.onerror = () => loadImg(cls, srcs)
            img.src = src
        }
    }
    loadImg('poster-{= ctx.vod.id =}', posters)
</script>
