/* Critical CSS - Only essential styles needed for initial rendering */

/* Import CSS variables from a single source */
@import url('/statics/css-variables.css');

/* Document reset and base styles */
:root {
  /* Base font size for rem calculations */
  font-size: 16px;
}

/* Improved box sizing for all elements */
*, *::before, *::after {
  box-sizing: border-box;
}

/* Base element styles */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-dark);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  text-rendering: optimizeSpeed;
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: color var(--transition);
}

a:hover {
  color: var(--primary-light);
}

button {
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
}

/* Container and layout */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

/* Grid system - critical for layout */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
  /* Define containment for better performance */
  contain: layout style paint;
}

/* Media card styles - critical for content display */
.media-card {
  position: relative;
  overflow: hidden;
  border-radius: var(--radius-lg);
  background-color: var(--bg-card);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  /* Hardware acceleration hint */
  transform: translateZ(0);
}

.media-poster {
  width: 100%;
  aspect-ratio: 2/3; /* Modern aspect ratio property */
  position: relative;
  overflow: hidden;
  background-color: var(--bg-card);
}

/* Fallback for browsers that don't support aspect-ratio */
@supports not (aspect-ratio: 2/3) {
  .media-poster {
    height: 0;
    padding-bottom: 150%;
  }
}

.media-poster img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.media-title {
  padding: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Image optimization */
img {
  max-width: 100%;
  height: auto;
  /* Prevent image layout shift */
  display: block;
}

/* Focus styles for accessibility */
a:focus, button:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .media-card:hover {
    transform: none !important;
  }
}

/* Adaptive media queries */
@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--spacing-4);
  }

  .site-header {
    position: sticky;
    top: 0;
    z-index: 10;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
}

/* High-contrast mode adjustments for accessibility */
@media (prefers-contrast: high) {
  .media-card {
    border: 1px solid currentColor;
  }
  
  a {
    text-decoration: underline;
  }
}
