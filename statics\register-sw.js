/**
 * Service Worker Registration
 */

// Register service worker if supported
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/statics/service-worker.js')
      .then(registration => {
        // Service Worker registered successfully

        // Check for updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;

          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New service worker is installed but waiting to activate

              // Show update notification
              showUpdateNotification();
            }
          });
        });
      })
      .catch(error => {
        // Service Worker registration failed
      });

    // Listen for controller change
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      // Service Worker controller changed
    });
  });
}

/**
 * Show update notification
 */
function showUpdateNotification() {
  // Create notification container if it doesn't exist
  let notificationContainer = document.getElementById('sw-update-notification');

  if (!notificationContainer) {
    notificationContainer = document.createElement('div');
    notificationContainer.id = 'sw-update-notification';
    notificationContainer.style.position = 'fixed';
    notificationContainer.style.bottom = '20px';
    notificationContainer.style.left = '20px';
    notificationContainer.style.backgroundColor = 'var(--primary)';
    notificationContainer.style.color = 'white';
    notificationContainer.style.padding = '12px 16px';
    notificationContainer.style.borderRadius = '4px';
    notificationContainer.style.boxShadow = 'var(--shadow-md)';
    notificationContainer.style.zIndex = '9999';
    notificationContainer.style.display = 'flex';
    notificationContainer.style.alignItems = 'center';
    notificationContainer.style.justifyContent = 'space-between';
    notificationContainer.style.maxWidth = '300px';
    notificationContainer.style.transform = 'translateY(100px)';
    notificationContainer.style.opacity = '0';
    notificationContainer.style.transition = 'transform 0.3s ease, opacity 0.3s ease';

    // Add notification content
    notificationContainer.innerHTML = `
      <div>
        <div style="font-weight: 600; margin-bottom: 4px;">更新可用</div>
        <div style="font-size: 14px;">刷新页面以获取最新内容</div>
      </div>
      <div style="display: flex; gap: 8px; margin-left: 16px;">
        <button id="sw-update-dismiss" style="background: none; border: none; color: white; cursor: pointer; padding: 4px 8px; font-size: 14px;">稍后</button>
        <button id="sw-update-refresh" style="background: white; border: none; color: var(--primary); cursor: pointer; padding: 4px 8px; border-radius: 4px; font-weight: 600; font-size: 14px;">刷新</button>
      </div>
    `;

    document.body.appendChild(notificationContainer);

    // Show notification with animation
    setTimeout(() => {
      notificationContainer.style.transform = 'translateY(0)';
      notificationContainer.style.opacity = '1';
    }, 100);

    // Add event listeners
    document.getElementById('sw-update-dismiss').addEventListener('click', () => {
      notificationContainer.style.transform = 'translateY(100px)';
      notificationContainer.style.opacity = '0';

      setTimeout(() => {
        if (notificationContainer.parentNode) {
          notificationContainer.parentNode.removeChild(notificationContainer);
        }
      }, 300);
    });

    document.getElementById('sw-update-refresh').addEventListener('click', () => {
      // Send message to service worker to skip waiting
      navigator.serviceWorker.ready.then(registration => {
        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      });

      // Reload the page
      window.location.reload();
    });
  }
}
