{| extend("../base.html") |}

{| block head_title |}{| include("incl_detail_head.html") |}{| end block head_title |}

{| block content |}
{| include("incl_detail_box.html") |}
{[
local ch_code
for _, ch in ipairs(ctx.channels) do
if ctx.vod.sources[ch.code] then
ch_code = ch.code
break
end
end
]}
{| include("incl_detail_episodes.html", { ch = ch_code }) |}
<div class="recommendations">
    <h2 class="recommendations-title">更多推荐</h2>
    {| include("incl_list.html", { vod_list = ctx.more_vods }) |}
</div>
{| end block content |}

{| block script |}
<script>
    (() => {
        const hist = JSON.parse(localStorage.getItem('vodg-vod-history'))
        const now = Date.now();
        const item = {
            id: '{= ctx.vod.id =}',
            url: '/vod/detail/?id={= ctx.vod.id =}',
            title: '{{ ctx.vod.title }}',
            poster: '{= ctx.vod.poster =}',
            timestamp: now, // Add timestamp for syncing
            lastWatched: now // Add lastWatched for consistency with favorites
        }
        if (hist) {
            for (let i = 0; i < hist.length; i++) {
                if (hist[i]) {
                    if (item.id == hist[i].id) {
                        hist.splice(i, 1)
                    }
                } else {
                    hist.splice(i, 1)
                }
            }
            hist.unshift(item)
            localStorage.setItem('vodg-vod-history', JSON.stringify(hist))

            // If user is logged in, sync directly to Firebase
            if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
                const userId = window.getCurrentUserId();
                if (userId && window.syncManager) {
                    if (typeof window.syncManager.forceViewingHistorySync === 'function') {
                        window.syncManager.forceViewingHistorySync(userId);
                    } else {
                        window.syncManager.syncToFirestore(userId, 'viewingHistory');
                    }
                }
            } else {
                // If not logged in, just dispatch the event
                window.dispatchEvent(new CustomEvent('viewingHistoryUpdated'));
            }
        } else {
            localStorage.setItem('vodg-vod-history', JSON.stringify([item]))

            // If user is logged in, sync directly to Firebase
            if (typeof window.isUserLoggedIn === 'function' && window.isUserLoggedIn()) {
                const userId = window.getCurrentUserId();
                if (userId && window.syncManager) {
                    if (typeof window.syncManager.forceViewingHistorySync === 'function') {
                        window.syncManager.forceViewingHistorySync(userId);
                    } else {
                        window.syncManager.syncToFirestore(userId, 'viewingHistory');
                    }
                }
            } else {
                // If not logged in, just dispatch the event
                window.dispatchEvent(new CustomEvent('viewingHistoryUpdated'));
            }
        }
        console.log('{{ ctx.vod.title }}')
    })();
</script>
{| end block script |}
