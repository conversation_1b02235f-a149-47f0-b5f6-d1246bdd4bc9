/**
 * Main Stylesheet - Consolidated and Optimized
 * Version: 2.0 - Refactored for better maintainability
 *
 * This file consolidates critical styles and replaces multiple CSS files:
 * - critical.css
 * - modern-ui.css
 * - style.css
 * - style-enhanced.css
 */

/* === IMPORTS === */
@import url('/statics/css-variables.css');

/* === BASE STYLES === */

/* Reset and normalize */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-dark);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
  text-rendering: optimizeSpeed;
}

/* === TYPOGRAPHY === */

h1, h2, h3, h4, h5, h6 {
  margin-bottom: var(--spacing-4);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin-bottom: var(--spacing-4);
  color: var(--text-secondary);
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: color var(--transition) var(--ease-in-out);
}

a:hover {
  color: var(--primary-light);
}

a:focus-visible {
  outline: var(--focus-ring-width) solid var(--focus-ring-color);
  outline-offset: var(--focus-ring-offset);
  border-radius: var(--radius-sm);
}

/* === LAYOUT === */

.container {
  width: 100%;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

.main-container {
  display: flex;
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  position: relative;
}

.main-content {
  flex: 1;
  min-width: 0; /* Prevent flex item overflow */
}

/* === GRID SYSTEM === */

.grid {
  display: grid;
  gap: var(--grid-gap);
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(var(--card-min-width), 1fr));
  gap: var(--spacing-4);
  width: 100%;
}

/* === COMPONENTS === */

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2) var(--spacing-4);
  border: none;
  border-radius: var(--radius);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-align: center;
  cursor: pointer;
  transition: all var(--transition) var(--ease-in-out);
  min-height: var(--touch-target-min);
  text-decoration: none;
}

.btn:focus-visible {
  outline: var(--focus-ring-width) solid var(--focus-ring-color);
  outline-offset: var(--focus-ring-offset);
}

.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  color: white;
}

.btn-secondary {
  background-color: var(--secondary);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--secondary-hover);
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--border);
  color: var(--text-primary);
}

.btn-outline:hover {
  background-color: var(--bg-card-hover);
  border-color: var(--border-light);
}

/* Cards */
.card {
  background-color: var(--bg-card);
  border-radius: var(--media-card-border-radius);
  overflow: hidden;
  transition: transform var(--transition) var(--ease-in-out),
              box-shadow var(--transition) var(--ease-in-out);
  border: 1px solid var(--border);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.card-header {
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--border);
}

.card-body {
  padding: var(--spacing-4);
}

.card-footer {
  padding: var(--spacing-4);
  border-top: 1px solid var(--border);
  background-color: var(--bg-darker);
}

/* Forms */
.form-group {
  margin-bottom: var(--spacing-4);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-3);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  transition: border-color var(--transition) var(--ease-in-out),
              box-shadow var(--transition) var(--ease-in-out);
}

.form-control:focus {
  border-color: var(--border-focus);
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* === HEADER === */

.site-header {
  background-color: var(--bg-card);
  padding: var(--spacing-4) 0;
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  transition: transform var(--transition) var(--ease-in-out),
              padding var(--transition) var(--ease-in-out),
              box-shadow var(--transition) var(--ease-in-out);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.site-header.header-collapsed {
  padding: var(--spacing-2) 0;
  box-shadow: var(--shadow-md);
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-4);
}

.site-logo {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  transition: transform var(--transition) var(--ease-in-out);
}

.site-logo a {
  color: inherit;
}

/* Navigation */
.main-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
}

.nav-link {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  transition: color var(--transition) var(--ease-in-out);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius);
  min-height: var(--touch-target-min);
  display: flex;
  align-items: center;
}

.nav-link:hover {
  color: var(--primary-light);
  background-color: rgba(255, 255, 255, 0.05);
}

.nav-link.active {
  color: var(--primary);
  font-weight: var(--font-weight-semibold);
}

/* === SEARCH === */

.search-form {
  display: flex;
  gap: var(--spacing-2);
  width: 100%;
  max-width: 500px;
}

.search-select {
  background-color: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  min-width: 100px;
}

.search-input {
  flex: 1;
  background-color: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-base);
  transition: border-color var(--transition) var(--ease-in-out);
}

.search-input:focus {
  border-color: var(--border-focus);
  outline: none;
}

.search-button {
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius);
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition) var(--ease-in-out);
}

.search-button:hover {
  background-color: var(--primary-hover);
}

/* === HERO SECTION === */

.hero {
  text-align: center;
  padding: var(--spacing-12) 0;
  background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-darker) 100%);
}

.hero-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-4);
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* === TABS === */

.tabs {
  margin-bottom: var(--spacing-8);
}

.tab-buttons {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
  border-bottom: 1px solid var(--border);
  padding-bottom: var(--spacing-2);
  flex-wrap: wrap;
}

.tab-button {
  position: relative;
  background: none;
  border: none;
  color: var(--text-tertiary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius) var(--radius) 0 0;
  cursor: pointer;
  transition: all var(--transition) var(--ease-in-out);
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  color: var(--text-secondary);
  background-color: rgba(255, 255, 255, 0.05);
}

.tab-button.active {
  color: var(--text-primary);
  background-color: rgba(37, 99, 235, 0.1);
  border-bottom-color: var(--primary);
}

.tab-content {
  display: none;
  animation: fadeIn 0.3s var(--ease-in-out);
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* === TOGGLE CONTROLS === */

.toggle-container {
  display: flex;
  background-color: var(--bg-card);
  border-radius: var(--radius);
  padding: var(--spacing-1);
  position: relative;
  width: fit-content;
  margin-bottom: var(--spacing-4);
  height: 38px;
}

.toggle-button {
  padding: var(--spacing-2) var(--spacing-5);
  font-weight: var(--font-weight-medium);
  transition: color var(--transition) var(--ease-in-out);
  border: none;
  outline: none;
  cursor: pointer;
  position: relative;
  z-index: 1;
  background-color: transparent;
  border-radius: var(--radius);
  min-width: 80px;
  text-align: center;
  color: var(--text-tertiary);
}

.toggle-button.active {
  color: white;
}

.toggle-button:not(.active):hover {
  color: var(--text-secondary);
}

.toggle-slider {
  position: absolute;
  top: var(--spacing-1);
  left: var(--spacing-1);
  height: calc(100% - var(--spacing-2));
  width: calc(50% - var(--spacing-1));
  background-color: var(--primary);
  border-radius: var(--radius);
  transition: transform var(--transition-slow) var(--ease-bounce);
  z-index: 0;
}

.toggle-slider.right {
  transform: translateX(100%);
}

/* === MEDIA CARDS === */

.media-card {
  background-color: var(--bg-card);
  border-radius: var(--media-card-border-radius);
  overflow: hidden;
  transition: transform var(--transition) var(--ease-in-out),
              box-shadow var(--transition) var(--ease-in-out);
  border: 1px solid var(--border);
  position: relative;
}

.media-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.media-poster {
  position: relative;
  width: 100%;
  aspect-ratio: var(--card-aspect-ratio);
  overflow: hidden;
  background-color: var(--bg-darker);
}

.media-poster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform var(--transition-slow) var(--ease-in-out);
}

.media-card:hover .media-poster img {
  transform: scale(1.05);
}

.media-info {
  padding: var(--spacing-3);
}

.media-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--spacing-1);
  line-height: var(--line-height-tight);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.media-meta {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* === LOADING STATES === */

.skeleton {
  background: linear-gradient(90deg, var(--bg-card) 25%, var(--bg-card-hover) 50%, var(--bg-card) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.skeleton-card {
  background-color: var(--bg-card);
  border-radius: var(--media-card-border-radius);
  overflow: hidden;
  border: 1px solid var(--border);
}

.skeleton-poster {
  width: 100%;
  aspect-ratio: var(--card-aspect-ratio);
  background: var(--bg-card-hover);
}

.skeleton-info {
  padding: var(--spacing-3);
}

.skeleton-title {
  height: 1rem;
  background: var(--bg-card-hover);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-2);
}

.skeleton-meta {
  height: 0.75rem;
  background: var(--bg-card-hover);
  border-radius: var(--radius-sm);
  width: 60%;
}

/* === UTILITY CLASSES === */

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-flex { display: flex; }
.d-grid { display: grid; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-none { display: none; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.justify-content-start { justify-content: flex-start; }
.justify-content-center { justify-content: center; }
.justify-content-end { justify-content: flex-end; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }

.align-items-start { align-items: flex-start; }
.align-items-center { align-items: center; }
.align-items-end { align-items: flex-end; }
.align-items-stretch { align-items: stretch; }

/* Spacing utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }
.m-6 { margin: var(--spacing-6); }
.m-8 { margin: var(--spacing-8); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-3 { margin-top: var(--spacing-3); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-6 { margin-top: var(--spacing-6); }
.mt-8 { margin-top: var(--spacing-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--spacing-1); }
.ml-2 { margin-left: var(--spacing-2); }
.ml-3 { margin-left: var(--spacing-3); }
.ml-4 { margin-left: var(--spacing-4); }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--spacing-1); }
.mr-2 { margin-right: var(--spacing-2); }
.mr-3 { margin-right: var(--spacing-3); }
.mr-4 { margin-right: var(--spacing-4); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }

/* === RESPONSIVE DESIGN === */

/* Mobile First Approach */
@media (max-width: 480px) {
  .container {
    padding: 0 var(--mobile-padding);
  }

  .hero {
    padding: var(--spacing-8) 0;
  }

  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-base);
  }

  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--spacing-3);
  }

  .tab-buttons {
    gap: var(--spacing-2);
  }

  .tab-button {
    font-size: var(--font-size-base);
    padding: var(--spacing-2) var(--spacing-3);
  }

  .search-form {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .search-select {
    order: 2;
  }

  .search-input {
    order: 1;
  }

  .search-button {
    order: 3;
  }
}

/* Tablet */
@media (min-width: 481px) and (max-width: 768px) {
  .container {
    padding: 0 var(--tablet-padding);
  }

  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }

  .hero-title {
    font-size: var(--font-size-4xl);
  }
}

/* Desktop */
@media (min-width: 769px) {
  .container {
    padding: 0 var(--desktop-padding);
  }

  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  }

  .hero {
    padding: var(--spacing-16) 0;
  }

  .hero-title {
    font-size: var(--font-size-5xl);
  }
}

/* Large Desktop */
@media (min-width: 1024px) {
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: var(--spacing-6);
  }
}

/* === PRINT STYLES === */

@media print {
  .site-header,
  .main-nav,
  .search-form,
  .tab-buttons,
  .btn {
    display: none !important;
  }

  .hero {
    padding: var(--spacing-4) 0;
  }

  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-2);
  }

  .media-card {
    break-inside: avoid;
    border: 1px solid #000;
  }

  body {
    background: white !important;
    color: black !important;
  }
}

/* === ACCESSIBILITY IMPROVEMENTS === */

/* Focus management */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: var(--radius);
  z-index: var(--z-max);
}

.skip-link:focus {
  top: 6px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .media-card,
  .card,
  .btn {
    border: 2px solid currentColor;
  }

  .media-card:hover,
  .card:hover {
    border-color: var(--primary);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .media-card:hover .media-poster img {
    transform: none;
  }

  .media-card:hover {
    transform: none;
  }
}

/* === PERFORMANCE OPTIMIZATIONS === */

/* GPU acceleration for animations */
.media-card,
.btn,
.tab-button,
.toggle-button {
  will-change: transform;
}

/* Contain layout shifts */
.media-poster {
  contain: layout style paint;
}

.skeleton {
  contain: layout style paint;
}

/* === EPISODES STYLING === */

.episodes-container {
  margin: var(--spacing-6) 0;
}

.episodes-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-4);
}

.tab-buttons-wrapper {
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--border) transparent;
}

.tab-buttons-wrapper::-webkit-scrollbar {
  height: 4px;
}

.tab-buttons-wrapper::-webkit-scrollbar-track {
  background: transparent;
}

.tab-buttons-wrapper::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: var(--radius-full);
}

.episodes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
}

.episode-button {
  background-color: var(--bg-card);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: var(--spacing-3);
  text-align: center;
  transition: all var(--transition) var(--ease-in-out);
  position: relative;
  overflow: hidden;
}

.episode-button:hover {
  background-color: var(--bg-card-hover);
  border-color: var(--primary);
  transform: translateY(-1px);
}

.episode-button.active {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
}

.episode-button a {
  color: inherit;
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.vod-speed {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--accent);
}

.speed-btn {
  background-color: var(--bg-card);
  border: 1px solid var(--border);
  color: var(--text-primary);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius);
  cursor: pointer;
  transition: all var(--transition) var(--ease-in-out);
}

.speed-btn:hover:not(:disabled) {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
}

.speed-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.neon-flash {
  animation: neonFlash 2s infinite;
}

@keyframes neonFlash {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* === DARK MODE ENHANCEMENTS === */

@media (prefers-color-scheme: light) {
  .hero {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  }

  .hero-title {
    background: linear-gradient(135deg, #1f2937 0%, #2563eb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}
