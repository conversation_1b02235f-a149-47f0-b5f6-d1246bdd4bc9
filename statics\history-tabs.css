/* History Tabs Styles */

/* History Tabs Container */
.history-tabs {
  display: flex;
  width: 100%;
  border-bottom: 1px solid var(--border-light, #4b5563);
  margin-bottom: 0;
  padding: 0 10px;
  flex-shrink: 0; /* Prevent the tabs from shrinking */
  background-color: var(--bg-dark, #111827); /* Match the background color */
  position: relative; /* For proper stacking */
  z-index: 5; /* Ensure tabs appear above content */
}

/* History Tab Button */
.history-tab {
  padding: 8px 16px;
  background: none;
  border: none;
  color: var(--text-secondary, #e5e7eb);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  position: relative;
  transition: color 0.2s ease;
  flex: 1;
  text-align: center;
}

.history-tab:hover {
  color: var(--text-primary, #f9fafb);
}

.history-tab.active {
  color: var(--primary, #2563eb);
  font-weight: 600;
}

.history-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary, #2563eb);
}

/* History Tab Content */
.history-tab-content {
  display: none;
  padding: 0;
  width: 100%;
  height: auto;
  overflow: hidden;
  opacity: 0;
  transition: opacity 0.2s ease;
  max-height: 0;
  position: relative; /* For proper positioning of children */
}

.history-tab-content.active {
  display: flex;
  flex-direction: column;
  opacity: 1;
  max-height: none;
  overflow: hidden; /* Keep hidden to prevent double scrolling */
  flex: 1; /* Take up available space */
  height: 100%; /* Ensure it takes full height */
}

/* History header, title, and close button styles moved to shared-containers.css for standardization */

/* History footer styles moved to shared-containers.css for standardization */

/* Standardized clear button style - used for both history and favorites */
.clear-history-button, .clear-favorites-button {
  background-color: var(--primary, #3b82f6);
  color: white;
  border: none;
  border-radius: var(--radius-md, 4px);
  padding: 6px 12px; /* Smaller padding for header placement */
  font-size: 14px; /* Smaller font for header placement */
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* Lighter shadow for header placement */
  white-space: nowrap; /* Prevent text wrapping */
  height: 32px; /* Smaller height for header placement */
  min-width: 70px; /* Smaller width for header placement */
  max-width: 100px; /* Smaller max width for header placement */
  margin: 0; /* No margin needed in header */
  font-weight: 500; /* Slightly bolder text */
}

.clear-history-button:hover, .clear-favorites-button:hover {
  background-color: var(--primary-dark, #2563eb);
  color: white;
  transform: translateY(-2px); /* Slight lift effect on hover */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* Enhanced shadow on hover */
}

.clear-history-button:active, .clear-favorites-button:active {
  transform: translateY(0); /* Reset on active/press */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); /* Reduced shadow on press */
}

.clear-history-button-icon, .clear-favorites-button-icon {
  margin-right: 6px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive Adjustments */
@media (max-width: 480px) {
  .history-tab {
    padding: 10px 12px;
    font-size: 14px;
    font-weight: 600;
  }

  .history-tab.active {
    font-weight: 700;
  }

  .history-tab.active::after {
    height: 3px; /* Make the active indicator more visible */
  }

  /* Mobile history title styles moved to shared-containers.css for standardization */

  /* Standardized clear button style for mobile */
  .clear-history-button, .clear-favorites-button {
    width: auto; /* Let the button size to its content */
    min-width: 60px; /* Smaller width for header placement */
    max-width: 80px; /* Smaller max width for header placement */
    padding: 6px 10px; /* Smaller padding for header placement */
    font-size: 14px; /* Smaller font for header placement */
    justify-content: center;
    margin: 0; /* No margin needed in header */
    border-radius: 6px; /* Slightly rounded corners */
    white-space: nowrap; /* Prevent text wrapping */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* Lighter shadow for header placement */
    height: 32px; /* Smaller height for header placement */
    font-weight: 500; /* Slightly bolder text */
    display: flex !important; /* Ensure it's displayed */
    align-items: center !important;
  }

  /* Mobile history list styles moved to shared-containers.css for standardization */

  /* Ensure tab content is visible and properly structured */
  .history-tab-content.active {
    display: flex !important;
    flex-direction: column !important;
    opacity: 1 !important;
    max-height: none !important;
    overflow: hidden !important; /* Keep hidden to prevent double scrolling */
    flex: 1 !important; /* Take up available space */
    position: relative !important; /* For proper positioning of children */
    height: 100% !important; /* Ensure it takes full height */
  }
}
