/* Shared Container Styles for History and Favorites */

/* Common dropdown content styles */
.history-content,
.favorites-content {
  position: fixed;
  top: 60px;
  right: 20px;
  width: 320px; /* Standardized width */
  max-width: 90vw;
  max-height: 80vh; /* Standardized max-height */
  background-color: var(--bg-card, #222);
  border-radius: var(--radius-lg, 0.5em);
  box-shadow: var(--shadow-lg, 0 4px 12px rgba(0, 0, 0, 0.2), 0 0 0 1px #333);
  z-index: 100;
  overflow: hidden; /* Prevent the container from scrolling */
  display: flex;
  flex-direction: column;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition, 0.3s) var(--transition-ease, ease);
}

/* Common show state */
.history-content.show,
.favorites-content.show,
.history-content.history-content-show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  z-index: 100;
}

/* Common header styles */
.history-header,
.favorites-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border, #333);
  background-color: var(--bg-card, #222);
}

/* Header actions container */
.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Common title styles */
.history-title,
.favorites-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #f9fafb);
}

/* Common close button styles */
.history-close,
.favorites-close {
  font-size: 22px;
  color: var(--text-secondary, #9ca3af);
  cursor: pointer;
  padding: 6px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition, 0.2s) ease;
  width: 36px;
  height: 36px;
  background-color: transparent;
  line-height: 1;
}

.history-close:hover,
.favorites-close:hover {
  color: var(--text-primary, #f9fafb);
  background-color: rgba(0, 0, 0, 0.05);
}

/* Common body styles */
.history-body,
.favorites-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 8px 0;
  height: 100%; /* Ensure it takes full height */
}

/* Common list styles */
.history-list,
.favorites-list,
.viewing-list,
.search-list {
  list-style: none;
  margin: 0;
  padding: 0;
  max-height: 60vh; /* Standardized height for desktop */
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%; /* Ensure full width */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

/* Common footer styles */
.history-footer,
.favorites-footer {
  padding: 12px 16px;
  border-top: 1px solid var(--border, #333);
  text-align: center;
  display: flex;
  justify-content: center; /* Center by default, favorites will override */
}

/* Mobile styles */
@media (max-width: 768px) {
  .history-content,
  .favorites-content {
    position: fixed !important;
    top: 0 !important; /* Start from the top of the screen */
    right: 0 !important;
    bottom: 0 !important; /* Extend to the bottom of the screen */
    left: 0 !important;
    width: 100% !important;
    max-width: none !important;
    height: 100% !important; /* Full height of the viewport */
    min-height: 100vh !important; /* Ensure minimum height covers viewport */
    max-height: none !important; /* Remove max-height limitation */
    border-radius: 0 !important; /* Remove border radius for full-screen effect */
    z-index: 1000 !important;
    display: flex !important;
    flex-direction: column !important;
    overflow: hidden !important; /* Important: prevent the container from scrolling */
    overflow-y: hidden !important; /* Explicitly prevent vertical scrolling */
    overflow-x: hidden !important; /* Explicitly prevent horizontal scrolling */
    transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
    margin: 0 !important;
    will-change: transform !important;
    backface-visibility: hidden !important;
    -webkit-backface-visibility: hidden !important;
    transform-style: preserve-3d !important;
    -webkit-transform-style: preserve-3d !important;
    transform: translateY(100vh) translateZ(0) !important; /* Use 100vh to ensure it's completely off-screen */
    -webkit-transform: translateY(100vh) translateZ(0) !important;
    background-color: var(--bg-dark, #111827) !important; /* Ensure background is opaque */
    padding-bottom: 60px !important; /* Add padding at the bottom to account for the navigation bar */
  }

  .history-content.show,
  .favorites-content.show,
  .history-content.history-content-show {
    transform: translateY(0) translateZ(0) !important;
    -webkit-transform: translateY(0) translateZ(0) !important;
    visibility: visible !important;
    opacity: 1 !important;
    background-color: var(--bg-dark, #111827) !important; /* Ensure background is opaque */
    height: 100% !important; /* Full height of the viewport */
    min-height: 100vh !important; /* Ensure minimum height covers viewport */
    display: flex !important; /* Ensure flex display */
    flex-direction: column !important; /* Stack children vertically */
    overflow: hidden !important; /* Important: prevent the container from scrolling */
    overflow-y: hidden !important; /* Explicitly prevent vertical scrolling */
    overflow-x: hidden !important; /* Explicitly prevent horizontal scrolling */
    padding-bottom: 60px !important; /* Add padding at the bottom to account for the navigation bar */
  }

  /* Add overlay to prevent seeing content underneath */
  .history-content::before,
  .favorites-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-dark, #111827);
    z-index: -1;
  }

  .history-header,
  .favorites-header {
    padding: 16px;
    background-color: var(--bg-card, #222);
    border-bottom: 1px solid var(--border, #333);
    flex-shrink: 0; /* Prevent the header from shrinking */
    min-height: 70px; /* Ensure minimum height */
  }

  /* Header actions container for mobile */
  .header-actions {
    display: flex;
    align-items: center;
    gap: 12px; /* Increased gap for mobile */
  }

  .history-title,
  .favorites-title {
    font-size: 20px;
    font-weight: 700;
  }

  .history-close,
  .favorites-close {
    font-size: 24px;
    width: 40px;
    height: 40px;
  }

  .history-body,
  .favorites-body,
  .history-tab-content,
  .favorites-tab-content {
    padding: 0;
    overflow: hidden !important; /* Prevent scrolling at this level */
    -webkit-overflow-scrolling: touch;
    flex: 1 1 auto !important; /* Allow it to grow and shrink as needed */
    height: calc(100% - 50px) !important; /* Take full height minus footer height */
    min-height: 0 !important; /* Allow it to shrink below its content size if needed */
    display: flex !important; /* Use flexbox for better control */
    flex-direction: column !important;
    position: relative !important; /* For proper positioning of children */
    padding-bottom: 0 !important; /* Removed padding-bottom */
  }

  .history-list,
  .favorites-list,
  .viewing-list,
  .search-list {
    overflow-y: auto !important; /* This is the only element that should scroll */
    overflow-x: hidden !important;
    flex: 1 1 auto !important; /* Allow the list to grow and shrink as needed */
    min-height: 0 !important; /* Allow it to shrink below its content size if needed */
    height: 100% !important; /* Take full height of the container */
    margin: 0 !important;
    padding: 0 !important; /* Remove padding to allow full scrolling */
    padding-bottom: 180px !important; /* Increased bottom padding to ensure clear button is visible */
    max-height: none !important; /* Remove max-height limitation */
    width: 100% !important; /* Ensure full width */
    position: relative !important; /* Changed from absolute to relative */
    display: block !important; /* Ensure the list is displayed as a block */
    -webkit-overflow-scrolling: touch !important; /* Smooth scrolling on iOS */
    overscroll-behavior: contain !important; /* Prevent scroll chaining */
    touch-action: pan-y !important; /* Allow only vertical touch actions */
  }

  .history-footer,
  .favorites-footer,
  .viewing-footer,
  .search-footer {
    padding: 12px 16px; /* Increased vertical padding */
    position: fixed; /* Fixed position to ensure it stays at the bottom */
    bottom: 80px; /* Positioned higher above the bottom navigation bar */
    left: 0;
    right: 0;
    background-color: var(--bg-dark, #111827); /* Match the background color */
    z-index: 1001; /* Higher z-index to ensure it stays above the content */
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2); /* Add shadow for separation */
    display: flex;
    justify-content: center; /* Center the button for consistency */
    width: 100%;
    flex-shrink: 0; /* Prevent the footer from shrinking */
    min-height: auto; /* Let it size naturally */
    border-top: 1px solid var(--border-light, rgba(75, 85, 99, 0.5)); /* Consistent border */
    height: 60px; /* Increased height for better spacing */
    pointer-events: auto; /* Ensure it captures clicks */
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .history-content,
  .favorites-content {
    bottom: 0 !important; /* Extend to the bottom of the screen */
    height: 100% !important; /* Full height of the viewport */
    min-height: 100vh !important; /* Ensure minimum height covers viewport */
    overflow: hidden !important; /* Important: prevent the container from scrolling */
    overflow-y: hidden !important; /* Explicitly prevent vertical scrolling */
    overflow-x: hidden !important; /* Explicitly prevent horizontal scrolling */
    padding-bottom: 56px !important; /* Add padding at the bottom to account for the navigation bar on small devices */
  }

  .history-content.show,
  .favorites-content.show,
  .history-content.history-content-show {
    height: 100% !important; /* Full height of the viewport */
    min-height: 100vh !important; /* Ensure minimum height covers viewport */
    overflow: hidden !important; /* Important: prevent the container from scrolling */
    overflow-y: hidden !important; /* Explicitly prevent vertical scrolling */
    overflow-x: hidden !important; /* Explicitly prevent horizontal scrolling */
    padding-bottom: 56px !important; /* Add padding at the bottom to account for the navigation bar on small devices */
  }

  .history-header,
  .favorites-header {
    min-height: 60px; /* Smaller header for small devices */
    padding: 12px 16px;
  }

  /* Header actions container for small mobile */
  .header-actions {
    gap: 10px; /* Slightly smaller gap for small devices */
  }

  .history-footer,
  .favorites-footer,
  .viewing-footer,
  .search-footer {
    min-height: auto; /* Let it size naturally */
    padding: 12px 12px; /* Increased vertical padding */
    justify-content: center; /* Center the button for consistency */
    position: fixed; /* Fixed position to ensure it stays at the bottom */
    bottom: 76px; /* Positioned higher above the bottom navigation bar for small devices */
    left: 0;
    right: 0;
    background-color: var(--bg-dark, #111827); /* Match the background color */
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2); /* Add shadow for separation */
    border-top: 1px solid var(--border-light, rgba(75, 85, 99, 0.5)); /* Consistent border */
    height: 60px; /* Increased height for better spacing */
    z-index: 1001; /* Higher z-index to ensure it stays above the content */
    pointer-events: auto; /* Ensure it captures clicks */
  }

  /* Adjust body height for small devices */
  .history-body,
  .favorites-body,
  .history-tab-content,
  .favorites-tab-content {
    padding-bottom: 0 !important; /* No padding needed */
    height: calc(100% - 60px) !important; /* Take full height minus header height */
    overflow: hidden !important; /* Prevent scrolling at this level */
    display: flex !important;
    flex-direction: column !important;
  }

  /* Adjust button styles for small devices */
  .clear-history-button,
  .clear-favorites-button {
    margin: 0 12px 0 0; /* Smaller right margin */
    padding: 4px 10px; /* Smaller padding */
    font-size: 14px; /* Smaller font size */
    height: 30px; /* Fixed height */
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Adjust button icon for header placement */
  .clear-history-button-icon,
  .clear-favorites-button-icon {
    font-size: 16px;
    margin-right: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
