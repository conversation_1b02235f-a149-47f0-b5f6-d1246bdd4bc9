/* Skeleton Loader Styles */

/* Base skeleton animation - optimized for performance */
@keyframes skeleton-loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Skeleton base style - using transform for better performance */
.skeleton-loader {
  background-color: #2a2f3a;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  will-change: transform; /* Hint for browser optimization */
}

.skeleton-loader::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent);
  transform: translateX(-100%);
  animation: skeleton-loading 1.5s infinite;
}

/* Card skeleton */
.card-skeleton {
  width: 100%;
  height: 0;
  padding-bottom: 150%; /* Maintain aspect ratio */
  margin-bottom: 8px;
  border-radius: 8px;
}

/* Title skeleton */
.title-skeleton {
  height: 16px;
  width: 90%;
  margin-bottom: 8px;
}

/* Subtitle skeleton */
.subtitle-skeleton {
  height: 12px;
  width: 60%;
}

/* Card grid skeleton */
.card-grid-skeleton {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: var(--spacing-6);
  margin-top: var(--spacing-8);
}

/* Player skeleton */
.player-skeleton {
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  margin-bottom: 16px;
  border-radius: 4px;
}

/* History item skeleton */
.history-item-skeleton {
  display: flex;
  width: 100%;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 4px;
  align-items: flex-start;
}

.history-item-skeleton-poster {
  width: 60px;
  height: 90px;
  border-radius: 4px;
  flex-shrink: 0;
}

.history-item-skeleton-content {
  flex: 1;
  margin-left: 12px;
}

.history-item-skeleton-title {
  height: 16px;
  width: 80%;
  margin-bottom: 8px;
}

.history-item-skeleton-episode {
  height: 12px;
  width: 50%;
  margin-bottom: 8px;
}

.history-item-skeleton-progress {
  height: 8px;
  width: 100%;
  border-radius: 4px;
}

/* Progressive image loading - optimized for performance */
.progressive-image-container {
  position: relative;
  overflow: hidden;
  background-color: #2a2f3a;
  will-change: contents; /* Hint for browser optimization */
}

.progressive-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  filter: blur(8px); /* Reduced blur for better performance */
  transform: scale(1.05); /* Reduced scale for better performance */
  transition: opacity 0.3s ease;
  will-change: opacity; /* Hint for browser optimization */
}

.progressive-image {
  position: relative;
  opacity: 0;
  transition: opacity 0.3s ease;
  will-change: opacity; /* Hint for browser optimization */
  backface-visibility: hidden; /* Prevent flickering in some browsers */
  transform: translateZ(0); /* Force GPU acceleration */
}

.progressive-image.loaded {
  opacity: 1;
}

.progressive-image.loaded + .progressive-image-placeholder {
  opacity: 0;
}

/* Add support for reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .progressive-image-placeholder {
    filter: blur(5px);
    transform: none;
    transition: opacity 0.1s ease;
  }

  .progressive-image {
    transition: opacity 0.1s ease;
  }

  .skeleton-loader::after {
    animation-duration: 2s;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card-grid-skeleton {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: var(--spacing-4);
  }
}

@media (max-width: 480px) {
  .card-grid-skeleton {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--spacing-3);
  }

  .history-item-skeleton-poster {
    width: 50px;
    height: 75px;
  }
}
