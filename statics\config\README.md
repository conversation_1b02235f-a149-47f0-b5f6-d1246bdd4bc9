# Configuration Directory

This directory contains configuration files for the application.

## Files

- `config.js`: Main configuration file containing site settings, Firebase configuration, and category definitions
- `index.js`: Entry point for all configuration files, making it easier to add more configuration files in the future

## Usage

The configuration is loaded in `base.html` with:

```html
<script src="/statics/config/config.js"></script>
```

## Configuration Options

### Site Title

```javascript
siteTitle: "影视"
```

### Firebase Authentication

```javascript
auth: {
    // Enable or disable Firebase authentication features (login/register)
    enabled: true,
    
    // Firebase configuration
    firebase: {
        apiKey: "...",
        authDomain: "...",
        projectId: "...",
        storageBucket: "...",
        messagingSenderId: "...",
        appId: "..."
    }
}
```

### Categories

```javascript
categories: [
    {
        title: "动漫",
        path: "/vod/list/?kw=动漫",
        subcategories: [
            { title: "全部动漫", path: "/vod/list/?kw=动漫" },
            { title: "国产动漫", path: "/vod/list/?kw=国产动漫" },
            // ...
        ]
    },
    // ...
]
```

## Adding New Configuration Files

To add a new configuration file:

1. Create a new file in this directory (e.g., `theme-config.js`)
2. Add your configuration code to the file
3. Import the file in `index.js`:

```javascript
import './theme-config.js';
```

4. Update this README.md to document the new configuration options
