/**
 * Enhanced Unified Utility Library
 * Consolidated utility functions from across the application
 * for better maintainability and performance
 */

// Core Utils Namespace
const Utils = {
  // Environment detection with memoization for better performance
  env: (() => {
    // Cache detection results
    const cache = {};
    
    return {
      /**
       * Check if we're in a development environment
       * @returns {boolean}
       */
      isDev: function() {
        if (typeof cache.isDev !== 'undefined') return cache.isDev;
        
        cache.isDev = window.location.hostname === 'localhost' || 
                     window.location.hostname === '127.0.0.1' ||
                     window.location.hostname.includes('.local');
        return cache.isDev;
      },
      
      /**
       * Check if the device is a mobile device
       * @returns {boolean}
       */
      isMobile: function() {
        if (typeof cache.isMobile !== 'undefined') return cache.isMobile;
        
        cache.isMobile = window.innerWidth <= 768 ||
                        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        return cache.isMobile;
      },
      
      /**
       * Check if the device is a low-end device
       * @returns {boolean}
       */
      isLowEndDevice: function() {
        if (typeof cache.isLowEnd !== 'undefined') return cache.isLowEnd;
        
        cache.isLowEnd = (
          (navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2) ||
          /Android [4-6]/.test(navigator.userAgent) ||
          /Mobile.*Firefox/.test(navigator.userAgent) ||
          (navigator.deviceMemory && navigator.deviceMemory <= 2)
        );
        return cache.isLowEnd;
      },
      
      /**
       * Check if reduced motion is preferred
       * @returns {boolean}
       */
      prefersReducedMotion: function() {
        if (typeof cache.reducedMotion !== 'undefined') return cache.reducedMotion;
        
        cache.reducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        return cache.reducedMotion;
      },
      
      /**
       * Check if dark mode is preferred
       * @returns {boolean}
       */
      prefersDarkMode: function() {
        if (typeof cache.darkMode !== 'undefined') return cache.darkMode;
        
        cache.darkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
        return cache.darkMode;
      }
    };
  })(),
  
  // Logging with environment awareness
  logger: {
    log: function(...args) {
      if (Utils.env.isDev()) console.log(...args);
    },
    error: function(...args) {
      if (Utils.env.isDev()) console.error(...args);
    },
    warn: function(...args) {
      if (Utils.env.isDev()) console.warn(...args);
    },
    info: function(...args) {
      if (Utils.env.isDev()) console.info(...args);
    }
  },
  
  // DOM Utilities
  dom: {
    /**
     * Create an element with attributes and children
     * @param {string} tag - The tag name
     * @param {Object} attrs - The attributes
     * @param {Array|string} children - The children
     * @returns {HTMLElement} The created element
     */
    createElement: function(tag, attrs = {}, children = []) {
      const element = document.createElement(tag);
  
      // Add attributes
      Object.entries(attrs).forEach(([key, value]) => {
        if (key === 'className') {
          element.className = value;
        } else if (key === 'style' && typeof value === 'object') {
          Object.entries(value).forEach(([prop, val]) => {
            element.style[prop] = val;
          });
        } else if (key.startsWith('on') && typeof value === 'function') {
          element.addEventListener(key.substring(2).toLowerCase(), value);
        } else {
          element.setAttribute(key, value);
        }
      });
  
      // Add children
      if (typeof children === 'string') {
        element.textContent = children;
      } else if (Array.isArray(children)) {
        children.forEach(child => {
          if (typeof child === 'string') {
            element.appendChild(document.createTextNode(child));
          } else if (child instanceof Node) {
            element.appendChild(child);
          }
        });
      }
  
      return element;
    },
  
    /**
     * Create HTML from a template string (safer than innerHTML)
     * @param {string} html - The HTML string
     * @returns {DocumentFragment} The document fragment
     */
    createHTML: function(html) {
      const template = document.createElement('template');
      template.innerHTML = html.trim();
      return template.content.cloneNode(true);
    },
  
    /**
     * Empty an element
     * @param {HTMLElement} element - The element to empty
     */
    emptyElement: function(element) {
      while (element.firstChild) {
        element.removeChild(element.firstChild);
      }
    },
    
    /**
     * Find element by selector with error handling
     * @param {string} selector - CSS selector
     * @param {HTMLElement} parent - Parent element (optional)
     * @returns {HTMLElement|null} The found element or null
     */
    find: function(selector, parent = document) {
      try {
        return parent.querySelector(selector);
      } catch (e) {
        Utils.logger.error('Invalid selector:', selector, e);
        return null;
      }
    },
    
    /**
     * Find all elements by selector with error handling
     * @param {string} selector - CSS selector
     * @param {HTMLElement} parent - Parent element (optional)
     * @returns {NodeList|Array} The found elements or empty array
     */
    findAll: function(selector, parent = document) {
      try {
        return parent.querySelectorAll(selector);
      } catch (e) {
        Utils.logger.error('Invalid selector:', selector, e);
        return [];
      }
    }
  },
  
  // Performance Utilities
  perf: {
    /**
     * Debounce a function
     * @param {Function} func - The function to debounce
     * @param {number} wait - The debounce wait time in milliseconds
     * @returns {Function} The debounced function
     */
    debounce: function(func, wait = 200) {
      let timeout;
      return function(...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), wait);
      };
    },
  
    /**
     * Throttle a function
     * @param {Function} func - The function to throttle
     * @param {number} wait - The throttle wait time in milliseconds
     * @returns {Function} The throttled function
     */
    throttle: function(func, wait = 200) {
      let timeout = null;
      let previous = 0;
      
      return function(...args) {
        const context = this;
        const now = Date.now();
        const remaining = wait - (now - previous);
        
        if (remaining <= 0 || remaining > wait) {
          if (timeout) {
            clearTimeout(timeout);
            timeout = null;
          }
          previous = now;
          func.apply(context, args);
        } else if (!timeout) {
          timeout = setTimeout(() => {
            previous = Date.now();
            timeout = null;
            func.apply(context, args);
          }, remaining);
        }
      };
    },
    
    /**
     * Optimize animations based on device capabilities
     */
    optimizeAnimations: function() {
      if (Utils.env.prefersReducedMotion()) {
        document.body.classList.add('reduced-motion');
        Utils.dom.findAll('.site-header').forEach(el => {
          el.classList.add('reduced-motion');
        });
      }
  
      if (Utils.env.isLowEndDevice()) {
        document.body.classList.add('low-end-device');
  
        // Disable complex animations
        Utils.dom.findAll('.skeleton-loader, .progressive-image-container').forEach(el => {
          el.classList.add('simplified-animation');
        });
      }
    },
    
    /**
     * Optimize image loading
     */
    optimizeImageLoading: function() {
      // Use native image lazy loading where supported
      if ('loading' in HTMLImageElement.prototype) {
        Utils.dom.findAll('img:not([loading])').forEach(img => {
          if (!img.hasAttribute('loading') && !img.classList.contains('progressive-image')) {
            img.loading = 'lazy';
          }
        });
      }
  
      // Set attributes for optimal image loading
      Utils.dom.findAll('img').forEach(img => {
        // Add fetchpriority to important images
        if (img.closest('.hero, .featured')) {
          img.fetchPriority = 'high';
        }
  
        // Add decoding attribute to images
        if (!img.hasAttribute('decoding')) {
          img.decoding = 'async';
        }
  
        // Add explicit width and height to prevent layout shifts
        if (!img.hasAttribute('width') && !img.hasAttribute('height') && img.naturalWidth && img.naturalHeight) {
          const aspectRatio = img.naturalWidth / img.naturalHeight;
          
          if (img.style.width) {
            const width = parseInt(img.style.width);
            if (!isNaN(width)) {
              img.height = Math.round(width / aspectRatio);
            }
          } else if (img.style.height) {
            const height = parseInt(img.style.height);
            if (!isNaN(height)) {
              img.width = Math.round(height * aspectRatio);
            }
          }
        }
      });
    }
  },
  
  // Storage Utilities
  storage: {
    /**
     * Set an item in localStorage with error handling
     * @param {string} key - The key
     * @param {*} value - The value to store
     * @returns {boolean} Whether the operation was successful
     */
    setItem: function(key, value) {
      try {
        const serialized = JSON.stringify(value);
        localStorage.setItem(key, serialized);
        return true;
      } catch (e) {
        Utils.logger.error('localStorage setItem failed:', e);
        return false;
      }
    },
    
    /**
     * Get an item from localStorage with error handling
     * @param {string} key - The key
     * @param {*} defaultValue - Default value if not found or error
     * @returns {*} The retrieved value or defaultValue
     */
    getItem: function(key, defaultValue = null) {
      try {
        const serialized = localStorage.getItem(key);
        if (serialized === null) return defaultValue;
        return JSON.parse(serialized);
      } catch (e) {
        Utils.logger.error('localStorage getItem failed:', e);
        return defaultValue;
      }
    },
    
    /**
     * Remove an item from localStorage with error handling
     * @param {string} key - The key
     * @returns {boolean} Whether the operation was successful
     */
    removeItem: function(key) {
      try {
        localStorage.removeItem(key);
        return true;
      } catch (e) {
        Utils.logger.error('localStorage removeItem failed:', e);
        return false;
      }
    }
  }
};

// Make Utils available globally
window.Utils = Utils;

// Initialize optimizations when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Apply performance optimizations
  Utils.perf.optimizeAnimations();
  Utils.perf.optimizeImageLoading();
});
